import { create<PERSON><PERSON><PERSON><PERSON><PERSON>er, Navigate, Outlet } from "react-router-dom";
import SetPassword from "../common-components/login/login-pages//set-password";
import EnterOtpOne from "../common-components/login/login-pages/enter-otp1";
import ForgotPassword from "../common-components/login/login-pages/forgot-password";
import Login from "../common-components/login/login-pages/login";
import AuthLayout from "../layouts/auth-layout";
import MainLayout from "../layouts/main-layout";
import {
  ENTER_OTP,
  FORGOT_PASSWORD,
  LOGIN,
  NEW_LOGIN,
  SET_PASSWORD,
} from "../models/routesConstant";
import Availability from "../pages/apps/provider-portal/availability/availability";
import FeeSchedule from "../pages/apps/provider-portal/other-setting/fee-schedule/fee-schedule";
import OtherSettingsMasterTabs from "../pages/apps/provider-portal/other-setting/other-settings-master-tabs";
import AllSettingsPage from "../pages/apps/provider-portal/settings/all-settings-page";
import Contact from "../pages/apps/provider-portal/settings/contacts/contact";
import Location from "../pages/apps/provider-portal/settings/location/location";
import ProfileTabs from "../pages/apps/provider-portal/settings/profile-settings-tabs";
import Profile from "../pages/apps/provider-portal/settings/profile/profile";
import Roles from "../pages/apps/provider-portal/settings/roles/roles-settings";
import Clinician from "../pages/apps/provider-portal/settings/Users/<USER>";
import Patients from "../pages/apps/provider-portal/patients/patients-tab/patients";
import AddPatients from "../pages/apps/provider-portal/patients/patients-tab/add-patients";
import ViewAppointments from "../pages/apps/provider-portal/calendar/view-appointments";
import PrivateRoute from "./private-route";
import CustomFormBuilder from "../pages/apps/provider-portal/form-builder/form-builder";
import PatientProfile from "../pages/apps/provider-portal/patients/patients-profile/patients-profile";
import PatientProfileNext from "../pages/apps/provider-portal/patients/patients-profile/patient-profile-next";
import UserProfile from "../pages/apps/provider-portal/profile/user-profile";
import NewLogin from "../common-components/login/login-pages/new-login";
import GroupSettings from "../pages/apps/provider-portal/other-setting/group-settings/group-settings";
import PatientInsurance from "../pages/apps/provider-portal/patients/patients-profile/Insurance/patient-insurance";
import InsurancePlan from "../pages/apps/provider-portal/patients/patients-profile/Insurance/insurance-plan";

export const router = createBrowserRouter([
  { path: "", element: <Navigate to={"/auth/login"} /> },
  {
    path: "/auth",
    element: (
      // <PublicRoute>
      <AuthLayout>
        <Outlet />
      </AuthLayout>
      // </PublicRoute>
    ),
    children: [
      { path: NEW_LOGIN, element: <NewLogin /> },
      { path: LOGIN, element: <Login /> },
      { path: FORGOT_PASSWORD, element: <ForgotPassword /> },
      { path: ENTER_OTP, element: <EnterOtpOne /> },
      { path: SET_PASSWORD, element: <SetPassword /> },
    ],
  },
  {
    path: "/admin",
    element: (
      <PrivateRoute>
        <MainLayout>
          <Outlet />
        </MainLayout>
      </PrivateRoute>
    ),
    children: [
      {
        path: "patients",
        element: <Outlet />,
        children: [
          {
            path: "",
            element: <Patients />,
          },
          {
            path: "patient-profile",
            element: <PatientProfile />,
            children: [
              {
                path: "",
                element: <PatientProfileNext />,
              },
            ],
          },

          {
            path: "insurance",
            element: <PatientInsurance />,
            children: [
              {
                path: "",
                element: <InsurancePlan />,
              },
            ],
          },
        ],
      },
      {
        path: "user-profile",
        element: <UserProfile />,
      },
      {
        path: "calendar",
        element: <ViewAppointments />,
      },
      {
        path: "patients/add-patient",
        element: <AddPatients />,
      },
      {
        path: "patients/edit-patient/:uuid",
        element: <AddPatients />,
      },
      {
        path: "forms",
        element: <CustomFormBuilder />,
      },
      {
        path: "calendar",
        element: <ViewAppointments />,
      },
      {
        path: "settings-tabs",
        element: <Outlet />,
        children: [
          { path: "", element: <AllSettingsPage /> },
          {
            path: "other-settings",
            element: <OtherSettingsMasterTabs />,
            children: [
              { path: "availability", element: <Availability /> },
              { path: "fee-schedule", element: <FeeSchedule /> },
              { path: "forms", element: <CustomFormBuilder /> },
              { path: "group-settings", element: <GroupSettings /> },
            ],
          },
          {
            path: "profile-tabs",
            element: <ProfileTabs />,
            children: [
              { path: "", element: <Profile /> },
              { path: "profile", element: <Profile /> },
              { path: "location", element: <Location /> },
              { path: "user", element: <Clinician /> },
              { path: "roles", element: <Roles /> },
              { path: "contact", element: <Contact /> },
            ],
          },
        ],
      },
    ],
  },
]);
