export type StatusEnum =
  | "DUPLICATE"
  | "NO_BILL_YET"
  | "NOT_SUBMITTED"
  | "CANCEL"
  | "WAITING_ROOM"
  | "SYSTEM_ERROR"
  | "FAILED"
  | "HIGH"
  | "COMPLETE"
  | "PAID"
  | "SUCCESS"
  | "SUBMITTED"
  | "CHECKED_IN"
  | "RECEIVED"
  | "PENDING"
  | "IN_PROGRESS"
  | "HOLD"
  | "RESCHEDULE"
  | "CHECKED_OUT"
  | "UPCOMING"
  | "STANDARD"
  | "REJECTED"
  | "NORMAL"
  | "BELOW_NORMAL"
  | "OVERWEIGHT"
  | "ACTIVE"
  | "IN_ACTIVE"
  | "IN_EXAM"
  | "CONFIRMED"
  | "CANCELLED"
  | "COMPLETED"
  | "INITIATED"
  | "DECLINED"
  | "CHARGEBACK"
  | "EXPIRED"
  | "UNPAID"
  | "ARCHIVE"
  | "APPROVED_WITH_CONDITION"
  | "APPROVED"
  | "CANCELLED"
  | "PENDING_FROM_PROVIDER"
  | "NO_SHOW"
  | "NO_SHOW_FROM_PROVIDER"
  | "NO_SHOW_FROM_PATIENT"
  | "ABLE_TO_CHECK_IN"
  | "CONSENT_PENDING"
  | "ID_PROOF_PENDING"
  | "SCREENERS_PENDING"
  | "VERIFIED"
  | "NOT_VERIFIED"
  | "PENDING_FROM_PROVIDER"
  | "NO_SHOW"
  | "NO_SHOW_FROM_PROVIDER"
  | "NO_SHOW_FROM_PATIENT"
  | "ABLE_TO_CHECK_IN"
  | "CONSENT_PENDING"
  | "ID_PROOF_PENDING"
  | "SCREENERS_PENDING"
  | "DISCHARGED"
  | "DISMISSED"
  | "SIGNED_OFF"
  | "NOT_AVAILABLE"
  | "NEW"
  | "INVITED";

export const statusLabels = {
  DUPLICATE: "Duplicate",
  CONSENT_PENDING: "Consent pending",
  ID_PROOF_PENDING: "Id proof pending",
  NO_BILL_YET: "No Bill Yet",
  NOT_SUBMITTED: "Not Submitted",
  CANCEL: "Cancel",
  WAITING_ROOM: "Waiting Room",
  SYSTEM_ERROR: "System Error",
  FAILED: "Failed",
  HIGH: "High",
  COMPLETE: "Completed",
  PAID: "Paid",
  SUCCESS: "Success",
  VERIFIED: "Verified",
  SUBMITTED: "Submitted",
  CHECKED_IN: "Checked In",
  RECEIVED: "Received",
  PENDING: "Pending",
  IN_PROGRESS: "In Progress",
  ARCHIVE: "Archive",
  HOLD: "Hold",
  CHECKED_OUT: "Checked Out",
  RESCHEDULE: "Reschedule",
  UPCOMING: "Upcoming",
  STANDARD: "Standard",
  REJECTED: "Rejected",
  CLOSE: "Close",
  NORMAL: "Normal",
  BELOW_NORMAL: "Below Normal",
  OVERWEIGHT: "Overweight",
  ACTIVE: "Active",
  IN_ACTIVE: "Inactive",
  IN_EXAM: "In Exam",
  CONFIRMED: "Confirmed",
  CANCELLED: "Cancelled",
  NOT_VERIFIED: "Not verified",
  INITIATED: "Initiated",
  DECLINED: "Declined",
  CHARGEBACK: "Chargeback",
  UNPAID: "Unpaid",
  EXPIRED: "Expired",
  APPROVED_WITH_CONDITION: "Approved with Condition",
  APPROVED: "Approved",
  PENDING_FROM_PROVIDER: "Pending Medical Review",
  PENDING_FROM_PATIENT: "Pending From Patient",
  NO_SHOW: "No Show",
  COMPLETED: "Completed",
  SCREENERS_PENDING: "Screeners pending",
  ABLE_TO_CHECK_IN: "Able to check in",
  NO_SHOW_FROM_PROVIDER: "No show from provider",
  NO_SHOW_FROM_PATIENT: "No show from patient",
  DISCHARGED: "Discharged",
  DISMISSED: "Dismissed",
  SIGNED_OFF: "Signed Off",
  NOT_AVAILABLE: "Not Available",
  NEW: "New",
  INVITED: "Invited",
};

export const statusLabelsUppercase = {
  DUPLICATE: "DUPLICATE",
  CONSENT_PENDING: "CONSENT PENDING",
  ID_PROOF_PENDING: "ID PROOF PENDING",
  NO_BILL_YET: "NO BILL YET",
  NOT_SUBMITTED: "NOT SUBMITTED",
  CANCEL: "CANCEL",
  WAITING_ROOM: "WAITING ROOM",
  SYSTEM_ERROR: "SYSTEM ERROR",
  FAILED: "FAILED",
  HIGH: "HIGH",
  COMPLETE: "COMPLETED",
  PAID: "PAID",
  SUCCESS: "SUCCESS",
  VERIFIED: "VERIFIED",
  SUBMITTED: "SUBMITTED",
  CHECKED_IN: "CHECKED IN",
  RECEIVED: "RECEIVED",
  PENDING: "PENDING",
  IN_PROGRESS: "IN PROGRESS",
  ARCHIVE: "ARCHIVE",
  HOLD: "HOLD",
  CHECKED_OUT: "CHECKED OUT",
  RESCHEDULE: "RESCHEDULE",
  UPCOMING: "UPCOMING",
  STANDARD: "STANDARD",
  REJECTED: "REJECTED",
  NOT_VERIFIED: "NOT_VERIFIED",
  CLOSE: "CLOSE",
  NORMAL: "NORMAL",
  BELOW_NORMAL: "BELOW NORMAL",
  OVERWEIGHT: "OVERWEIGHT",
  ACTIVE: "ACTIVE",
  IN_ACTIVE: "IN-ACTIVE",
  IN_EXAM: "IN EXAM",
  CONFIRMED: "CONFIRMED",
  CANCELLED: "CANCELLED",
  INITIATED: "INITIATED",
  DECLINED: "DECLINED",
  CHARGEBACK: "CHARGEBACK",
  UNPAID: "UNPAID",
  EXPIRED: "EXPIRED",
  APPROVED_WITH_CONDITION: "APPROVED WITH CONDITION",
  APPROVED: "APPROVED",
  PENDING_FROM_PROVIDER: "PENDING MEDICAL REVIEW",
  PENDING_FROM_PATIENT: "PENDING FROM PATIENT",
  NO_SHOW: "NO SHOW",
  COMPLETED: "COMPLETED",
  SCREENERS_PENDING: "SCREENERS PENDING",
  ABLE_TO_CHECK_IN: "ABLE TO CHECK IN",
  NO_SHOW_FROM_PROVIDER: "NO SHOW FROM PROVIDER",
  NO_SHOW_FROM_PATIENT: "NO SHOW FROM PATIENT",
  DISCHARGED: "DISCHARGED",
  DISMISSED: "DISMISSED",
  SIGNED_OFF: "SIGNED OFF",
  NOT_AVAILABLE: "NOT AVAILABLE",
  NEW: "NEW",
  INVITED: "INVITED",
};

const typeColorMap = {
  DUPLICATE: "#393939",
  NO_BILL_YET: "#393939",
  NOT_SUBMITTED: "#393939",
  CANCEL: "#393939",
  WAITING_ROOM: "#393939",
  SYSTEM_ERROR: "#B42318",
  FAILED: "#B42318",
  REJECTED: "#B42318",
  HIGH: "#B42318",
  UPCOMING: "#0068FF",
  STANDARD: "#0068FF",
  PENDING: "#B54708",
  IN_PROGRESS: "#B54708",
  ARCHIVE: "#B54708",
  HOLD: "#B54708",
  RESCHEDULE: "#B54708",
  CHECKED_OUT: "#B54708",
  COMPLETE: "#049B22",
  PAID: "#049B22",
  ABLE_TO_CHECK_IN: "#049B22",
  SUCCESS: "#049B22",
  VERIFIED: "#049B22",
  SUBMITTED: "#049B22",
  RECEIVED: "#049B22",
  CLOSE: "#565656",
  NO_SHOW_FROM_PROVIDER: "#565656",
  NO_SHOW_FROM_PATIENT: "#565656",
  CHECKED_IN: "#049B22",
  NORMAL: "#049B22",
  BELOW_NORMAL: "#B54708",
  OVERWEIGHT: "#B42318",
  ACTIVE: "#049B22",
  IN_ACTIVE: "#B42318",
  IN_EXAM: "#B54708",
  CONFIRMED: "#049B22",
  CANCELLED: "#B42318",
  NOT_VERIFIED: "#B42318",
  UNPAID: "#B42318",
  INITIATED: "#393939",
  DECLINED: "#145DA0",
  CHARGEBACK: "#B54708",
  EXPIRED: "#565656",
  APPROVED_WITH_CONDITION: "#B54708",
  APPROVED: "#049B22",
  PENDING_FROM_PROVIDER: "#B54708",
  PENDING_FROM_PATIENT: "#B54708",
  CONSENT_PENDING: "#B54708",
  ID_PROOF_PENDING: "#B54708",
  NO_SHOW: "#B54708",
  COMPLETED: "#B54708",
  SCREENERS_PENDING: "#B54708",
  DISCHARGED: "#B42318",
  NEW: "#0068FF",
  INVITED: "#0068FF",
};

const typeBackgroundMap = {
  DUPLICATE: "#F8F8F8",
  NO_BILL_YET: "#F8F8F8",
  NOT_SUBMITTED: "#F8F8F8",
  CANCEL: "#F8F8F8",
  WAITING_ROOM: "#F8F8F8",
  SYSTEM_ERROR: "#FEF3F2",
  FAILED: "#FEF3F2",
  REJECTED: "#FEF3F2",
  NOT_VERIFIED: "#FEF3F2",
  HIGH: "#FEF3F2",
  UPCOMING: "#EEF7FE",
  STANDARD: "#EEF7FE",
  PENDING: "#FFFAEB",
  IN_PROGRESS: "#FFFAEB",
  ARCHIVE: "#FFFAEB",
  HOLD: "#FFFAEB",
  RESCHEDULE: "#FFFAEB",
  CHECKED_OUT: "#FFFAEB",
  COMPLETE: "#ECFDF3",
  PAID: "#ECFDF3",
  VERIFIED: "#ECFDF3",
  ABLE_TO_CHECK_IN: "#ECFDF3",
  UNPAID: "#FEF3F2",
  SUCCESS: "#ECFDF3",
  CHECKED_IN: "#ECFDF3",
  SUBMITTED: "#ECFDF3",
  RECEIVED: "#ECFDF3",
  CLOSE: "#F8F8F8",
  NO_SHOW_FROM_PROVIDER: "#F8F8F8",
  NO_SHOW_FROM_PATIENT: "#F8F8F8",
  NORMAL: "#ECFDF3",
  BELOW_NORMAL: "#FFFAEB",
  OVERWEIGHT: "#FEF3F2",
  ACTIVE: "#ECFDF3",
  IN_ACTIVE: "#FEF3F2",
  IN_EXAM: "#FFFAEB",
  CONFIRMED: "#ECFDF3",
  CANCELLED: "#FEF3F2",
  INITIATED: "#F8F8F8",
  DECLINED: "#F6FAFF",
  CHARGEBACK: "#FFFAEB",
  EXPIRED: "#F8F8F8",
  APPROVED_WITH_CONDITION: "#FFFAEB",
  APPROVED: "#ECFDF3",
  PENDING_FROM_PROVIDER: "#FFFAEB",
  PENDING_FROM_PATIENT: "#FFFAEB",
  CONSENT_PENDING: "#FFFAEB",
  ID_PROOF_PENDING: "#FFFAEB",
  NO_SHOW: "#FFFAEB",
  COMPLETED: "#FFFAEB",
  SCREENERS_PENDING: "#FFFAEB",
  DISCHARGED: "#FEF3F2",
  NEW: "#EEF7FE",
  INVITED: "#EEF7FE",
};

export const getColorByType = (type: StatusEnum) => {
  return typeColorMap[type as keyof typeof typeColorMap];
};

export const getBackgroundByType = (type: StatusEnum) => {
  return typeBackgroundMap[type as keyof typeof typeBackgroundMap];
};
