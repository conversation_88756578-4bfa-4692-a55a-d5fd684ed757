export type AllTypes = {
  firstName: string;
  lastName: string;
  contactNumber: string;
  npiNumber: string;
  archive: boolean;
  role: "THERAPIST" | "PSYCHIATRIST" | "SUPERVISOR" | "PRACTICE_OWNER"; // Add other roles as needed
  languagesSpoken: string[];
  locationUuids: string[] | null;
  supervisorClinicianId: string | null;
  supervisorClinicianName: string;
  locationNames: string[];
  uuid: string;
  locationName: string;
  emailId: string;
  groupNpiNumber: string;
  status: boolean;
  fax: string;
  address: Address;
  size: number;
  page: number;
  searchString: string;
  faxNumber: string;
  email: string;
  roles: string[];
  filter: boolean;
};

type Address = {
  uuid: string;
  line1: string;
  line2: string;
  city: string;
  state: string;
  zipcode: string;
};

export type RolesAndPermissionsData = {
  [role: string]: {
    [group: string]: {
      uuid: string;
      name: string;
      status: boolean;
      group: string;
    }[];
  };
};

export type PermissionGroup = {
  group: string;
  permissions: string[];
};

export type PracticeProfilePayload = {
  uuid: string;
  clinicName: string;
  npiNumber: string;
  taxType: "SSN" | "EIN" | string; // You can refine this union as needed
  taxNumber: string;
  contactNumber: string;
  emailId: string;
  taxonomy: string;
  address: Address;
};

export type ContactPayload = {
  uuid: string;
  contactType: "LAB" | "PHARMACY" | "IMAGING_CENTER" | "OTHER";
  name: string;
  contactNumber: string;
  faxNumber: string;
  emailId: string;
  address: Address;
  size: number;
  page: number;
  searchString: string;
  filter?: boolean;
  archive?: boolean;
};

export type ProcedureCode = {
  uuid: string;
  procedureCode: string;
  codeType: "CPT" | "HCPCS" | "ICD" | string; // Add or refine as needed
  rate: number;
  active: boolean;
  status: boolean;
  archive?: boolean;
};

export interface PatientData {
  uuid: string;
  mrn: string;
  patientName: string;
  dob: string;
  emailId: string;
  contactNumber: string;
  clinician: string;
  memberSince: string;
  status: "NEW" | "ACTIVE" | "DISCHARGED";
  note?: string;
  alertNote?: string | null;
  clientId?: string;
  guardianContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
    responsibleParty: boolean;
  };
  emergencyContact?: {
    name: string;
    phoneNumber: string;
    relationship: string;
    responsibleParty: boolean;
  };
  clientInsurances?: {
    insuranceName: string;
    memberId: string;
    groupId: string;
    startDate: string;
    endDate: string;
    relationship: string;
    subscriberFirstName: string;
    subscriberLastName: string;
    subscriberBirthDate: string;
    insuranceCardFront: string;
    insuranceCardBack: string;
  }[];
  profileImageUrl: string;
  profileImageFile: File;
  profileImageBase64: string;
  primaryClinicianId: string;
  referringClinicianId: string;
  phoneAppointmentReminder: boolean;
  emailAppointmentRemainder: boolean;
  paymentMethod: string;
  legalSex: string;
  genderIdentity: string;
  preferredName: string;
  middleName: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  ethnicity: string;
  phoneNumber: string;
  race: string;
  preferredLanguage: string;
  insuranceName: string;
  memberId: string;
  groupId: string;
  address: Address;
}

export type StaffPayload = {
  uuid?: string;
  firstName?: string;
  lastName?: string;
  emailId?: string;
  contactNumber?: string;
  status?: boolean;
  role?:
    | "FRONT_OFFICE_ADMIN"
    | "BACK_OFFICE_ADMIN"
    | "PRACTICE_OWNER"
    | "PROVIDER"
    | "THERAPIST";
  size: number;
  page: number;
  searchString: string;
};

export type USState =
  | "Alabama"
  | "Alaska"
  | "Arizona"
  | "Arkansas"
  | "California"
  | "Colorado"
  | "Connecticut"
  | "Delaware"
  | "Florida"
  | "Georgia"
  | "Hawaii"
  | "Idaho"
  | "Illinois"
  | "Indiana"
  | "Iowa"
  | "Kansas"
  | "Kentucky"
  | "Louisiana"
  | "Maine"
  | "Maryland"
  | "Massachusetts"
  | "Michigan"
  | "Minnesota"
  | "Mississippi"
  | "Missouri"
  | "Montana"
  | "Nebraska"
  | "Nevada"
  | "New Hampshire"
  | "New Jersey"
  | "New Mexico"
  | "New York"
  | "North Carolina"
  | "North Dakota"
  | "Ohio"
  | "Oklahoma"
  | "Oregon"
  | "Pennsylvania"
  | "Rhode Island"
  | "South Carolina"
  | "South Dakota"
  | "Tennessee"
  | "Texas"
  | "Utah"
  | "Vermont"
  | "Virginia"
  | "Washington"
  | "West Virginia"
  | "Wisconsin"
  | "Wyoming";

export type ClinicianMapPayload = {
  uuid: string;
  firstName: string;
  lastName: string;
};

export type ClinicianPayload = {
  uuid: string;
  firstName: string;
  lastName: string;
  emailId: string;
  contactNumber: string;
  npiNumber: string | null;
  status: null | boolean;
  archive?: boolean;
  roles: string[];
  languagesSpoken: string[];
  locationUuids: string[] | null;
  supervisorClinicianId: string | null;
  supervisorClinicianName: string;
  locationNames: string[];
  size: number;
  page: number;
  searchString: string;
  email: string;
  filter?: boolean;
  // role:
  //   | "THERAPIST"
  //   | "PSYCHIATRIST"
  //   | "SUPERVISOR"
  //   | "PRACTICE_OWNER"
  //   | "FRONT_OFFICE_ADMIN";
};

export type GetClinicianPayload = {
  uuid: string;
  firstName: string;
  lastName: string;
  emailId: string;
  contactNumber: string;
  npiNumber: string | null;
  status: boolean;
  archive?: boolean;
  // role:
  //   | "THERAPIST"
  //   | "PSYCHIATRIST"
  //   | "SUPERVISOR"
  //   | "PRACTICE_OWNER"
  //   | "FRONT_OFFICE_ADMIN";
  languagesSpoken: string[];
  locationUuids: string[] | null;
  supervisorClinicianId: string | null;
  supervisorClinicianName: string;
  locationNames: string[];
};

export type LocationPayload = {
  uuid: string;
  locationName: string;
  contactNumber: string;
  emailId: string;
  groupNpiNumber: string;
  status: boolean;
  fax: string;
  address: Address;
  size: number;
  page: number;
  searchString: string;
  filter: boolean;
  archive?: boolean;
};

export type ProfilePayload = {
  uuid: string;
  clinicName: string;
  npiNumber: string;
  taxType: "EIN" | "SSN"; // Add more if applicable
  taxNumber: string;
  contactNumber: string;
  emailId: string;
  taxonomy: string;
  address: Address;
  size: number;
  page: number;
  searchString: string;
};

export type OfficeLocationPayload = {
  // ... existing code ...
};
