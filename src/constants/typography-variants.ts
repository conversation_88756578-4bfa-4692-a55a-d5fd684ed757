export enum ProfileTypographyVariants {
  TITLE_MEDIUM_PROFILE_BOLD = "titleMediumProfileBold",
  TITLE_SMALL_PROFILE_GREY = "titleSmallProfileGrey",
  TITLE_SMALL_PROFILE_GREY_LIGHT = "titleSmallProfileGreyLight",
  SIGNED_BY_CLINICIAN = "Signed by Albert <PERSON>",
  DATE_TIME = "October 10, 2023 03:29 PM",
}

export enum ClinicianDialogTypographyVariants {
  BODY_REGULAR_6 = "bodyRegular6",
  SIGNED_BY_CLINICIAN = "SIGNED_BY_CLINICIAN",
  DATE_TIME = "DATE_TIME",
}

