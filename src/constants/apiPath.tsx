export const apiPath = {
  LOGIN: "api/master/login",
  VERIFY_LINK_SET: "api/master/verify-password-link/set",
  VERIFY_LINK_RESET: "api/master/verify-password-link/reset",
  GET_PRACTICE_PROFILE: "api/master/practice",
  UPDATE_PRACTICE_PROFILE: "api/master/practice",
  GET_LOCATION_DETAILS: "api/master/location/all",
  GET_ALL_STATES: "api/master/states",
  ADD_LOCATION: "api/master/location",
  EDIT_LOCATION: "api/master/location",
  EDIT_LOCATION_STATUS: "api/master/location/change-status",
  GET_ALL_STAFF: "api/master/staff/all",
  ADD_STAFF: "api/master/staff",
  EDIT_STAFF: "api/master/staff",
  EDIT_STAFF_STATUS: "api/master/staff/change-status",
  GET_ALL_CONTACTS: "api/master/contacts/all",
  ADD_CONTACT: "api/master/contacts",
  EDIT_CONTACT: "api/master/contacts",
  GET_ALL_WORK_LOCATION_CLINICIAN: "api/master/clinician/locations",
  GET_ALL_SUPERVISOR_CLINICIAN: "api/master/clinician/supervisors",
  GET_ALL_CLINICIAN_DETAILS: "api/master/clinician/all",
  ADD_CLINICIAN: "api/master/clinician",
  EDIT_CLINICIAN: "api/master/clinician",
  EDIT_CLINICIAN_STATUS: "api/master/clinician/change-status",
  GET_CLINICIAN_BY_ID: "api/master/clinician",
  GET_LOCATION_DETAILS_BY_ID: "api/master/location",
  GET_ALL_PERMISSIONS: "api/master/roles-and-permissions/permissions",
  GET_ALL_ROLES_AND_PERMISSIONS: "api/master/roles-and-permissions",
  EDIT_ROLES_AND_PERMISSIONS: "api/master/roles-and-permissions",
  GET_ALL_FEE_SCHEDULE: "api/master/fee-schedule",
  ADD_FEE_SCHEDULE: "api/master/fee-schedule",
  UPDATE_FEE_SCHEDULE: "api/master/fee-schedule",
  UPDATE_FEE_SCHEDULE_STATUS: "api/master/fee-schedule/change-status",
  GET_FEE_SCHEDULE_BY_ID: "api/master/fee-schedule",
  GET_ALL_PROCEDURE_CODES: "api/master/fee-schedule/codes",
  ADD_PATIENT: "api/master/client",
  GET_ALL_PATIENTS: "api/master/client",
  ADD_AVAILABILITY: "api/master/availability",
  GET_AVAILABILITY: "api/master/availability",
  GET_AVAILABILOTY_BY_CLINICIAL_ID:
    "api/master/availability/clinician-available-slots",
  GET_PATIENT_BY_ID: "api/master/client",
  GET_USER_PROFILE: "api/master/practice/user-profile",
  ADD_CLINICIAN_SIGNATURE: "api/master/clinician",
  ADD_NEW_LOGIN: "api/master/user/verify",
  SET_PASSWORD: "api/master/set-password",
  SEND_VERIFICATION_OTP: "api/master/forgot-password",
  VERIFY_USER_OTP: "api/master/user/verify",
  ARCHIVE_LOCATION: "api/master/location/archive-restore",
  ARCHIVE_CLINICIAN: "api/master/clinician/archive-restore",
  ARCHIVE_CONTACT: "api/master/contacts/archive-restore",
  CHANGE_USER_PASSWORD: "api/master/change-password",
  ARCHIVE_FEE_SCHEDULE: "api/master/fee-schedule/archive-restore",
  LOGOUT_USER: "api/master/logout",
  CHANGE_PRIMARY_CLINICIAN: "api/master/client/change-clinician",
  GET_EMAIL_BY_UUID: "api/master/get-email-by-uuid",
  ADD_STICKY_NOTES: "api/master/client/sticky-note",
  CHANGE_PORTAL_ACCESS: "api/master/client/portal-access",
  CHANGE_TWO_FACTOR_AUTHENTICATION_PATIENT_PROFILE:
    "api/master/client/two-factor-authentication",
  CHANGE_TWO_FACTOR_AUTHENTICATION_CLINICIAN_PROFILE:
    "api/master/clinician/two-factor-authentication",
  RESEND_INVITE_CLIENT: "api/master/resend-invitation",
  GET_PATIENT_BY_UUID: "api/master/client",
  EDIT_PATIENT_PROFILE: "api/master/client",
  GET_ALL_GROUP_SETTINGS: "api/master/group",
  
};
