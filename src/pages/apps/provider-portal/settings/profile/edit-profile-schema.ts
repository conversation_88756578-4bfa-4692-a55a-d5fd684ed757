import * as yup from "yup";

const taxNumberRegex = /^(?:\d{3}-\d{2}-\d{4}|\d{9}|\d{2}-\d{7})$/;
const taxonomyRegex = /^[A-Z0-9]{9}[A-Z]$/;

export const EditProfileSchema = yup.object().shape({
  clinicName: yup.string().required("Name is required"),
  npiNumber: yup
    .string()
    .required("NPI number is required")
    .matches(/^\d{10}$/, "NPI number must be exactly 10 digits"),
  contactNumber: yup
    .string()
    .required("Contact number is required")
    .min(12, "Contact number must be at least 10 digits")
    .max(13, "Contact number must be at most 10 digits"),
  // .matches(/^[0-9-+()]*$/, "Please enter a valid contact number"),
  emailId: yup
    .string()
    .required("Email is required")
    .email("Please enter a valid email address"),

  taxType: yup.string().optional(),
  taxNumber: yup
    .string()
    .optional()
    .test(
      "is-valid-tax-number",
      "Tax format should be XXX-XX-XXXX",
      (value) => {
        if (!value) return true;
        return taxNumberRegex.test(value);
      }
    ),

  taxonomy: yup
    .string()
    .optional()
    .test(
      "is-valid-taxonomy",
      "Invalid Taxonomy Code. Please enter a valid code, e.g., 261QM0801X.",
      (value) => {
        if (!value) return true;
        return taxonomyRegex.test(value);
      }
    ),
  line1: yup.string().required("Address line 1 is required"),
  line2: yup.string().optional(),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  zip: yup
    .string()
    .required("Zipcode is required")
    .test(
      "zipCode-format",
      "Zipcode should be either 5 or 9 digits",
      (value) => {
        if (!value) return true; // allow empty
        return /^\d{5}(-?\d{4})?$/.test(value);
      }
    ),
});
