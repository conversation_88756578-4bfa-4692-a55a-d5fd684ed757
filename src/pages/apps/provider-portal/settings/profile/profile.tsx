import { Grid, Paper, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import doctorProfileImg from "../../../../../assets/images/Practice Easily Logo.svg";
import { ProfileFieldLabels } from "../../../../../constants/formConst";
import { ProfileTypographyVariants } from "../../../../../constants/typography-variants";
import { getAllPracticeDetails } from "../../../../../redux/auth/profile/get-profile-reducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { ProfilePayload } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { formatPhoneNumber } from "../../../../../common-components/utils/stringUtils";

interface FieldData {
  label: string;
  value: string;
}

interface Address {
  uuid?: string;
  line1: string;
  line2?: string | null;
  city: string;
  state: string;
  zipcode: string;
}

const formatAddress = (address: Address | string): string => {
  if (typeof address === "string") return address;

  const parts = [
    address.line1,
    address.line2,
    address.city,
    address.state,
    address.zipcode,
  ].filter(Boolean);

  return parts.join(", ");
};

const Profile = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [leftColumnFields, setLeftColumnFields] = useState<FieldData[]>([]);
  const [rightColumnFields, setRightColumnFields] = useState<FieldData[]>([]);

  const {
    data: getAllPracticeDetailData,
    status: getAllPracticeDetailsStatus,
  } = useSelector((state: RootState) => state.GetAllPracticeDetailsReducer);

  useEffect(() => {
    dispatch(getAllPracticeDetails());
  }, [dispatch]);

  useEffect(() => {
    if (getAllPracticeDetailData) {
      const profileData = getAllPracticeDetailData as unknown as ProfilePayload;
      setLeftColumnFields([
        {
          label: ProfileFieldLabels.CLINIC_NPI_NUMBER,
          value: profileData.npiNumber,
        },
        {
          label: ProfileFieldLabels.TAX_NUMBER,
          value: `${profileData.taxNumber}  (${profileData.taxType})`,
        },
        {
          label: ProfileFieldLabels.CONTACT_NUMBER,
          value: `${formatPhoneNumber(profileData.contactNumber)}`,
        },
      ]);

      setRightColumnFields([
        {
          label: ProfileFieldLabels.EMAIL_ID,
          value: profileData.emailId,
        },
        {
          label: ProfileFieldLabels.TAXONOMY_CODE,
          value: profileData.taxonomy,
        },
        {
          label: ProfileFieldLabels.ADDRESS,
          value: formatAddress(profileData.address),
        },
      ]);
    }
  }, [getAllPracticeDetailData]);

  useEffect(() => {
    switch (getAllPracticeDetailsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllPracticeDetailsStatus, dispatch]);

  return (
    <Paper
      sx={{
        p: 3,
        borderRadius: 1,
        boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
        width: "100%",
      }}
    >
      <Grid container spacing={3}>
        <Grid
          item
          xs={12}
          md={3}
          sx={{
            display: "flex",
            justifyContent: { xs: "center", md: "flex-start" },
          }}
        >
          <Grid
            component="img"
            src={doctorProfileImg}
            alt="Practice building"
            sx={{
              width: { xs: "70%", sm: "40%", md: "80%" },
              height: "auto",
              objectFit: "contain",
              borderRadius: 1,
              maxHeight: "19vh",
              ml: { xs: 0, md: 2 },
            }}
          />
        </Grid>
        <Grid item xs={12} md={9}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography
                variant={ProfileTypographyVariants.TITLE_MEDIUM_PROFILE_BOLD}
                sx={{ textAlign: { xs: "center", md: "left" } }}
              >
                {getAllPracticeDetailData &&
                  (getAllPracticeDetailData as unknown as ProfilePayload)
                    .clinicName}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Grid container>
                <Grid item xs={12} md={4}>
                  {leftColumnFields.map((field, index) => (
                    <Grid container key={index} sx={{ mb: 2.5 }}>
                      <Grid item xs={5} sm={5}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={7} sm={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>

                <Grid item xs={12} md={5}>
                  {rightColumnFields.map((field, index) => (
                    <Grid container key={index} sx={{ mb: 2.5 }}>
                      <Grid item xs={5} sm={4}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={7} sm={8}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default Profile;
