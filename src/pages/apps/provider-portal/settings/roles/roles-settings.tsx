import { Box } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { rolesTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import { editRolesAndPermissions } from "../../../../../redux/auth/profile/edit-roles-and-permissions-reducer";
import { getAllRolesAndPermissions } from "../../../../../redux/auth/profile/get-all-roles-and-permissions-reducer";
import { getAllRoles } from "../../../../../redux/auth/profile/get-all-roles-reducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
} from "@mui/material";

interface PermissionItem {
  uuid: string;
  name: string;
  status: boolean;
  group: string;
}

interface RolePermissions {
  [key: string]: PermissionItem[];
}

interface RolesData {
  [role: string]: RolePermissions;
}

// Enhanced map to store more information about the permission
interface PermissionMapping {
  uuid: string;
  name: string;
  group: string;
  role: string;
}

// Map to store the relationship between checkbox IDs and permission details
interface UuidMap {
  [checkboxId: string]: PermissionMapping;
}

// Sample data for development in case API doesn't return data
const sampleRolePermissions: RolesData = {
  SUPER_ADMIN: {
    APPOINTMENT: [
      {
        uuid: "f7a9e614-8d23-4c5b-ae31-9d2c5b8e4f76",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "sa-client-uuid",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "sa-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "sa-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
  CASE_MANAGER: {
    APPOINTMENT: [
      {
        uuid: "cm-a1b2c3d4-5e6f-7890-ab12-cd34ef567890",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "cm-b8e3a47c-9b59-4e4a-b85f-a87e42e9b12b",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "cm-doc-uuid",
        name: "View Document",
        status: false,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "cm-avail-uuid",
        name: "Manage Availability",
        status: false,
        group: "AVAILABILITY",
      },
    ],
  },
  NAVIGATOR: {
    APPOINTMENT: [
      {
        uuid: "nav-a1b2c3d4-5e6f-7890-ab12-cd34ef567890",
        name: "Create Appointment",
        status: false,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "nav-b8e3a47c-9b59-4e4a-b85f-a87e42e9b12b",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "nav-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "nav-avail-uuid",
        name: "Manage Availability",
        status: false,
        group: "AVAILABILITY",
      },
    ],
  },
  THERAPIST: {
    APPOINTMENT: [
      {
        uuid: "a1b2c3d4-5e6f-7890-ab12-cd34ef567890",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "b8e3a47c-9b59-4e4a-b85f-a87e42e9b12b",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "th-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "th-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
  CLINICIAN: {
    APPOINTMENT: [
      {
        uuid: "d2e1c479-8b34-4c7a-9f65-a12d4e7b86c0",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "a7f9e2b3-c5d8-4e1a-b67f-32c4e8d9a01b",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "cl-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "cl-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
  PRACTICE_OWNER: {
    APPOINTMENT: [
      {
        uuid: "e3f4a5b6-c7d8-9e01-f234-56789abcdef0",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "f5a6b7c8-d9e0-1f23-4567-89abcdef0123",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "po-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "po-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
  FRONT_OFFICE_ADMIN: {
    APPOINTMENT: [
      {
        uuid: "foa-appt-uuid",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "12345678-9abc-def0-1234-56789abcdef0",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "foa-doc-uuid",
        name: "View Document",
        status: false,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "foa-avail-uuid",
        name: "Manage Availability",
        status: false,
        group: "AVAILABILITY",
      },
    ],
  },
  RECORDS_CUSTODIAN: {
    APPOINTMENT: [
      {
        uuid: "rc-appt-uuid",
        name: "Create Appointment",
        status: false,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "rc-client-uuid",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "c5d2a36e-7f12-4b9a-8e34-d6c1f9e3a45d",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "rc-avail-uuid",
        name: "Manage Availability",
        status: false,
        group: "AVAILABILITY",
      },
    ],
  },
  DIRECTOR: {
    APPOINTMENT: [
      {
        uuid: "98765432-1abc-def0-9876-543210fedcba",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "dir-client-uuid",
        name: "View Client",
        status: true,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "dir-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "dir-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
  PSYCHOTHERAPIST: {
    APPOINTMENT: [
      {
        uuid: "pt-11223344-5566-7788-99aa-bbccddeeff00",
        name: "Create Appointment",
        status: true,
        group: "APPOINTMENT",
      },
    ],
    CLIENT: [
      {
        uuid: "pt-*************-8899-aabb-ccddeeff0011",
        name: "View Client",
        status: false,
        group: "CLIENT",
      },
    ],
    DOCUMENT: [
      {
        uuid: "pt-doc-uuid",
        name: "View Document",
        status: true,
        group: "DOCUMENT",
      },
    ],
    AVAILABILITY: [
      {
        uuid: "pt-avail-uuid",
        name: "Manage Availability",
        status: true,
        group: "AVAILABILITY",
      },
    ],
  },
};

// Available roles for permissions
const roles = [
  { id: "superAdmin", label: "Super Admin" },
  { id: "casemanager", label: "Case Manager" },
  { id: "navigator", label: "Navigator" },
  { id: "recordCustodian", label: "Record Custodian" },
  { id: "frontOfficeAdmin", label: "Front Office Admin" },
  { id: "practiceOwner", label: "Practice Owner" },
  { id: "director", label: "Director" },
  { id: "psyschotherapist", label: "Psychotherapist" },
];

// Utility maps and helper functions for role/permission look-ups.
const roleIdToApiRoleMap: Record<string, string> = {
  superAdmin: "SUPER_ADMIN",
  casemanager: "CASE_MANAGER",
  navigator: "NAVIGATOR",
  recordCustodian: "RECORDS_CUSTODIAN",
  frontOfficeAdmin: "FRONT_OFFICE_ADMIN",
  practiceOwner: "PRACTICE_OWNER",
  director: "DIRECTOR",
  psyschotherapist: "PSYCHOTHERAPIST",
};

const apiRoleDisplayNameMap: Record<string, string> = {
  SUPER_ADMIN: "Super Admin",
  CASE_MANAGER: "Case Manager",
  NAVIGATOR: "Navigator",
  RECORDS_CUSTODIAN: "Record Custodian",
  FRONT_OFFICE_ADMIN: "Front Office Admin",
  PRACTICE_OWNER: "Practice Owner",
  DIRECTOR: "Director",
  PSYCHOTHERAPIST: "Psychotherapist",
};

/**
 * Converts a front-end specific role id into the role code expected by the API.
 */
const roleToApiRole = (roleId: string): string =>
  roleIdToApiRoleMap[roleId] ?? roleId.toUpperCase();

/**
 * Generates a human-friendly label from the API role code.
 */
const getRoleDisplayName = (apiRole: string): string =>
  apiRoleDisplayNameMap[apiRole] ?? apiRole;

/**
 * Looks up a PermissionItem inside the nested RolesData structure.
 */
const getPermissionObject = (
  data: RolesData,
  role: string,
  group: string,
  permission: string
): PermissionItem | undefined =>
  data?.[role]?.[group]?.find((p) => p.name === permission);

/**
 * Returns true if a permission is currently enabled for the role.
 */
const getPermissionStatus = (
  data: RolesData,
  role: string,
  group: string,
  permission: string
): boolean =>
  getPermissionObject(data, role, group, permission)?.status ?? false;

const Roles = () => {
  const dispatch = useDispatch<AppDispatch>();

  const [selectedCheckBoxValues, setSelectedCheckBoxValues] = useState<
    string[]
  >([]);
  const [checkboxUuidMap, setCheckboxUuidMap] = useState<UuidMap>({});
  const [isUpdating, setIsUpdating] = useState(false);
  const [updatingPermissionId, setUpdatingPermissionId] = useState<
    string | null
  >(null);

  // New state for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingCheckboxId, setPendingCheckboxId] = useState<string | null>(
    null
  );

  // const { data: getAllRolesData } = useSelector(
  //   (state: RootState) => state.GetAllRolesReducer || {}
  // );

  const { data: getAllRolesAndPermissionsData } = useSelector(
    (state: RootState) => state.GetAllRolesAndPermissionsReducer || {}
  );

  const { status: editPermissionStatus, error: editPermissionError } =
    useSelector(
      (state: RootState) => state.EditRolesAndPermissionsReducer || {}
    );

  useEffect(() => {
    dispatch(getAllRoles());
    dispatch(getAllRolesAndPermissions());
  }, [dispatch]);

  // Refresh permissions data after successful update
  useEffect(() => {
    if (isUpdating && updatingPermissionId) {
      if (editPermissionStatus === "succeeded") {
        // Refresh data from server
        dispatch(getAllRolesAndPermissions());

        // Update the local checkboxes state to match what we just set on the server
        // Get permission details
        const permissionDetails = checkboxUuidMap[updatingPermissionId];
        if (permissionDetails) {
          const isBeingChecked =
            !selectedCheckBoxValues.includes(updatingPermissionId);

          // Update UI immediately while waiting for the refresh
          setSelectedCheckBoxValues((prev) => {
            if (isBeingChecked) {
              // Add to selected list
              return [...prev, updatingPermissionId];
            } else {
              // Remove from selected list
              return prev.filter((id) => id !== updatingPermissionId);
            }
          });

          // Dispatch a more specific snackbar message
          dispatch(
            snackbarAction.showSnackbarAction({
              severity: "success",
              message: `${permissionDetails.name} permission for ${getRoleDisplayName(permissionDetails.role)} has been ${isBeingChecked ? "enabled" : "disabled"}.`,
            })
          );
        } else {
          dispatch(
            snackbarAction.showSnackbarAction({
              severity: "success",
              message: "Permission updated successfully",
            })
          );
        }

        setIsUpdating(false);
        setUpdatingPermissionId(null);
      } else if (editPermissionStatus === "failed") {
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: "error",
            message: editPermissionError || "Failed to update permission",
          })
        );
        setIsUpdating(false);
        setUpdatingPermissionId(null);
      }
    }
  }, [
    editPermissionStatus,
    editPermissionError,
    dispatch,
    isUpdating,
    updatingPermissionId,
    checkboxUuidMap,
    selectedCheckBoxValues,
  ]);

  // Generate a unique ID for each role+permission combination
  const generateCheckboxId = (permissionUuid: string, roleId: string) => {
    return `${permissionUuid}__${roleId}`;
  };

  // Handle checkbox selection - now shows confirmation dialog
  const handleCheckBoxSelect = (checkboxId: string) => {
    // Instead of directly updating, show confirmation dialog
    setPendingCheckboxId(checkboxId);
    setShowConfirmDialog(true);
  };

  // Handle confirmation dialog - Yes option
  const handleConfirmYes = () => {
    if (pendingCheckboxId) {
      // Execute the original checkbox logic
      const permissionDetails = checkboxUuidMap[pendingCheckboxId];

      if (permissionDetails) {
        // Determine if the checkbox is being checked or unchecked
        const isChecked = !selectedCheckBoxValues.includes(pendingCheckboxId);

        // Set loading state for this specific checkbox
        setIsUpdating(true);
        setUpdatingPermissionId(pendingCheckboxId);

        // No verbose console logging in production code – UI feedback is enough.

        // Dispatch action to update permission
        dispatch(
          editRolesAndPermissions({
            clinicianId: permissionDetails.uuid,
            roleName: permissionDetails.role,
            flag: isChecked,
          })
        );

        // Don't update the UI state here - wait for the API response
        // and we'll update it in the useEffect that watches editPermissionStatus
      } else {
        // Permission details not found – nothing to update.
      }
    }

    // Close dialog and reset state
    setShowConfirmDialog(false);
    setPendingCheckboxId(null);
  };

  // Handle confirmation dialog - No option
  const handleConfirmNo = () => {
    // Just close the dialog and reset state without making changes
    setShowConfirmDialog(false);
    setPendingCheckboxId(null);
  };

  // Get confirmation message based on current state
  const getConfirmationMessage = () => {
    if (!pendingCheckboxId) return "";

    const permissionDetails = checkboxUuidMap[pendingCheckboxId];
    if (!permissionDetails) return "Are you sure you want to make this change?";

    const isCurrentlyChecked =
      selectedCheckBoxValues.includes(pendingCheckboxId);
    const action = isCurrentlyChecked ? "disable" : "enable";
    const roleName = getRoleDisplayName(permissionDetails.role);

    return `Are you sure you want to ${action} "${permissionDetails.name}" permission for ${roleName}?`;
  };

  // Generate table data dynamically from API response with memoization to prevent
  // recalculation on every render
  const { tableData, uuidMap, initialSelectedCheckboxes } = useMemo(() => {
    // For actual API data, we need to convert it to our expected format
    // or use the sample data for development
    let rolePermissionsData: RolesData;

    if (
      getAllRolesAndPermissionsData &&
      typeof getAllRolesAndPermissionsData === "object"
    ) {
      try {
        // Attempt to use the API data, but safeguard with sample data if needed
        rolePermissionsData =
          getAllRolesAndPermissionsData as unknown as RolesData;

        // If the data is empty or doesn't match our expected structure, use sample data
        if (!Object.keys(rolePermissionsData).length) {
          rolePermissionsData = sampleRolePermissions;
        }
      } catch (error) {
        console.error("Error parsing permissions data:", error);
        rolePermissionsData = sampleRolePermissions;
      }
    } else {
      // No data available, use sample data
      rolePermissionsData = sampleRolePermissions;
    }

    // Create a new UUID mapping object
    const newUuidMap: UuidMap = {};
    // Track initially selected checkboxes based on API data
    const selectedCheckboxes: string[] = [];

    // Extract unique groups
    const groups = new Set<string>();

    // Loop through all roles and permissions to extract unique groups
    Object.entries(rolePermissionsData).forEach(([, roleData]) => {
      Object.keys(roleData).forEach((group) => {
        groups.add(group);
      });
    });

    // Convert to array and sort alphabetically
    const groupsArray = Array.from(groups).sort();

    // For each group, get all unique permission names
    const permissionsByGroup: Record<string, Set<string>> = {};

    groupsArray.forEach((group) => {
      permissionsByGroup[group] = new Set<string>();

      Object.entries(rolePermissionsData).forEach(([, roleData]) => {
        if (roleData[group]) {
          roleData[group].forEach((permission) => {
            permissionsByGroup[group].add(permission.name);
          });
        }
      });
    });

    // Now generate the table data
    const tableData = groupsArray.flatMap((group) => {
      // Group header row
      const headerRow = {
        description:
          group === "CLIENT"
            ? "Patients"
            : group === "APPOINTMENT"
              ? "Appointments"
              : group === "AVAILABILITY"
                ? "Availability"
                : group === "DOCUMENT"
                  ? "Documents - Intake"
                  : group,
        style: {
          backgroundColor: "#F4F4F4",
          "& td": {
            fontWeight: 500,
            padding: "9px 16px",
          },
          "& td:not(:first-of-type)": {
            visibility: "hidden",
            backgroundColor: "blue",
          },
        },
      };

      // Get the permissions for this group
      const permissions = Array.from(permissionsByGroup[group]).sort();

      // Permission rows
      const permissionRows = permissions.map((permission) => {
        const permissionUuid =
          group.toLowerCase() +
          "-" +
          permission.toLowerCase().replace(/\s+/g, "-");

        // Create unique IDs for each role's checkbox
        const rowData: any = {
          description: permission,
          uuid: permissionUuid,
          style: {
            "& td": {
              padding: "9px 16px",
            },
          },
        };

        // Add data for each role with a unique checkbox identifier
        roles.forEach((role) => {
          const roleApiName = roleToApiRole(role.id);
          const checkboxId = generateCheckboxId(permissionUuid, role.id);

          // Find the actual permission object and its UUID from the API data
          const permissionObj = getPermissionObject(
            rolePermissionsData,
            roleApiName,
            group,
            permission
          );

          if (permissionObj) {
            // Store all relevant permission details in the mapping
            newUuidMap[checkboxId] = {
              uuid: permissionObj.uuid,
              name: permission,
              group: group,
              role: roleApiName,
            };

            // If permission is enabled, add to selected checkboxes
            const isPermissionEnabled = getPermissionStatus(
              rolePermissionsData,
              roleApiName,
              group,
              permission
            );

            if (isPermissionEnabled) {
              selectedCheckboxes.push(checkboxId);
            }
          }

          rowData[`${role.id}_id`] = checkboxId; // Store the checkbox ID
          rowData[role.id] = getPermissionStatus(
            rolePermissionsData,
            roleApiName,
            group,
            permission
          );
        });

        return rowData;
      });

      return [headerRow, ...permissionRows];
    });

    // Return both the tableData and the UUID map
    return {
      tableData,
      uuidMap: newUuidMap,
      initialSelectedCheckboxes: selectedCheckboxes,
    };
  }, [getAllRolesAndPermissionsData]); // Only recalculate when API data changes

  // Update the UUID map in a separate effect, not during render
  useEffect(() => {
    setCheckboxUuidMap(uuidMap);
    // Update selected checkboxes based on API data whenever it changes
    setSelectedCheckBoxValues(initialSelectedCheckboxes);
  }, [uuidMap, initialSelectedCheckboxes]);

  // Check if a specific checkbox is currently being updated
  // const isCheckboxUpdating = (checkboxId: string) => {
  //   return isUpdating && updatingPermissionId === checkboxId;
  // };

  // Custom rendering to handle our updated checkbox IDs and loading state
  const getSelectedCheckboxValue = (row: any, columnId: string) => {
    const checkboxId = row[`${columnId}_id`] || row.uuid || "";
    return checkboxId;
  };

  // Create a custom modified version of the table headers
  // that knows how to handle our unique checkbox identifiers
  const customizedTableHeaders = useMemo(() => {
    return rolesTableHeaders.map((header) => {
      if (header.type === "checkbox") {
        // For checkbox columns, we need to modify how they identify the value
        return {
          ...header,
          // Add a custom property that indicates this column uses a special ID
          useCustomCheckboxId: true,
          // The custom ID suffix to use for this column
          idSuffix: header.id,
        };
      }
      return header;
    });
  }, []);

  return (
    <Box sx={{ width: "100%" }}>
      <CustomisedTable
        headCells={customizedTableHeaders}
        tableData={tableData}
        setHeight="75vh"
        removeRadius={false}
        handleCheckBoxSelect={handleCheckBoxSelect}
        selectedCheckBoxVal={selectedCheckBoxValues}
        getSelectedCheckboxValue={getSelectedCheckboxValue}
      />

      {/* Confirmation Dialog */}
      <Dialog
        open={showConfirmDialog}
        onClose={handleConfirmNo}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">
          Confirm Permission Change
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            {getConfirmationMessage()}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleConfirmNo} color="primary" variant="outlined">
            No
          </Button>
          <Button
            onClick={handleConfirmYes}
            color="primary"
            variant="contained"
            autoFocus
          >
            Yes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Roles;
