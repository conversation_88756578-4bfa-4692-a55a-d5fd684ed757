import AddIcon from "@mui/icons-material/Add";
import CreateOutlinedIcon from "@mui/icons-material/CreateOutlined";
import SearchIcon from "@mui/icons-material/Search";
import WestOutlinedIcon from "@mui/icons-material/WestOutlined";
import { Grid, SelectChangeEvent, Tab, Tabs, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import CustomInput from "../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../common-components/custom-select/customSelect";
import DrawerBS from "../../../../common-components/drawer-bs/custom-drawer";
import {
  PracticeSettingsRoutes,
  PracticeSettingsTabs,
  SettingsFormConstants,
} from "../../../../constants/formConst";
import { tabLabel, tabSx } from "../../../../constants/tabs-widget";
import { apiStatus } from "../../../../models/apiStatus";
import { RootState } from "../../../../redux/store";
import AddContactsDialog from "./contacts/add-contacts-dialog";
import AddLocationDialog from "./location/add-location-dialog";
import EditProfileDialog from "./profile/edit-profile-dialog";
import AddClinicianDialog from "./Users/<USER>";

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export const MastertabsConstBilling = Object.values(PracticeSettingsTabs);

export default function SettingsTab() {
  const tabLabels: string[] = Object.values(PracticeSettingsTabs);
  const [editProfileDialog, setEditProfileDialog] = React.useState(false);
  const [addLocationDialog, setAddLocationDialog] = React.useState(false);
  const [addContactDialog, setAddContactDialog] = React.useState(false);
  const [addUserDialog, setAddUserDialog] = React.useState(false);
  const [locationString, setLocationString] = useState("");
  const [userString, setUserString] = useState("");
  const [contactsString, setContactStrring] = useState("");
  // const [searchParams] = useSearchParams();
  const [value, setValue] = React.useState(0);
  const navigate = useNavigate();
  const location = useLocation();

  const pageDisplaySize = 10;

  const { data: profileData } = useSelector(
    (state: RootState) => state.GetAllPracticeDetailsReducer
  );

  const { status: archiveLocationStatus } = useSelector(
    (state: RootState) => state.ArchiveLocationReducer
  );

  const { status: archiveClinicianStatus } = useSelector(
    (state: RootState) => state.ArchiveClinicianReducer
  );

  const { status: archiveContactStatus } = useSelector(
    (state: RootState) => state.ArchiveContactReducer
  );

  const tabRoutes = Object.values(PracticeSettingsRoutes);

  const [selectedLocationCodeType, setSelectedLocationCodeType] = useState<
    string | null
  >("ALL");
  const [selectedUserCodeType, setSelectedUserCodeType] = useState<
    string | null
  >("ALL");
  const [selectedContactCodeType, setSelectedContactCodeType] = useState<
    string | null
  >("ALL");

  const handleLocationStatusTypeChange = (e: SelectChangeEvent<string>) => {
    setSelectedLocationCodeType(e.target.value);
  };

  const handleUserStatusTypeChange = (e: SelectChangeEvent<string>) => {
    setSelectedUserCodeType(e.target.value);
  };

  const handleContactStatusTypeChange = (e: SelectChangeEvent<string>) => {
    setSelectedContactCodeType(e.target.value);
  };

  const locationStatusOption = [
    { value: "ACTIVE", label: "Active" },
    { value: "IN_ACTIVE", label: "Inactive" },
    { value: "ARCHIVE", label: "Archive" },
    { value: "ALL", label: "All" },
  ];

  useEffect(() => {
    const currentPath = location.pathname;
    const pathParts = currentPath.split("/");
    const lastPart = pathParts[pathParts.length - 1];

    const index = tabRoutes.findIndex((route) => route === lastPart);
    if (index !== -1) {
      setValue(index);
    } else {
      navigate(`/admin/settings-tabs/profile-tabs/${tabRoutes[0]}`);
    }
  }, [location.pathname, tabRoutes, navigate]);

  useEffect(() => {
    if (archiveLocationStatus === apiStatus.SUCCEEDED) {
      setSelectedLocationCodeType("ALL");
    }
  }, [archiveLocationStatus]);

  useEffect(() => {
    if (archiveClinicianStatus === apiStatus.SUCCEEDED) {
      setSelectedUserCodeType("ALL");
    }
  }, [archiveClinicianStatus]);

  useEffect(() => {
    if (archiveContactStatus === apiStatus.SUCCEEDED) {
      setSelectedContactCodeType("ALL");
    }
  }, [archiveContactStatus]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    navigate(`/admin/settings-tabs/profile-tabs/${tabRoutes[newValue]}`);
  };

  return (
    <Box>
      <Grid
        display={"flex"}
        flexDirection={"row"}
        gap={1}
        alignItems={"center"}
        ml={1}
        mb={1}
      >
        <Grid sx={{ cursor: "pointer" }}>
          <WestOutlinedIcon onClick={() => navigate("/admin/settings-tabs")} />
        </Grid>
        <Grid>
          {" "}
          <Typography variant="titleMediumProfileBold">
            {SettingsFormConstants.PRACTICE_SETTINGS}
          </Typography>
        </Grid>
      </Grid>
      <Box
        display="flex"
        flexDirection={"row"}
        justifyContent={"space-between"}
        width={"100%"}
        alignItems="center"
      >
        <Box sx={{ ...tabSx, ml: 0.5 }}>
          <Tabs value={value} onChange={handleChange}>
            {tabLabels.map((item: string, index: number) => (
              <Tab
                key={index}
                label={item}
                {...a11yProps(index)}
                sx={tabLabel}
              />
            ))}
          </Tabs>
        </Box>
        <Grid mr={1}>
          {value === 0 && (
            <Grid>
              <CustomButton
                variant="editButton"
                label={SettingsFormConstants.EDIT_PROFILE}
                startIcon={<CreateOutlinedIcon />}
                onClick={() => setEditProfileDialog(true)}
              />
            </Grid>
          )}
          {value === 1 && (
            <Grid
              display={"flex"}
              flexDirection={"row"}
              gap={3}
              alignItems={"center"}
            >
              <Grid>
                <CustomInput
                  value={locationString}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setLocationString(e.target.value)
                  }
                  placeholder={"Search Location "}
                  bgWhite={true}
                  showIcon={<SearchIcon />}
                />
              </Grid>
              <Grid width={"8vw"}>
                <CustomSelect
                  placeholder={"Code Type"}
                  value={selectedLocationCodeType || ""}
                  items={locationStatusOption}
                  backgroundColor={"#FFF"}
                  onChange={handleLocationStatusTypeChange}
                />
              </Grid>
              <Grid>
                <CustomButton
                  variant="filled"
                  label={SettingsFormConstants.ADD_NEW_LOCATION}
                  startIcon={<AddIcon />}
                  changePadding={true}
                  onClick={() => setAddLocationDialog(true)}
                />
              </Grid>
            </Grid>
          )}
          {value === 2 && (
            <Grid
              display={"flex"}
              flexDirection={"row"}
              gap={3}
              alignItems={"center"}
            >
              <Grid>
                <CustomInput
                  value={userString}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setUserString(e.target.value)
                  }
                  placeholder={"Search User"}
                  bgWhite={true}
                  showIcon={<SearchIcon />}
                />
              </Grid>
              <Grid width={"8vw"}>
                <CustomSelect
                  placeholder={"Code Type"}
                  value={selectedUserCodeType || ""}
                  items={locationStatusOption}
                  backgroundColor={"#FFF"}
                  onChange={handleUserStatusTypeChange}
                />
              </Grid>
              <Grid>
                <CustomButton
                  variant="filled"
                  label={SettingsFormConstants.ADD_NEW_USER}
                  startIcon={<AddIcon />}
                  changePadding={true}
                  onClick={() => setAddUserDialog(true)}
                />
              </Grid>
            </Grid>
          )}
          {value === 4 && (
            <Grid
              display={"flex"}
              flexDirection={"row"}
              gap={3}
              alignItems={"center"}
            >
              <Grid>
                <CustomInput
                  value={contactsString}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setContactStrring(e.target.value)
                  }
                  placeholder={"Search Contact"}
                  bgWhite={true}
                  showIcon={<SearchIcon />}
                />
              </Grid>
              <Grid width={"8vw"}>
                <CustomSelect
                  placeholder={"Code Type"}
                  value={selectedContactCodeType || ""}
                  items={locationStatusOption}
                  backgroundColor={"#FFF"}
                  onChange={handleContactStatusTypeChange}
                />
              </Grid>
              <Grid>
                <CustomButton
                  variant="filled"
                  label={SettingsFormConstants.ADD_NEW_CONTACT}
                  startIcon={<AddIcon />}
                  changePadding={true}
                  onClick={() => setAddContactDialog(true)}
                />
              </Grid>
            </Grid>
          )}
        </Grid>
      </Box>

      <Box mt={1} p={1}>
        <Outlet
          context={{
            userString,
            selectedUserCodeType,
            contactsString,
            selectedContactCodeType,
            locationString,
            selectedLocationCodeType,
          }}
        />
      </Box>

      <DrawerBS
        title={SettingsFormConstants.EDIT_PROFILE}
        open={editProfileDialog}
        onClose={() => setEditProfileDialog(false)}
        anchor={"right"}
        drawerWidth="50vw"
      >
        <EditProfileDialog
          handleClose={() => setEditProfileDialog(false)}
          profileData={profileData}
        />
      </DrawerBS>
      <DrawerBS
        title={SettingsFormConstants.ADD_NEW_LOCATION}
        open={addLocationDialog}
        onClose={() => setAddLocationDialog(false)}
        anchor={"right"}
        drawerWidth="50vw"
      >
        <AddLocationDialog
          handleClose={() => setAddLocationDialog(false)}
          pageDisplaySize={pageDisplaySize.toString()}
        />
      </DrawerBS>
      <DrawerBS
        title={SettingsFormConstants.ADD_NEW_CONTACT}
        open={addContactDialog}
        onClose={() => setAddContactDialog(false)}
        anchor={"right"}
        drawerWidth="50vw"
      >
        <AddContactsDialog
          handleClose={() => setAddContactDialog(false)}
          pageDisplaySize={pageDisplaySize.toString()}
        />
      </DrawerBS>
      <DrawerBS
        title={SettingsFormConstants.ADD_NEW_USER}
        open={addUserDialog}
        onClose={() => setAddUserDialog(false)}
        anchor={"right"}
        drawerWidth="60vw"
      >
        <AddClinicianDialog
          handleClose={() => setAddUserDialog(false)}
          pageDisplaySize={pageDisplaySize.toString()}
        />
      </DrawerBS>
    </Box>
  );
}
