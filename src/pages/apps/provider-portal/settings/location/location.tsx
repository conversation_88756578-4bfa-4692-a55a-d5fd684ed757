import { Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useOutletContext } from "react-router-dom";
import { LocationPayload } from "src/models/all-const";
import DrawerBS from "../../../../../common-components/drawer-bs/custom-drawer";
import { locationTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import {
  capitalizeFirstLetter,
  formatPhoneNumber,
} from "../../../../../common-components/utils/stringUtils";
import { ViewMode } from "../../../../../constants/formConst";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  archiveLocation,
  archiveLocationReducerAction,
} from "../../../../../redux/auth/profile/archive-location-reducer";
import { getAllLocationDetails } from "../../../../../redux/auth/profile/get-location-details";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import type { AppDispatch } from "../../../../../redux/store";
import { RootState } from "../../../../../redux/store";
import AddLocationDialog from "./add-location-dialog";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";

interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zipcode: string;
}

interface LocationData {
  locationName: string;
  contactNumber: string;
  emailId: string;
  groupNpiNumber: string;
  fax?: string;
  address?: Address;
  status: string;
  uuid: string;
  archive?: boolean;
}

interface LocationResponse {
  content: LocationData[];
  totalPages: number;
  totalElements: number;
}

interface TableLocationData {
  locationName: string;
  contactNumber: string;
  email: string;
  groupNPI: string;
  fax: string;
  address: string;
  status: string;
  uuid: string;
  action: Array<{ label: string; route: string }>;
}

interface LocationOutletContext {
  locationString: string;
  selectedLocationCodeType: string;
}

const Location = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);

  const [locationDataTable, setLocationDataTable] = useState<
    TableLocationData[] | undefined
  >(undefined);

  const [selectedLocation, setSelectedLocation] =
    useState<TableLocationData | null>(null);

  const { locationString, selectedLocationCodeType } =
    useOutletContext<LocationOutletContext>();

  const { data: getAllLocationDetailData, status: getAllLocationStatus } =
    useSelector((state: RootState) => state.GetAllLocationDetailsReducer) as {
      data: LocationResponse | null;
      status: string;
    };

  const {
    status: archiveLocationStatus,
    error: archiveLocationError,
    data: archiveLocationData,
  } = useSelector((state: RootState) => state.ArchiveLocationReducer);

  const getLocationPayload = (pageNumber: number, size: number) => {
    const payload: any = {
      size,
      page: pageNumber,
      searchString: locationString,
    };
    if (selectedLocationCodeType === "ACTIVE") {
      payload.filter = true;
    } else if (selectedLocationCodeType === "IN_ACTIVE") {
      payload.filter = false;
    } else if (selectedLocationCodeType === "ARCHIVE") {
      payload.archive = true;
    }
    return payload;
  };

  const handleArchiveLocation = async (rowData: TableLocationData) => {
    try {
      await dispatch(
        archiveLocation({ uuid: rowData.uuid, archiveStatus: true })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.SUCCESS,
          message:
            archiveLocationData || "Practice location archived successfully",
        })
      );
      dispatch(
        getAllLocationDetails(
          getLocationPayload(page, parseInt(pageDisplaySize)) as LocationPayload
        )
      );
      dispatch(archiveLocationReducerAction.resetArchiveLocationReducer());
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.ERROR,
          message: archiveLocationError || "Failed to archive location",
        })
      );
      dispatch(archiveLocationReducerAction.resetArchiveLocationReducer());
    }
  };

  const handleRestoreLocation = async (rowData: TableLocationData) => {
    try {
      await dispatch(
        archiveLocation({ uuid: rowData.uuid, archiveStatus: false })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.SUCCESS,
          message:
            archiveLocationData || "Practice location restored successfully",
        })
      );
      dispatch(
        getAllLocationDetails(
          getLocationPayload(page, parseInt(pageDisplaySize)) as LocationPayload
        )
      );
      dispatch(archiveLocationReducerAction.resetArchiveLocationReducer());
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.ERROR,
          message: archiveLocationError || "Failed to restore location",
        })
      );
      dispatch(archiveLocationReducerAction.resetArchiveLocationReducer());
    }
  };

  const handleDeleteOrRestore = async (
    rowData: TableLocationData,
    type: ViewMode
  ) => {
    if (type === ViewMode.ARCHIVE) {
      await handleArchiveLocation(rowData);
    } else if (type === ViewMode.RESTORE) {
      await handleRestoreLocation(rowData);
    }
  };

  const [drawerOpenType, setDrawerOpenType] = useState<ViewMode>();
  const [openAddLocationDrawer, setOpenAddLocationDrawer] = useState(false);

  const handlePagechange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
  };

  const handleOpenDrawer = (rowData: TableLocationData, type: ViewMode) => {
    setDrawerOpenType(type);
    setSelectedLocation(rowData);
    setOpenAddLocationDrawer(true);
  };

  const handleCloseDrawer = () => {
    setOpenAddLocationDrawer(false);
    setSelectedLocation(null);
  };

  useEffect(() => {
    switch (getAllLocationStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllLocationStatus, dispatch]);

  useEffect(() => {
    switch (archiveLocationStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [archiveLocationStatus, dispatch]);

  useEffect(() => {
    setPage(0);
  }, [locationString, selectedLocationCodeType]);

  useEffect(() => {
    const timer = setTimeout(
      () =>
        dispatch(
          getAllLocationDetails(
            getLocationPayload(
              page,
              parseInt(pageDisplaySize)
            ) as LocationPayload
          )
        ),
      500
    );
    return () => clearTimeout(timer);
  }, [
    dispatch,
    pageDisplaySize,
    page,
    locationString,
    selectedLocationCodeType,
  ]);

  useEffect(() => {
    if (getAllLocationDetailData) {
      const {
        content,
        totalPages: total,
        totalElements: elements,
      } = getAllLocationDetailData;
      setTotalPages(total);
      setTotalElements(elements);

      const modifiedLocationData = content.map((location: LocationData) => {
        const address = location?.address;
        const addressStr = address
          ? [
              address.line1,
              address.line2,
              address.city,
              address.state,
              address.zipcode,
            ]
              .filter(Boolean)
              .join(", ")
          : "No address available";

        // Conditional actions based on archive status
        const actions = location?.archive
          ? [{ label: "Restore", route: "" }]
          : [
              { label: "Edit", route: "" },
              { label: "Archive", route: "" },
            ];

        return {
          locationName: capitalizeFirstLetter(location?.locationName),
          contactNumber: location?.contactNumber
            ? `${formatPhoneNumber(location.contactNumber)}`
            : "",
          email: location?.emailId,
          groupNPI: location?.groupNpiNumber,
          fax: location?.fax || "",
          address: addressStr,
          status:
            selectedLocationCodeType === "ARCHIVE"
              ? "ARCHIVE"
              : location?.status
                ? "ACTIVE"
                : "IN_ACTIVE",
          uuid: location?.uuid,
          action: actions,
        };
      });
      setLocationDataTable(modifiedLocationData);
    }
  }, [getAllLocationDetailData]);

  return (
    <Grid>
      <Grid>
        <CustomisedTable
          headCells={locationTableHeaders}
          tableData={locationDataTable}
          showCPTAndICDPagination
          setPage={handlePagechange}
          pageSize={totalPages}
          setPageDisplaySize={handlePageSizeChange}
          pageDisplaySize={pageDisplaySize}
          page={page}
          setHeight="65vh"
          handleOpenDrawer={(e) => handleOpenDrawer(e, ViewMode.EDIT)}
          handleDelete={handleDeleteOrRestore}
          handleArchiveLocation={(e) => handleArchiveLocation(e)}
        />
      </Grid>

      <DrawerBS
        anchor={"right"}
        open={openAddLocationDrawer}
        onClose={handleCloseDrawer}
        title={
          drawerOpenType === ViewMode.EDIT ? "Edit Location" : "Add Location"
        }
        drawerWidth="50vw"
      >
        <AddLocationDialog
          handleClose={handleCloseDrawer}
          isEdit={drawerOpenType === ViewMode.EDIT}
          selectedLocation={selectedLocation}
          pageDisplaySize={pageDisplaySize}
        />
      </DrawerBS>
    </Grid>
  );
};

export default Location;
