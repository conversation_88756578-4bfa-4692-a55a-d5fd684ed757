import * as yup from "yup";

export const LocationSchema = yup.object().shape({
  locationName: yup
    .string()
    .required("Location name is required")
    .min(2, "Location name must be at least 2 characters"),
  contactNumber: yup
    .string()
    .required("Contact number is required")
    .min(12, "Contact number must be at least 10 digits")
    .max(13, "Contact number must be at most 10 digits"),
  emailId: yup
    .string()
    .required("Email is required")
    .email("Please enter a valid email address"),
  groupNpiNumber: yup
    .string()
    .required("Group NPI number is required")

    .matches(/^\d{10}$/, "Please enter a valid 10-digit NPI number"),
  status: yup.string().required("Status is required"),
  fax: yup
    .string()
    .optional()
    .matches(/^[0-9-+()]*$/, "Please enter a valid fax number"),
  addressLine1: yup.string().required("Address line 1 is required"),
  addressLine2: yup.string().optional(),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  zipCode: yup
    .string()
    .required("Zipcode is required")
    .optional()
    .test(
      "zipCode-format",
      "Zipcode should be either 5 or 9 digits",
      (value) => {
        if (!value) return true; // allow empty
        return /^\d{5}(-?\d{4})?$/.test(value);
      }
    ),
});
