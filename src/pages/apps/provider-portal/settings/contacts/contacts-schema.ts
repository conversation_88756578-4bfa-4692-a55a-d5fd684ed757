import * as yup from "yup";

export const ContactsSchema = yup.object().shape({
  contactType: yup.string().required("Contact type is required"),
  fullName: yup
    .string()
    .required("Full name is required")
    .min(2, "Full name must be at least 2 characters")
    .matches(/^[a-zA-Z0-9\s]+$/, "Full name cannot contain special characters"),

  contactNumber: yup
    .string()
    .required("Contact number is required")
    .min(12, "Contact number must be at least 10 digits")
    .max(13, "Contact number must be at most 10 digits"),
  faxNumber: yup
    .string()
    .optional()
    .matches(/^[0-9-+()]*$/, "Please enter a valid fax number"),
  emailId: yup
    .string()
    .required("Email is required")
    .email("Please enter a valid email address"),
  addressLine1: yup.string().required("Address line 1 is required"),
  addressLine2: yup.string(),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  status: yup.string().required("Status is required"),

  zipCode: yup
    .string()
    .required("Zipcode is required")
    .test(
      "zipCode-format",
      "Zipcode should be either 5 or 9 digits",
      (value) => {
        if (!value) return true; // allow empty
        return /^\d{5}(-?\d{4})?$/.test(value);
      }
    ),
});
