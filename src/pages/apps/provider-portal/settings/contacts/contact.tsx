import { Box, Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useOutletContext } from "react-router-dom";
import { ContactPayload } from "src/models/all-const";
import DrawerBS from "../../../../../common-components/drawer-bs/custom-drawer";
import { contactTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import {
  capitalizeFirstLetter,
  formatPhoneNumber,
} from "../../../../../common-components/utils/stringUtils";
import { ViewMode } from "../../../../../constants/formConst";
import { getAllContacts } from "../../../../../redux/auth/profile/get-all-contacts-reducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import AddContactsDialog from "./add-contacts-dialog";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import {
  archiveContact,
  archiveContactReducerAction,
} from "../../../../../redux/auth/profile/archive-contact-reducer";

// Define interfaces for contact data
interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zipcode: string;
}

interface ContactData {
  name: string;
  contactType: string;
  emailId: string;
  contactNumber: string;
  faxNumber: string;
  uuid: string;
  address?: Address;
  workLocation: string[] | string;
  archive?: boolean;
  status?: boolean;
}

interface ContactResponse {
  content: ContactData[];
  totalPages: number;
  totalElements: number;
}

interface TableContactData {
  name: string;
  contacttype: string;
  email: string;
  address: string;
  contact: string;
  fax: string;
  uuid: string;
  workLocation: string;
  originalContact: ContactData;
  action: Array<{ label: string; route: string }>;
}

interface ContactOutletContext {
  contactsString: string;
  selectedContactCodeType: string;
}

const Contact = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);
  const [tableData, setTableData] = useState<TableContactData[] | undefined>(
    undefined
  );

  const [openContactDrawer, setOpenContactDrawer] = useState(false);
  const [drawerOpenType, setDrawerOpenType] = useState<ViewMode>();
  const [selectedContact, setSelectedContact] =
    useState<TableContactData | null>(null);

  const { contactsString, selectedContactCodeType } =
    useOutletContext<ContactOutletContext>();

  const { data: getAllContactsData, status: getAllContactsStatus } =
    useSelector((state: RootState) => state.GetAllContactsReducer) as {
      data: ContactResponse | null;
      status: string;
    };

  const {
    status: archiveContactStatus,
    error: archiveContactError,
    data: archiveContactData,
  } = useSelector((state: RootState) => state.ArchiveContactReducer);

  const getContactPayload = (pageNumber: number, size: number) => {
    const payload: any = {
      size,
      page: pageNumber,
      searchString: contactsString,
    };
    if (selectedContactCodeType === "ACTIVE") {
      payload.filter = true;
    } else if (selectedContactCodeType === "IN_ACTIVE") {
      payload.filter = false;
    } else if (selectedContactCodeType === "ARCHIVE") {
      payload.archive = true;
    }
    return payload;
  };

  const handleArchiveContact = async (rowData: any) => {
    try {
      await dispatch(
        archiveContact({ uuid: rowData.uuid, archiveStatus: true })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "success",
          message: archiveContactData || "Contact archived successfully",
        })
      );
      dispatch(
        getAllContacts(
          getContactPayload(page, parseInt(pageDisplaySize)) as ContactPayload
        )
      );
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "error",
          message: archiveContactError || "Failed to archive contact",
        })
      );
    }
  };

  const handleRestoreContact = async (rowData: any) => {
    try {
      await dispatch(
        archiveContact({ uuid: rowData.uuid, archiveStatus: false })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "success",
          message: `Contact restored successfully`,
        })
      );
      dispatch(
        getAllContacts(
          getContactPayload(page, parseInt(pageDisplaySize)) as ContactPayload
        )
      );
      dispatch(archiveContactReducerAction.resetArchiveContactReducer());
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "error",
          message: "Failed to restore contact",
        })
      );
      dispatch(archiveContactReducerAction.resetArchiveContactReducer());
    }
  };

  const handleDeleteOrRestore = async (rowData: any, type: ViewMode) => {
    if (type === ViewMode.ARCHIVE) {
      await handleArchiveContact(rowData);
    } else if (type === ViewMode.RESTORE) {
      await handleRestoreContact(rowData);
    }
  };

  const handleCloseDrawer = () => {
    setOpenContactDrawer(false);
    setSelectedContact(null);
  };

  const handlePageChange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
  };

  const handleOpenDrawer = (rowData: TableContactData, type: ViewMode) => {
    setDrawerOpenType(type);
    setSelectedContact(rowData);
    setOpenContactDrawer(true);
  };

  useEffect(() => {
    setPage(0);
  }, [contactsString, selectedContactCodeType]);

  useEffect(() => {
    const timer = setTimeout(
      () =>
        dispatch(
          getAllContacts(
            getContactPayload(page, parseInt(pageDisplaySize)) as ContactPayload
          )
        ),
      500
    );
    return () => clearTimeout(timer);
  }, [
    dispatch,
    page,
    pageDisplaySize,
    contactsString,
    selectedContactCodeType,
  ]);

  useEffect(() => {
    if (getAllContactsData) {
      const {
        content,
        totalPages: total,
        totalElements: elements,
      } = getAllContactsData;
      setTotalPages(total);
      setTotalElements(elements);

      const modifiedContactsData = content?.map((contact: ContactData) => {
        const address = contact?.address;
        const addressStr = address
          ? [
              address.line1,
              address.line2,
              address.city,
              address.state,
              address.zipcode,
            ]
              .filter((part) => part)
              .join(", ")
          : "-";

        // Handle work location array
        const workLocation = Array.isArray(contact?.workLocation)
          ? contact.workLocation.join(", ")
          : contact?.workLocation || "";

        // Conditional actions based on archive status
        const actions = contact?.archive
          ? [{ label: "Restore", route: "" }]
          : [
              { label: "Edit", route: "" },
              { label: "Archive", route: "" },
            ];

        return {
          name: contact?.name,
          contacttype: capitalizeFirstLetter(contact?.contactType),
          email: contact?.emailId,
          address: addressStr,
          contact: contact?.contactNumber
            ? `${formatPhoneNumber(contact.contactNumber)}`
            : "",
          fax: contact?.faxNumber,
          uuid: contact?.uuid,
          workLocation: workLocation,
          originalContact: contact,
          status:
            selectedContactCodeType === "ARCHIVE"
              ? "ARCHIVE"
              : contact?.status
                ? "ACTIVE"
                : "IN_ACTIVE",
          action: actions,
        };
      });
      setTableData(modifiedContactsData);
    }
  }, [getAllContactsData, selectedContactCodeType]);

  useEffect(() => {
    switch (getAllContactsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllContactsStatus, dispatch]);

  useEffect(() => {
    switch (archiveContactStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [archiveContactStatus, dispatch]);

  return (
    <>
      <Grid>
        <Box sx={{ width: "100%" }}>
          <CustomisedTable
            headCells={contactTableHeaders}
            tableData={tableData}
            showCPTAndICDPagination
            setPage={handlePageChange}
            pageSize={totalPages}
            setPageDisplaySize={handlePageSizeChange}
            pageDisplaySize={pageDisplaySize}
            page={page}
            setHeight="65vh"
            handleOpenDrawer={(e) => handleOpenDrawer(e, ViewMode.EDIT)}
            handleDelete={handleDeleteOrRestore}
            handleArchiveLocation={(e) => handleArchiveContact(e)}
          />
        </Box>
        <DrawerBS
          anchor={"right"}
          open={openContactDrawer}
          onClose={handleCloseDrawer}
          title={
            drawerOpenType === ViewMode.EDIT ? "Edit Contact" : "Add Contact"
          }
        >
          <AddContactsDialog
            handleClose={handleCloseDrawer}
            isEdit={drawerOpenType === ViewMode.EDIT}
            selectedContact={selectedContact as any}
            pageDisplaySize={pageDisplaySize}
          />
        </DrawerBS>
      </Grid>
    </>
  );
};

export default Contact;
