import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Grid } from "@mui/material";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { AlertSeverity } from "../../../../../common-components/alert/alert";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import CustomContactInputNew from "../../../../../common-components/custom-contact-field/custom-contact-field";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import {
  ContactFormLabels,
  ContactFormPlaceholders,
  LocationFormLabels,
  LocationFormPlaceholders,
  SettingsFormPlaceholders,
} from "../../../../../constants/formConst";
import { AllTypes, ContactPayload } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { PatientTypes } from "../../../../../models/providerGroup";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  addContacts,
  addContactsReducerAction,
} from "../../../../../redux/auth/profile/add-contacts-reducer";
import {
  editContacts,
  editContactsReducerAction,
} from "../../../../../redux/auth/profile/edit-contacts-reducer";
import { getAllContacts } from "../../../../../redux/auth/profile/get-all-contacts-reducer";
import { getAllAmericanStates } from "../../../../../redux/auth/profile/get-all-states-reducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { ContactsSchema } from "./contacts-schema";
interface ContactAddress {
  line1: string;
  line2: string;
  city: string;
  state: string;
  zipcode: string;
}

interface ContactData {
  uuid?: string;
  contactType: string;
  name: string;
  contactNumber: string;
  faxNumber: string;
  emailId: string;
  status: boolean;
  address: ContactAddress;
}

interface SelectedContact {
  originalContact: ContactData;
}

interface AddContactsDialogProps {
  handleClose: () => void;
  isEdit?: boolean;
  selectedContact?: SelectedContact;
  pageDisplaySize: string;
}

const AddContactsDialog = ({
  handleClose,
  isEdit,
  selectedContact,
  pageDisplaySize,
}: AddContactsDialogProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    control,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm({
    defaultValues: {
      contactType: "",
      fullName: "",
      contactNumber: "",
      faxNumber: "",
      emailId: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      zipCode: "",
      status: "active",
    },
    resolver: yupResolver(ContactsSchema),
  });

  const contactTypeOptions = [
    { value: "REFERRAL", label: "Referral" },
    { value: "LAB", label: "Lab" },
  ];

  const statusOptions = [
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  const getAllAmericanStatesData = useSelector((state: RootState) => {
    const data = state.GetAllAmericanStatesReducer?.data;
    return Array.isArray(data) ? data : [];
  });

  const stateOptions = getAllAmericanStatesData.map((state: string) => ({
    value: state,
    label: state,
  }));

  useEffect(() => {
    dispatch(getAllAmericanStates());
  }, [dispatch]);

  const addContactsStatus = useSelector(
    (state: RootState) => state.AddContactsReducer?.status
  );

  const addContactData = useSelector(
    (state: RootState) => state.AddContactsReducer?.data
  );

  const addContactsError = useSelector(
    (state: RootState) => state.AddContactsReducer?.error
  );

  const editContactsStatus = useSelector(
    (state: RootState) => state.EditContactsReducer?.status
  );

  const editContactData = useSelector(
    (state: RootState) => state.EditContactsReducer?.data
  );

  const editContactsError = useSelector(
    (state: RootState) => state.EditContactsReducer?.error
  );

  useEffect(() => {
    if (isEdit && selectedContact) {
      // Use the original contact data for prepopulation

      const originalContact = selectedContact.originalContact;
      setValue("contactType", originalContact.contactType);
      setValue("fullName", originalContact.name);
      setValue("contactNumber", originalContact.contactNumber);
      setValue("faxNumber", originalContact.faxNumber);
      setValue("emailId", originalContact.emailId);
      setValue(
        "status",
        originalContact.status === true ? "active" : "inactive"
      );
      // Handle address from the original contact data
      const address = originalContact.address;
      if (address) {
        setValue("addressLine1", address.line1);
        setValue("addressLine2", address.line2);
        setValue("city", address.city);
        setValue("state", address.state);
        setValue("zipCode", address.zipcode);
      }
    }
  }, [isEdit, selectedContact, setValue]);

  const onSubmit = (data: any) => {
    const payload = {
      uuid: isEdit ? selectedContact?.originalContact?.uuid : undefined,
      contactType: data?.contactType,
      name: data?.fullName,
      contactNumber: data?.contactNumber,
      faxNumber: data?.faxNumber,
      emailId: data?.emailId,
      status: data.status == "active" ? true : false,
      address: {
        line1: data?.addressLine1,
        line2: data?.addressLine2,
        city: data?.city,
        state: data?.state,
        zipcode: data?.zipCode,
      },
      xTenant: "default",
    };
    if (isEdit) {
      dispatch(editContacts(payload as unknown as PatientTypes));
    } else {
      dispatch(addContacts(payload as unknown as AllTypes));
    }
  };

  useEffect(() => {
    switch (addContactsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: addContactData as string,
          })
        );
        dispatch(
          getAllContacts({
            size: parseInt(pageDisplaySize, 10),
            page: 0,
            searchString: "",
          } as ContactPayload)
        );
        dispatch(addContactsReducerAction.resetAddContactsReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addContactsError ?? undefined,
          })
        );
        break;
    }
  }, [
    addContactsStatus,
    dispatch,
    addContactsError,
    handleClose,
    pageDisplaySize,
  ]);

  useEffect(() => {
    switch (editContactsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: editContactData as string,
          })
        );
        dispatch(
          getAllContacts({
            size: parseInt(pageDisplaySize, 10),
            page: 0,
            searchString: "",
          } as ContactPayload)
        );
        dispatch(editContactsReducerAction.resetEditContactsReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: editContactsError ?? undefined,
          })
        );
        break;
    }
  }, [
    editContactsStatus,
    dispatch,
    editContactsError,
    handleClose,
    pageDisplaySize,
  ]);

  return (
    <Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.CONTACT_TYPE} isRequired />
            <Controller
              control={control}
              name="contactType"
              render={({ field }) => (
                <CustomSelect
                  placeholder={ContactFormPlaceholders.SELECT_TYPE}
                  {...field}
                  value={field.value}
                  items={contactTypeOptions}
                  hasError={!!errors.contactType}
                  errorMessage={errors.contactType?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.FULL_NAME} isRequired />
            <Controller
              control={control}
              name="fullName"
              render={({ field }) => (
                <CustomInput
                  placeholder={ContactFormPlaceholders.ENTER_FULL_NAME}
                  {...field}
                  hasError={!!errors.fullName}
                  errorMessage={errors.fullName?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.CONTACT_NUMBER} isRequired />
            <Controller
              control={control}
              name="contactNumber"
              render={({ field }) => (
                <CustomContactInputNew
                  {...field}
                  hasError={!!errors.contactNumber}
                  errorMessage={errors.contactNumber?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.FAX_NUMBER} />
            <Controller
              control={control}
              name="faxNumber"
              render={({ field }) => (
                <CustomInput
                  placeholder={ContactFormPlaceholders.ENTER_FAX_NUMBER}
                  {...field}
                  hasError={!!errors.faxNumber}
                  errorMessage={errors.faxNumber?.message}
                  isNumeric={true}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.EMAIL_ID} isRequired />
            <Controller
              control={control}
              name="emailId"
              render={({ field }) => (
                <CustomInput
                  placeholder={ContactFormPlaceholders.ENTER_EMAIL_ID}
                  {...field}
                  hasError={!!errors.emailId}
                  errorMessage={errors.emailId?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <CustomLabel label={LocationFormLabels.STATUS} />
            <Controller
              control={control}
              name="status"
              render={({ field }) => (
                <CustomSelect
                  placeholder={LocationFormPlaceholders.SELECT_STATUS}
                  {...field}
                  value={field.value || ""}
                  items={statusOptions}
                  hasError={!!errors.status}
                  errorMessage={errors.status?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <CustomLabel label={LocationFormLabels.ADDRESS_LINE_1} isRequired />
            <Controller
              control={control}
              name="addressLine1"
              render={({ field }) => (
                <CustomInput
                  placeholder={LocationFormPlaceholders.ENTER_ADDRESS_LINE_1}
                  {...field}
                  hasError={!!errors.addressLine1}
                  errorMessage={errors.addressLine1?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <CustomLabel label={LocationFormLabels.ADDRESS_LINE_2} />
            <Controller
              control={control}
              name="addressLine2"
              render={({ field }) => (
                <CustomInput
                  placeholder={LocationFormPlaceholders.ENTER_ADDRESS_LINE_2}
                  {...field}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.CITY} isRequired />
            <Controller
              control={control}
              name="city"
              render={({ field }) => (
                <CustomInput
                  placeholder={ContactFormPlaceholders.SELECT_CITY}
                  {...field}
                  hasError={!!errors.city}
                  errorMessage={errors.city?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.STATE} isRequired />
            <Controller
              control={control}
              name="state"
              render={({ field }) => (
                <CustomSelect
                  placeholder={SettingsFormPlaceholders.SELECT_STATE}
                  {...field}
                  value={field.value || ""}
                  items={stateOptions}
                  hasError={!!errors.state}
                  errorMessage={errors.state?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ContactFormLabels.ZIP_CODE} isRequired />
            <Controller
              control={control}
              name="zipCode"
              render={({ field }) => (
                <CustomInput
                  placeholder={ContactFormPlaceholders.ENTER_ZIP_CODE}
                  {...field}
                  isNumeric={true}
                  hasError={!!errors.zipCode}
                  errorMessage={errors.zipCode?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          flexDirection={"row"}
          justifyContent={"flex-end"}
          mt={2}
          sx={{
            display: "flex",
            position: "absolute",
            bottom: "0",
            right: "0",
            width: "100%",
            borderTop: "1px solid #E7E7E7",
            paddingTop: 2,
          }}
        >
          <Grid
            display="flex"
            flexDirection={"row"}
            gap={3}
            sx={{ marginBottom: "1.5vh", marginRight: "1.5vw" }}
          >
            <Grid>
              <CustomButton
                variant="outline"
                label="Cancel"
                isSubmitButton
                onClick={handleClose}
              />
            </Grid>
            <Grid>
              <CustomButton
                variant="filled"
                label="Save"
                type="submit"
                changePadding={false}
                isSubmitButton
              />
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddContactsDialog;
