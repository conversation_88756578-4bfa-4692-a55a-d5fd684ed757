import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Grid } from "@mui/material";
import { useEffect } from "react";
import { Controller, Resolver, useForm, useWatch } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { AllTypes, ClinicianPayload } from "src/models/all-const";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import CustomContactInputNew from "../../../../../common-components/custom-contact-field/custom-contact-field";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomMultiSelect from "../../../../../common-components/custom-multiselect/custom-multiselect";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";
import { capitalizeFirstLetter } from "../../../../../common-components/utils/stringUtils";
import {
  ClinicianFormLabels,
  ClinicianFormPlaceholders,
  StaffFormLabels,
  StaffFormPlaceholders,
} from "../../../../../constants/formConst";
import { apiStatus } from "../../../../../models/apiStatus";
import { PatientTypes } from "../../../../../models/providerGroup";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  addClinician,
  addClinicianReducerAction,
} from "../../../../../redux/auth/profile/add-clinician-reducer";
import {
  editClinician,
  editClinicianReducerAction,
} from "../../../../../redux/auth/profile/edit-clinician-reducer";
import { getAllClinicians } from "../../../../../redux/auth/profile/get-all-clinicians";
import { getAllSupervisingClinicians } from "../../../../../redux/auth/profile/get-all-supervising-clinicians";
import { getAllWorkLocationClinician } from "../../../../../redux/auth/profile/get-all-worklocation-clinicians";
import { getClinicianById } from "../../../../../redux/auth/profile/get-clinician-by-id-reducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { ClinicianSchema } from "./clinician-schema";

interface AddClinicianDialogProps {
  handleClose: () => void;
  isEdit?: boolean;
  selectedClinician?: any;
  pageDisplaySize: string;
}

interface ClinicianFormData {
  firstName: string;
  lastName: string;
  emailId: string;
  contactNumber: string;
  npiNumber: string | null;
  workLocations?: string[];
  languagesSpoken?: string[];
  supervisingClinician?: string;
  role?: string[];
  status?: string;
}

const AddClinicianDialog = ({
  handleClose,
  isEdit = false,
  selectedClinician,
  pageDisplaySize,
}: AddClinicianDialogProps) => {
  const {
    control,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<ClinicianFormData>({
    defaultValues: {
      firstName: "",
      lastName: "",
      emailId: "",
      contactNumber: "",
      npiNumber: null,
      workLocations: [],
      languagesSpoken: [],
      supervisingClinician: "",
      role: [],
      status: "active",
    },
    resolver: yupResolver(ClinicianSchema) as Resolver<ClinicianFormData>,
  });

  const watchedRoles = useWatch({
    control,
    name: "role",
  });

  const rolesToShowFields = ["PSYCHOTHERAPIST", "CASE_MANAGER", "NAVIGATOR"];

  const shouldShowFields =
    Array.isArray(watchedRoles) &&
    watchedRoles.some((role) => rolesToShowFields.includes(role));

  const dispatch = useDispatch<AppDispatch>();

  const statusOptions = [
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  const {
    status: addClinicianStatus,
    error: addClinicianError,
    data: addClinicianData,
  }: any = useSelector((state: RootState) => state.AddClinicianReducer);

  const {
    status: editClinicianStatus,
    error: editClinicianError,
    data: editClinicianData,
  }: any = useSelector((state: RootState) => state.EditClinicianReducer);

  const { data: getClinicianByIdData = {} }: any = useSelector(
    (state: RootState) => state.GetClinicianByIdReducer
  );

  const { data: getAllWorkLocationClinicianData = {} }: any =
    useSelector(
      (state: RootState) => state.GetAllWorkLocationClinicianReducer
    ) || {};

  const { data: getAllSupervisingCliniciansData }: any =
    useSelector(
      (state: RootState) => state.GetAllSupervisingCliniciansReducer
    ) || {};

  const displaySupervisingClinicians = Object.entries(
    getAllSupervisingCliniciansData || {}
  ).map(([uuid, value]) => ({
    value: uuid,
    label: String(value),
  }));

  const workLocationOptions = Object.entries(
    getAllWorkLocationClinicianData || {}
  ).map(([uuid, locationName]) => ({
    key: uuid,
    value: capitalizeFirstLetter(String(locationName)),
  }));

  const roleOptions = [
    { key: "PSYCHOTHERAPIST", value: "Psychotherapist" },
    { key: "CASE_MANAGER", value: "Case Manager" },
    { key: "NAVIGATOR", value: "Navigator" },
    { key: "PRACTICE_OWNER", value: "Practice Owner" },
    { key: "DIRECTOR", value: "Director" },
    { key: "FRONT_OFFICE_ADMIN", value: "Front Office Admin" },
    { key: "RECORDS_CUSTODIAN", value: "Records Custodian" },
  ];

  const languageOptions = [
    { key: "English", value: "English" },
    { key: "Spanish", value: "Spanish" },
    { key: "Portuguese", value: "Portuguese" },
    { key: "French", value: "French" },
    { key: "Mandarin", value: "Mandarin" },
    { key: "Arabic", value: "Arabic" },
    { key: "Japanese", value: "Japanese" },
  ];

  const onSubmit = (data: ClinicianFormData) => {
    const payload: any = {
      uuid: selectedClinician?.uuid,
      firstName: data?.firstName,
      lastName: data?.lastName,
      emailId: data?.emailId,
      contactNumber: data?.contactNumber ?? "",
      npiNumber:
        shouldShowFields && data?.npiNumber && data?.npiNumber !== ""
          ? data?.npiNumber
          : null,
      locationUuids: shouldShowFields ? (data?.workLocations ?? null) : null,
      languagesSpoken: shouldShowFields ? (data?.languagesSpoken ?? []) : [],
      supervisorClinicianId: shouldShowFields
        ? data?.supervisingClinician || null
        : null,
      roles: data?.role ?? [],
      size: 10,
      page: 0,
      searchString: "",
      email: data?.emailId,
      ...(isEdit &&
        getClinicianByIdData?.status != null && {
          status: data?.status === "active",
        }),
      archive: false,
      supervisorClinicianName: "",
      locationNames: [],
    };

    if (isEdit) {
      dispatch(editClinician(payload as unknown as PatientTypes));
    } else {
      dispatch(addClinician(payload as unknown as AllTypes));
    }
  };

  useEffect(() => {
    if (selectedClinician?.uuid) {
      dispatch(getClinicianById(selectedClinician?.uuid));
    }
  }, [selectedClinician, dispatch]);

  useEffect(() => {
    dispatch(getAllWorkLocationClinician());
  }, [dispatch]);

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  useEffect(() => {
    if (
      isEdit &&
      getClinicianByIdData &&
      Object.keys(getClinicianByIdData).length > 0
    ) {
      setValue("firstName", getClinicianByIdData.firstName || "");
      setValue("lastName", getClinicianByIdData.lastName || "");
      setValue("emailId", getClinicianByIdData.emailId || "");
      setValue("contactNumber", getClinicianByIdData.contactNumber || "");
      setValue("npiNumber", getClinicianByIdData.npiNumber || "");

      const locationUuids = getClinicianByIdData?.locationUuids || [];
      setValue(
        "workLocations",
        Array.isArray(locationUuids) ? locationUuids : []
      );

      const languagesList = getClinicianByIdData?.languagesSpoken || [];
      setValue(
        "languagesSpoken",
        Array.isArray(languagesList) ? languagesList : []
      );
      setValue("status", getClinicianByIdData?.status ? "active" : "inactive");

      setValue(
        "supervisingClinician",
        getClinicianByIdData?.supervisorClinicianId || ""
      );

      const rolesArray = Array.isArray(getClinicianByIdData?.roles)
        ? getClinicianByIdData.roles
        : [];

      setValue("role", rolesArray);
    }
  }, [isEdit, getClinicianByIdData, setValue]);

  useEffect(() => {
    switch (addClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: (addClinicianData as string) || "",
          })
        );
        dispatch(
          getAllClinicians({
            size: parseInt(pageDisplaySize, 10),
            page: 0,
            searchString: "",
          } as ClinicianPayload)
        );
        dispatch(addClinicianReducerAction.resetAddClinicianReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addClinicianError,
          })
        );
        dispatch(addClinicianReducerAction.resetAddClinicianReducer());
        break;
    }
  }, [addClinicianStatus, dispatch, addClinicianError, handleClose]);

  useEffect(() => {
    switch (editClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: (editClinicianData as string) || "",
          })
        );
        dispatch(
          getAllClinicians({
            size: parseInt(pageDisplaySize, 10),
            page: 0,
            searchString: "",
          } as ClinicianPayload)
        );
        dispatch(editClinicianReducerAction.resetEditClinicianReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: editClinicianError,
          })
        );
        dispatch(editClinicianReducerAction.resetEditClinicianReducer());
        break;
    }
  }, [editClinicianStatus, dispatch, editClinicianError, handleClose]);

  return (
    <Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <CustomLabel
              label={ClinicianFormLabels.FIRST_NAME}
              isRequired={true}
            />
            <Controller
              control={control}
              name="firstName"
              render={({ field }) => (
                <CustomInput
                  placeholder={ClinicianFormPlaceholders.ENTER_FIRST_NAME}
                  {...field}
                  hasError={!!errors.firstName}
                  errorMessage={errors.firstName?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel
              label={ClinicianFormLabels.LAST_NAME}
              isRequired={true}
            />
            <Controller
              control={control}
              name="lastName"
              render={({ field }) => (
                <CustomInput
                  placeholder={ClinicianFormPlaceholders.ENTER_LAST_NAME}
                  {...field}
                  hasError={!!errors.lastName}
                  errorMessage={errors.lastName?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ClinicianFormLabels.ROLE} isRequired={true} />
            <Controller
              control={control}
              name="role"
              render={({ field }) => (
                <CustomMultiSelect
                  placeholder={ClinicianFormPlaceholders.SELECT_ROLE}
                  value={Array.isArray(field.value) ? field.value : []}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  options={roleOptions}
                  hasError={!!errors.role}
                  errMsg={errors.role?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel label={ClinicianFormLabels.EMAIL_ID} isRequired />
            <Controller
              control={control}
              name="emailId"
              render={({ field }) => (
                <CustomInput
                  placeholder={ClinicianFormPlaceholders.ENTER_EMAIL_ID}
                  {...field}
                  hasError={!!errors.emailId}
                  errorMessage={errors.emailId?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <CustomLabel
              label={ClinicianFormLabels.CONTACT_NUMBER}
              isRequired
            />
            <Controller
              control={control}
              name="contactNumber"
              render={({ field }) => (
                <CustomContactInputNew
                  {...field}
                  hasError={!!errors.contactNumber}
                  errorMessage={errors.contactNumber?.message}
                />
              )}
            />
          </Grid>

          {isEdit && getClinicianByIdData?.status != null && (
            <Grid item xs={12} md={4}>
              <CustomLabel label={StaffFormLabels.STATUS} />
              <Controller
                control={control}
                name="status"
                render={({ field }) => (
                  <CustomSelect
                    placeholder={StaffFormPlaceholders.SELECT_STATUS}
                    {...field}
                    value={field.value || ""}
                    items={statusOptions}
                    hasError={!!errors.status}
                    errorMessage={errors.status?.message}
                  />
                )}
              />
            </Grid>
          )}

          {shouldShowFields && (
            <>
              <Grid item xs={12} md={4}>
                <CustomLabel
                  label={ClinicianFormLabels.NPI_NUMBER}
                  isRequired={true}
                />
                <Controller
                  control={control}
                  name="npiNumber"
                  render={({ field }) => (
                    <CustomInput
                      placeholder={ClinicianFormPlaceholders.ENTER_NPI_NUMBER}
                      {...field}
                      isNumeric={true}
                      hasError={!!errors.npiNumber}
                      errorMessage={errors.npiNumber?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <CustomLabel label={ClinicianFormLabels.WORK_LOCATIONS} />
                <Controller
                  control={control}
                  name="workLocations"
                  render={({ field }) => (
                    <CustomMultiSelect
                      placeholder={
                        ClinicianFormPlaceholders.SELECT_WORK_LOCATIONS
                      }
                      value={Array.isArray(field.value) ? field.value : []}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      name={field.name}
                      options={workLocationOptions}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <CustomLabel label={ClinicianFormLabels.LANGUAGES_SPOKEN} />
                <Controller
                  control={control}
                  name="languagesSpoken"
                  render={({ field }) => (
                    <CustomMultiSelect
                      placeholder={ClinicianFormPlaceholders.SELECT_LANGUAGES}
                      value={Array.isArray(field.value) ? field.value : []}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      name={field.name}
                      options={languageOptions}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <CustomLabel
                  label={ClinicianFormLabels.SUPERVISING_CLINICIAN}
                />
                <Controller
                  control={control}
                  name="supervisingClinician"
                  render={({ field }) => (
                    <CustomSelect
                      placeholder={
                        ClinicianFormPlaceholders.SELECT_SUPERVISING_CLINICIAN
                      }
                      {...field}
                      value={field.value || ""}
                      items={displaySupervisingClinicians}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>

        <Grid
          flexDirection={"row"}
          justifyContent={"flex-end"}
          mt={2}
          sx={{
            display: "flex",
            position: "absolute",
            bottom: "0",
            right: "0",
            width: "100%",
            borderTop: "1px solid #E7E7E7",
            paddingTop: 2,
          }}
        >
          <Grid
            display="flex"
            flexDirection={"row"}
            gap={3}
            sx={{ marginBottom: "1.5vh", marginRight: "1.5vw" }}
          >
            <Grid>
              <CustomButton
                variant="outline"
                label="Cancel"
                isSubmitButton
                onClick={handleClose}
              />
            </Grid>
            <Grid>
              <CustomButton
                variant="filled"
                label="Save"
                type="submit"
                changePadding={false}
                isSubmitButton
              />
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddClinicianDialog;
