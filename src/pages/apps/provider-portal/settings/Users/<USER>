import * as yup from "yup";

export const ClinicianSchema = yup.object().shape({
  firstName: yup
    .string()
    .required("First name is required")
    .min(2, "First name must be at least 2 characters")
    .matches(
      /^[a-zA-Z0-9]+$/,
      "First name cannot contain spaces or special characters"
    ),
  lastName: yup
    .string()
    .required("Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .matches(
      /^[a-zA-Z0-9]+$/,
      "Last name cannot contain spaces or special characters"
    ),
  emailId: yup
    .string()
    .required("Email is required")
    .email("Please enter a valid email address"),
  contactNumber: yup
    .string()
    .required("Contact number is required")
    .min(12, "Contact number must be at least 10 digits")
    .max(13, "Contact number must be at most 10 digits"),
  status: yup.string().required("Status is required"),

  npiNumber: yup
    .string()
    .nullable()
    .when("role", {
      is: (roles: string[]) => {
        const rolesToShowFields = [
          "PSYCHOTHERAPIST",
          "CASE_MANAGER",
          "NAVIGATOR",
        ];
        return (
          Array.isArray(roles) &&
          roles.some((role) => rolesToShowFields.includes(role))
        );
      },
      then: (schema) =>
        schema
          .required("NPI Number is required")
          .matches(/^\d{10}$/, "Please enter a valid 10-digit NPI number"),
      otherwise: (schema) => schema.notRequired(),
    }),

  workLocations: yup.array().of(yup.string().defined()).optional(),
  languagesSpoken: yup.array().of(yup.string().defined()).optional(),
  supervisingClinician: yup.string().optional(),
  role: yup
    .array()
    .of(yup.string().defined())
    .min(1, "Role is required")
    .required("Role is required"),
});
