import { Box, Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useOutletContext } from "react-router-dom";
import DrawerBS from "../../../../../common-components/drawer-bs/custom-drawer";
import { clinicianTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import {
  capitalizeFirstLetter,
  formatPhoneNumber,
  getRolesCountAndFormat,
} from "../../../../../common-components/utils/stringUtils";
import { ViewMode } from "../../../../../constants/formConst";
import { ClinicianPayload } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  archiveClinician,
  archiveClinicianReducerAction,
} from "../../../../../redux/auth/profile/archive-clinician-reducer";
import { editClinicianStatus } from "../../../../../redux/auth/profile/edit-clinician-status";
import { getAllClinicians } from "../../../../../redux/auth/profile/get-all-clinicians";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import AddClinicianDialog from "./add-clinician-dialog";
import {
  resendInviteClientClinician,
  resendInviteClientClinicianReducerAction,
} from "../../../../../redux/auth/profile/resend-invitation-client-clinician-reducer";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";

export interface User {
  uuid: string;
  firstName: string;
  lastName: string;
  emailId: string;
  contactNumber: string;
  npiNumber: string;
  status: boolean;
  archive: boolean;
  roles: string[];
  languagesSpoken: string[];
  locationUuids: string[] | null;
  supervisorClinicianId: string | null;
  supervisorClinicianName: string | null;
  locationNames: (string | null)[];
  signature: string | null;
  twoFactorAuthentication: boolean | null;
}

export interface ClinicianOutletContext {
  userString: string;
  selectedUserCodeType: string;
}

const Clinician = () => {
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);
  const [selectedClinician, setSelectedClinician] = useState<User | null>(null);
  const [drawerOpenType, setDrawerOpenType] = useState<ViewMode>();
  const [openAddClinicianDrawer, setOpenAddClinicianDrawer] = useState(false);
  const [tableData, setTableData] = useState();

  const dispatch = useDispatch<AppDispatch>();

  const { userString, selectedUserCodeType } =
    useOutletContext<ClinicianOutletContext>();

  const { data: getAllCliniciansData, status: getAllCliniciansStatus }: any =
    useSelector((state: RootState) => state.GetAllCliniciansReducer);

  const {
    status: archiveClinicianStatus,
    error: archiveClinicianError,
    data: archiveClinicianData,
  } = useSelector((state: RootState) => state.ArchiveClinicianReducer);

  const {
    status: resendInviteClientClinicianStatus,
    data: resendInviteClientClinicianData,
  } = useSelector(
    (state: RootState) => state.ResendInviteClientClinicianReducer
  );

  const getClinicianPayload = (pageNumber: number, size: number) => {
    const payload: any = {
      size: size,
      page: pageNumber,
      searchString: userString,
    };
    if (selectedUserCodeType === "ACTIVE") {
      payload.filter = true;
    } else if (selectedUserCodeType === "IN_ACTIVE") {
      payload.filter = false;
    }
    if (selectedUserCodeType === "ARCHIVE") {
      payload.archive = true;
    }
    return payload;
  };

  const handleArchiveClinician = async (rowData: User) => {
    try {
      await dispatch(
        archiveClinician({ uuid: rowData.uuid, archiveStatus: true })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "success",
          message: archiveClinicianData || "Clinician archived successfully",
        })
      );
      dispatch(
        getAllClinicians(
          getClinicianPayload(
            page,
            parseInt(pageDisplaySize, 10)
          ) as ClinicianPayload
        )
      );
      dispatch(archiveClinicianReducerAction.resetArchiveClinicianReducer());
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "error",
          message: archiveClinicianError || "Failed to archive clinician",
        })
      );
      dispatch(archiveClinicianReducerAction.resetArchiveClinicianReducer());
    }
  };

  const handleRestoreClinician = async (rowData: User) => {
    try {
      await dispatch(
        archiveClinician({ uuid: rowData.uuid, archiveStatus: false })
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.SUCCESS,
          message: archiveClinicianData || "Clinician restored Successfully",
        })
      );
      dispatch(
        getAllClinicians(
          getClinicianPayload(
            page,
            parseInt(pageDisplaySize, 10)
          ) as ClinicianPayload
        )
      );
      dispatch(archiveClinicianReducerAction.resetArchiveClinicianReducer());
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.ERROR,
          message: archiveClinicianError || "Failed to restore clinician",
        })
      );
      dispatch(archiveClinicianReducerAction.resetArchiveClinicianReducer());
    }
  };

  const handleInvite = (rowData: User) => {
    if (rowData?.uuid) {
      dispatch(
        resendInviteClientClinician({
          uuid: rowData.uuid,
          isClient: false,
        })
      );
    }
  };

  const handleDeleteOrRestore = async (rowData: User, type: ViewMode) => {
    if (type === ViewMode.ARCHIVE) {
      await handleArchiveClinician(rowData);
    } else if (type === ViewMode.RESTORE) {
      await handleRestoreClinician(rowData);
    }
  };

  const handlePageChange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
  };

  const handleOpenDrawer = (rowData: User, type: ViewMode) => {
    setDrawerOpenType(type);
    setSelectedClinician(rowData);
    setOpenAddClinicianDrawer(true);
  };

  const handleCloseDrawer = () => {
    setOpenAddClinicianDrawer(false);
    setSelectedClinician(null);
  };

  const handleSwitch = async (flag: boolean, uuid: string) => {
    try {
      await dispatch(editClinicianStatus({ clinicianId: uuid, flag }));
      dispatch(
        getAllClinicians(
          getClinicianPayload(
            page,
            parseInt(pageDisplaySize, 10)
          ) as ClinicianPayload
        )
      );
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.SUCCESS,
          message: `Clinician status ${flag ? "activated" : "deactivated"} successfully`,
        })
      );
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: AlertSeverity.ERROR,
          message: "Failed to update clinician status",
        })
      );
    }
  };

  useEffect(() => {
    switch (getAllCliniciansStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllCliniciansStatus, dispatch]);

  useEffect(() => {
    switch (archiveClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [archiveClinicianStatus, dispatch]);

  useEffect(() => {
    switch (resendInviteClientClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: "success",
            message:
              resendInviteClientClinicianData || "Invite sent successfully",
          })
        );
        dispatch(
          resendInviteClientClinicianReducerAction.resetResendInviteClientClinicianReducer()
        );
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          resendInviteClientClinicianReducerAction.resetResendInviteClientClinicianReducer()
        );
        break;
    }
  }, [
    resendInviteClientClinicianStatus,
    dispatch,
    resendInviteClientClinicianData,
  ]);

  useEffect(() => {
    setPage(0);
  }, [userString, selectedUserCodeType]);

  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(
        getAllClinicians(
          getClinicianPayload(
            page,
            parseInt(pageDisplaySize, 10)
          ) as ClinicianPayload
        )
      );
    }, 500);
    return () => clearTimeout(timer);
  }, [dispatch, page, pageDisplaySize, userString, selectedUserCodeType]);

  useEffect(() => {
    if (getAllCliniciansData) {
      const {
        content,
        totalPages: total,
        totalElements: elements,
      } = getAllCliniciansData;
      setTotalPages(total);
      setTotalElements(elements);

      const modifiedCliniciansData = content?.map((clinician: any) => {
        let actions = [];

        if (clinician?.archive) {
          actions = [{ label: "Restore", route: "" }];
        } else {
          actions = [
            { label: "Edit", route: "" },
            { label: "Archive", route: "" },
          ];

          if (clinician?.status === null) {
            actions.unshift({ label: "Invite", route: "" });
          }
        }

        const mappedData = {
          uuid: clinician?.uuid,
          firstName: clinician?.firstName,
          lastName: clinician?.lastName,
          name: clinician?.firstName + " " + clinician?.lastName,
          emailId: clinician?.emailId,
          role:
            clinician?.roles && Array.isArray(clinician?.roles)
              ? getRolesCountAndFormat(clinician?.roles).formattedRoles
              : "",
          email: clinician?.emailId,
          contact: clinician?.contactNumber
            ? `${formatPhoneNumber(clinician.contactNumber)}`
            : "",
          npiNumber: clinician?.npiNumber || null,
          npinumber: clinician?.npiNumber,
          languagesSpoken:
            clinician?.languagesSpoken &&
            Array.isArray(clinician?.languagesSpoken)
              ? getRolesCountAndFormat(clinician?.languagesSpoken)
                  .formattedRoles
              : "",
          locationUuids:
            clinician?.locationUuids && Array.isArray(clinician?.locationUuids)
              ? clinician?.locationUuids.join(", ")
              : "",
          worklocation:
            clinician?.locationNames && Array.isArray(clinician?.locationNames)
              ? capitalizeFirstLetter(clinician?.locationNames.join(", "))
              : "",
          supervisingClinician:
            clinician?.supervisorClinicianName &&
            clinician?.supervisorClinicianName.trim() != ""
              ? clinician?.supervisorClinicianName
              : "-",
          status:
            selectedUserCodeType === "ARCHIVE"
              ? "ARCHIVE"
              : clinician?.status === null
                ? "INVITED"
                : clinician?.status
                  ? "ACTIVE"
                  : "IN_ACTIVE",
          action: actions,
        };
        return mappedData;
      });
      setTableData(modifiedCliniciansData);
    }
  }, [getAllCliniciansData]);

  return (
    <Grid>
      <Box sx={{ width: "100%" }}>
        <CustomisedTable
          headCells={clinicianTableHeaders}
          tableData={tableData}
          showCPTAndICDPagination
          setPage={handlePageChange}
          pageSize={totalPages}
          setPageDisplaySize={handlePageSizeChange}
          pageDisplaySize={pageDisplaySize}
          page={page}
          setHeight="65vh"
          handleOpenDrawer={(e) => handleOpenDrawer(e, ViewMode.EDIT)}
          handleSwitch={handleSwitch}
          handleDelete={handleDeleteOrRestore}
          handleArchiveLocation={(e) => handleArchiveClinician(e)}
          handleViewClinicalNotes={handleInvite}
        />
      </Box>

      <DrawerBS
        anchor={"right"}
        open={openAddClinicianDrawer}
        onClose={handleCloseDrawer}
        title={
          drawerOpenType === ViewMode.EDIT ? "Edit Clinician" : "Add Clinician"
        }
        drawerWidth="60vw"
      >
        <AddClinicianDialog
          handleClose={handleCloseDrawer}
          isEdit={drawerOpenType === ViewMode.EDIT}
          selectedClinician={selectedClinician}
          pageDisplaySize={pageDisplaySize}
        />
      </DrawerBS>
    </Grid>
  );
};

export default Clinician;
