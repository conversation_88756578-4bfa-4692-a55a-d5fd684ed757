import * as yup from "yup";

export const groupSettingsSchema = yup
  .object({
    clinicianName: yup.string(),
    groupName: yup.string().required("Group Name is required"),
    groupInitials: yup.string(),
    groupCptCode: yup.string().required("Group CPT Code is required"),
    groupMembers: yup
      .array(
        yup.object({
          key: yup.string().required("Group Member is required"),
          value: yup.string().required("Group Member is required"),
        })
      )
      .min(1, "At least one group member is required")
      .required("Group members are required"),
    isFamilyGroup: yup.boolean().default(false),
    billTo: yup
      .string()
      .default("")
      .when("isFamilyGroup", {
        is: true,
        then: (schema) =>
          schema.required("Bill To member must be selected for family groups"),
        otherwise: (schema) => schema.optional(),
      }),
  })
  .required();
