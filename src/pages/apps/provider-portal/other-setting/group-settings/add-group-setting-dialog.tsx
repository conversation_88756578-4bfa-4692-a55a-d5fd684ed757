import { yupResolver } from "@hookform/resolvers/yup";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  Box,
  Checkbox,
  Grid,
  IconButton,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import MultiSelectDropdown from "../../../../../common-components/custom-auto-complete/custom-new-comp";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";
import {
  tableBodyStyles,
  tableBorder,
  tableHeadStyles,
} from "../../../../../common-components/table/widgets/tablestyles";
import {
  GroupSettingsFormLabels,
  GroupSettingsFormValidationMessages,
  SettingsFormConstants,
} from "../../../../../constants/formConst";
import { apiStatus } from "../../../../../models/apiStatus";
import { getAllProcedureCodes } from "../../../../../redux/auth/fee-schedule/get-all-procedure-codes-reducer";
import {
  addGroupSettings,
  addGroupSettingsAction,
} from "../../../../../redux/auth/group-settings/add-group-settings";
import {
  EditAllGroupSettings,
  editGroupSettingsAction,
} from "../../../../../redux/auth/group-settings/edit-group-settings";
import { getAllGroupSettings } from "../../../../../redux/auth/group-settings/get-all-group-settings";
import {
  getGroupSettingsById,
  getGroupSettingsByIdAction,
} from "../../../../../redux/auth/group-settings/get-group-setting-by-id";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { getAllPatients } from "../../../../../redux/auth/patient/get-all-patients-reducer";
import { getAllSupervisingClinicians } from "../../../../../redux/auth/profile/get-all-supervising-clinicians";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { groupSettingsSchema } from "./group-settings-schema";
import type {
  GroupSettingsDialogProps,
  GroupSettingsFormData,
  SupervisingClinicianData,
  PatientData,
  ProcedureCode,
  SelectOption,
  GroupSettingsData,
  EditGroupSettingsResponse,
} from "./group-settings-const";

const AddGroupSettingsDialog = ({
  handleClose,
  isEdit,
  groupSettings,
}: GroupSettingsDialogProps) => {
  const {
    control,
    formState: { errors },
    handleSubmit,
    setValue,
    watch,
  } = useForm<GroupSettingsFormData>({
    resolver: yupResolver<GroupSettingsFormData>(groupSettingsSchema),
  });

  const dispatch = useDispatch<AppDispatch>();
  const [patientsList, setPatientsList] = useState<
    Array<{ key: string; value: string }>
  >([]);
  const [allLoadedPatients, setAllLoadedPatients] = useState<
    Array<{ key: string; value: string }>
  >([]);

  const [groupMembers, setGroupMembers] = useState<
    Array<{ key: string; value: string }>
  >([]);

  const [searchString, setSearchString] = useState("");

  const { data: getAllSupervisingCliniciansData } = useSelector(
    (state: RootState) =>
      state.GetAllSupervisingCliniciansReducer as {
        data: SupervisingClinicianData;
      }
  );

  const getAllProcedureCodesData = useSelector(
    (state: RootState) => state.GetAllProcedureCodesReducer?.data
  ) as unknown as string[];

  const AllProcedureCodes: ProcedureCode[] = Array.isArray(
    getAllProcedureCodesData
  )
    ? getAllProcedureCodesData?.map((code: string) => ({
        value: code,
        label: code,
      }))
    : [];

  const displaySupervisingClinicians: SelectOption[] = Object.entries(
    getAllSupervisingCliniciansData || {}
  ).map(([uuid, value]) => ({
    value: uuid,
    label: String(value),
  }));

  const { data: getAllPatientsDataTable } = useSelector(
    (state: RootState) => state.GetAllPatientsReducer
  );

  const {
    data: addGroupSettingsData,
    status: addGroupSettingsStatus,
    error: addGroupSettingsError,
  } = useSelector((state: RootState) => state.AddGroupSettingsReducer);

  const {
    data: editGroupSettingsData,
    status: editGroupSettingsStatus,
    error: editGroupSettingsError,
  } = useSelector((state: RootState) => state.EditGroupSettingsReducer);

  const {
    data: getGroupSettingsByIdData,
    status: getGroupSettingsByIdStatus,
    error: getGroupSettingsByIdError,
  } = useSelector((state: RootState) => state.GetGroupSettingsByIdReducer);

  const onSubmit = handleSubmit((data: GroupSettingsFormData) => {
    const setBillto = data?.isFamilyGroup ?? true;

    const payload = {
      groupName: data.groupName,
      groupInitials: data.groupInitials || "",
      cptCode: data.groupCptCode,
      clientUuids: data.groupMembers.map((member) => member.key),
      clinicianUUID: data.clinicianName || "",
      familyGroup: data.isFamilyGroup,
      billTo: setBillto ? data.billTo || "" : "",
    };
    if (isEdit && groupSettings?.uuid) {
      dispatch(EditAllGroupSettings({ uuid: groupSettings.uuid, ...payload }));
    } else {
      dispatch(addGroupSettings(payload));
    }
  });

  const isFamilyGroup = watch("isFamilyGroup");
  const selectedBillTo = watch("billTo");

  const handleBillToChange = (memberUuid: string) => {
    setValue("billTo", memberUuid);
  };

  useEffect(() => {
    if (!isEdit || !groupSettings?.uuid) {
      return;
    }
    dispatch(getGroupSettingsById({ groupId: groupSettings.uuid }));
    return () => {
      dispatch(getGroupSettingsByIdAction.resetGroupSettingsAction());
    };
  }, [isEdit, groupSettings, dispatch]);

  useEffect(() => {
    if (
      !getGroupSettingsByIdData ||
      !isEdit ||
      getGroupSettingsByIdStatus !== apiStatus.SUCCEEDED
    ) {
      return;
    }

    const data = getGroupSettingsByIdData as unknown as GroupSettingsData;
    if (!data.groupName || !data.cptCode) {
      return;
    }

    const transformed = Object.entries(data.uuidStringMap || {}).map(
      ([key, value]) => ({
        key,
        value: String(value),
      })
    );
    setValue("groupName", data.groupName);
    setValue("groupInitials", data.groupInitials || "");
    setValue("groupCptCode", data.cptCode);
    setValue("groupMembers", transformed);
    setValue(
      "isFamilyGroup",
      Boolean(data.familyGroup === true ? true : false)
    );
    setValue("billTo", data.billTo || "");
    setValue("clinicianName", data.clinicianUUID || "");
    setGroupMembers(transformed);
  }, [getGroupSettingsByIdData, getGroupSettingsByIdStatus, isEdit, setValue]);

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  useEffect(() => {
    const response = getAllPatientsDataTable?.content;
    const insurancepayload = Array.isArray(response) ? response : [];

    const insuranceOptionsPayload =
      insurancepayload?.map((ins: PatientData) => ({
        key: ins?.uuid,
        value: `${ins?.firstName} ${ins?.lastName}`,
      })) || [];

    setPatientsList(insuranceOptionsPayload);

    setAllLoadedPatients((prevAll) => {
      const existingKeys = new Set(prevAll.map((p) => p.key));
      const newPatients = insuranceOptionsPayload.filter(
        (p) => !existingKeys.has(p.key)
      );
      return [...prevAll, ...newPatients];
    });
  }, [getAllPatientsDataTable]);

  useEffect(() => {
    dispatch(
      getAllPatients({
        size: 10,
        page: 0,
        searchString: searchString,
        status: "",
        insurance: "",
        clinicianId: "",
        filter: "name",
      })
    );
  }, [dispatch, searchString]);

  useEffect(() => {
    const response = getAllPatientsDataTable?.content;
    const insurancepayload = Array.isArray(response) ? response : [];

    const insuranceOptionsPayload =
      insurancepayload?.map((ins: PatientData) => ({
        key: ins?.uuid,
        value: `${ins?.firstName} ${ins?.lastName}`,
      })) || [];

    setPatientsList(insuranceOptionsPayload);
  }, [getAllPatientsDataTable]);

  useEffect(() => {
    switch (addGroupSettingsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              typeof addGroupSettingsData === "string"
                ? addGroupSettingsData
                : GroupSettingsFormValidationMessages.GROUP_SETTING_ADDED_SUCCESSFULLY,
          })
        );
        dispatch(getAllGroupSettings({ size: 10, page: 0, searchString: "" }));
        dispatch(addGroupSettingsAction.resetGroupSettingsAction());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              addGroupSettingsError ??
              GroupSettingsFormValidationMessages.GROUP_SETTING_ADDED_FAILED,
          })
        );
        dispatch(addGroupSettingsAction.resetGroupSettingsAction());
        break;
    }
  }, [
    addGroupSettingsStatus,
    dispatch,
    addGroupSettingsError,
    handleClose,
    addGroupSettingsData,
  ]);

  useEffect(() => {
    switch (editGroupSettingsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        const successMessage =
          editGroupSettingsData &&
          typeof editGroupSettingsData === "object" &&
          "message" in editGroupSettingsData
            ? (editGroupSettingsData as EditGroupSettingsResponse).message
            : GroupSettingsFormValidationMessages.GROUP_SETTING_UPDATED_SUCCESSFULLY;
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: successMessage,
          })
        );
        dispatch(getAllGroupSettings({ size: 10, page: 0, searchString: "" }));
        dispatch(editGroupSettingsAction.resetGroupSettingsAction());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              editGroupSettingsError ??
              GroupSettingsFormValidationMessages.GROUP_SETTING_UPDATED_FAILED,
          })
        );
        dispatch(editGroupSettingsAction.resetGroupSettingsAction());
        break;
    }
  }, [
    editGroupSettingsStatus,
    dispatch,
    editGroupSettingsError,
    handleClose,
    editGroupSettingsData,
  ]);

  useEffect(() => {
    switch (getGroupSettingsByIdStatus) {
      case apiStatus.SUCCEEDED:
        dispatch(getGroupSettingsByIdAction.resetGroupSettingsAction());
        break;
      case apiStatus.FAILED:
        dispatch(getGroupSettingsByIdAction.resetGroupSettingsAction());
        break;
    }
  }, [
    getGroupSettingsByIdStatus,
    dispatch,
    getGroupSettingsByIdError,
    handleClose,
  ]);

  useEffect(() => {
    dispatch(getAllProcedureCodes());
  }, [dispatch]);

  return (
    <Box>
      <form onSubmit={onSubmit}>
        <Grid mb={6}>
          <Grid
            container
            spacing={2}
            sx={{ display: "flex", flexDirection: "row" }}
          >
            <Grid item xs={4}>
              <CustomLabel
                label={GroupSettingsFormLabels.CLINICIAN_NAME}
                isRequired
              />
              <Controller
                control={control}
                name="clinicianName"
                render={({ field }) => (
                  <CustomSelect
                    placeholder={GroupSettingsFormLabels.SELECT_CLINICIAN_NAME}
                    {...field}
                    value={field.value}
                    items={displaySupervisingClinicians}
                    hasError={!!errors.clinicianName}
                    errorMessage={errors.clinicianName?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={4}>
              <CustomLabel
                label={GroupSettingsFormLabels.GROUP_NAME}
                isRequired
              />
              <Controller
                control={control}
                name="groupName"
                render={({ field }) => (
                  <CustomInput
                    placeholder={GroupSettingsFormLabels.GROUP_NAME_PLACEHOLDER}
                    {...field}
                    value={field.value}
                    hasError={!!errors.groupName}
                    errorMessage={errors.groupName?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={4}>
              <CustomLabel label={GroupSettingsFormLabels.GROUP_INITIALS} />
              <Controller
                control={control}
                name="groupInitials"
                render={({ field }) => (
                  <CustomInput
                    placeholder={
                      GroupSettingsFormLabels.GROUP_INITIALS_PLACEHOLDER
                    }
                    {...field}
                    value={field.value}
                    hasError={!!errors.groupInitials}
                    errorMessage={errors.groupInitials?.message}
                  />
                )}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} pt={2}>
            <Grid item xs={12}>
              <CustomLabel
                label={GroupSettingsFormLabels.GROUP_CPT_CODE}
                isRequired
              />
              <Controller
                control={control}
                name="groupCptCode"
                render={({ field }) => (
                  <CustomSelect
                    placeholder={
                      GroupSettingsFormLabels.GROUP_CPT_CODE_PLACEHOLDER
                    }
                    {...field}
                    value={field.value}
                    items={AllProcedureCodes}
                    hasError={!!errors.groupCptCode}
                    errorMessage={errors.groupCptCode?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                control={control}
                name="groupMembers"
                render={({ field, fieldState }) => (
                  <MultiSelectDropdown
                    options={patientsList}
                    allLoadedOptions={allLoadedPatients}
                    selectedValues={field.value || []}
                    setSelectedValues={(selectedValue) => {
                      field.onChange(selectedValue);
                      setGroupMembers(selectedValue);
                    }}
                    handleInputVal={(text: string) => {
                      setSearchString(text);
                    }}
                    label={GroupSettingsFormLabels.GROUP_MEMBERS}
                    isRequired
                    name="groupMembers"
                    hasError={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Grid
            container
            spacing={2}
            pt={3}
            alignItems={"center"}
            pl={1}
            mb={1}
          >
            <Grid>
              <Controller
                name="isFamilyGroup"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    {...field}
                    checked={field.value === true ? true : false}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                )}
              />
            </Grid>
            <Grid>
              <Typography>Family Group</Typography>
            </Grid>
          </Grid>
          <TableContainer
            sx={{
              ...tableBorder,
              borderRadius: "var(--1, 8px)",
              maxHeight: "45vh",
              overflowY: "scroll",
            }}
          >
            <Table sx={{ minWidth: "100%" }} aria-labelledby="tableTitle">
              <TableHead>
                <TableRow
                  sx={{
                    background: "#E9E9E9",
                  }}
                >
                  <TableCell
                    sx={{
                      ...tableHeadStyles,
                      padding: "10px 18px",
                      textTransform: "none",
                      position: "sticky",
                      top: 0,
                      zIndex: 1,
                      background: "#E9E9E9",
                    }}
                  >
                    <Typography variant="bodyMedium4">
                      {GroupSettingsFormLabels.GROUP_MEMBERS}
                    </Typography>
                  </TableCell>
                  {isFamilyGroup && (
                    <TableCell
                      sx={{
                        ...tableHeadStyles,
                        padding: "10px 18px",
                        textTransform: "none",
                        position: "sticky",
                        top: 0,
                        zIndex: 1,
                        background: "#E9E9E9",
                      }}
                    >
                      <Typography variant="bodyMedium4">
                        {GroupSettingsFormLabels.BILL_TO}
                      </Typography>
                    </TableCell>
                  )}
                  <TableCell
                    sx={{
                      ...tableHeadStyles,
                      padding: "10px 18px",
                      textTransform: "none",
                      position: "sticky",
                      top: 0,
                      zIndex: 1,
                      background: "#E9E9E9",
                    }}
                  >
                    <Typography variant="bodyMedium4">
                      {GroupSettingsFormLabels.ACTION}
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {groupMembers?.map((member) => {
                  return (
                    <TableRow
                      hover
                      role="checkbox"
                      tabIndex={-1}
                      key={member.key}
                      sx={{
                        background: "white",
                      }}
                    >
                      <TableCell sx={tableBodyStyles}>{member.value}</TableCell>
                      {isFamilyGroup && (
                        <TableCell sx={tableBodyStyles}>
                          <Radio
                            checked={selectedBillTo === member.key}
                            onChange={() => handleBillToChange(member.key)}
                          />
                        </TableCell>
                      )}
                      <TableCell sx={tableBodyStyles}>
                        <IconButton
                          onClick={() => {
                            setGroupMembers(
                              groupMembers.filter((m) => m.key !== member.key)
                            );
                            const currentFormValues =
                              control._formValues.groupMembers || [];
                            const updatedMembers = currentFormValues.filter(
                              (val: { key: string; value: string }) =>
                                val.key !== member.key
                            );
                            setValue("groupMembers", updatedMembers);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
        <Grid
          flexDirection={"row"}
          justifyContent={"flex-end"}
          mt={2}
          sx={{
            display: "flex",
            position: "absolute",
            bottom: "0",
            right: "0",
            width: "100%",
            borderTop: "1px solid #E7E7E7",
            paddingTop: 2,
          }}
        >
          <Grid
            display="flex"
            flexDirection={"row"}
            gap={3}
            sx={{ marginBottom: "1.5vh", marginRight: "1.5vw" }}
          >
            <Grid>
              <CustomButton
                variant="outline"
                label={SettingsFormConstants.CANCEL}
                isSubmitButton
                onClick={handleClose}
              />
            </Grid>
            <Grid>
              <CustomButton
                variant="filled"
                label={SettingsFormConstants.SAVE}
                type="submit"
                changePadding={false}
                isSubmitButton
              />
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddGroupSettingsDialog;
