import { Grid2 } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import DrawerBS from "../../../../../common-components/drawer-bs/custom-drawer";
import { groupSettingsTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import { getRolesCountAndFormat } from "../../../../../common-components/utils/stringUtils";
import {
  GroupSettingsFormLabels,
  ViewMode,
} from "../../../../../constants/formConst";
import { getAllGroupSettings } from "../../../../../redux/auth/group-settings/get-all-group-settings";
import { AppDispatch, RootState } from "../../../../../redux/store";
import AddGroupSettingsDialog from "./add-group-setting-dialog";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { apiStatus } from "../../../../../models/apiStatus";
import {
  deleteGroupSettings,
  deleteGroupSettingsAction,
} from "../../../../../redux/auth/group-settings/delete-group-setting";
import { AlertSeverity } from "../../../../../common-components/alert/alert";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { GroupSettingsFormValidationMessages } from "../../../../../constants/formConst";
import type {
  GroupSettingsDialogProps,
  TableRowData,
  Group,
} from "./group-settings-const";

const GroupSettings = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [openGroupSettingsDrawer, setOpenGroupSettingsDrawer] = useState(false);
  const [drawerOpenType, setDrawerOpenType] = useState<ViewMode>();
  const [selectedGroupSettings, setSelectedGroupSettings] = useState<
    GroupSettingsDialogProps | undefined
  >(undefined);
  const [tableData, setTableData] = useState<TableRowData[]>([]);
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);

  const {
    data: deleteGroupSettingsData,
    status: deleteGroupSettingsStatus,
    error: deleteGroupSettingsError,
  } = useSelector((state: RootState) => state.DeleteGroupSettingsReducer);

  const { data: getAllGroupSettingsData, status: getAllGroupSettingsStatus } =
    useSelector((state: RootState) => state.GetAllGroupSettingsReducer);

  const handleOpenDrawer = (rowData: TableRowData, type: ViewMode) => {
    setDrawerOpenType(type);
    const dialogProps: GroupSettingsDialogProps = {
      uuid: rowData.uuid,
      groupName: rowData.groupName,
      groupCPTCode: rowData.groupCPTCode,
    };
    setSelectedGroupSettings(dialogProps);
    setOpenGroupSettingsDrawer(true);
  };

  const getGroupSettingsPayload = (pageNumber: number, size: number) => {
    return {
      size: size,
      page: pageNumber,
      searchString: "",
    };
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
  };

  const handleCloseDrawer = () => {
    setOpenGroupSettingsDrawer(false);
    setSelectedGroupSettings(undefined);
  };

  const handlePageChange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
  };

  const handleDeleteGroupsettings = (rowData: TableRowData) => {
    dispatch(deleteGroupSettings({ groupId: rowData.uuid }));
  };

  useEffect(() => {
    switch (getAllGroupSettingsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllGroupSettingsStatus, dispatch]);

  useEffect(() => {
    if (getAllGroupSettingsData) {
      const { totalPages: total, totalElements: elements } =
        getAllGroupSettingsData;
      setTotalPages(total);
      setTotalElements(elements);
      const modifiedTableData: TableRowData[] = (
        getAllGroupSettingsData.content as unknown as Group[]
      )?.map((item: Group) => ({
        groupName: item?.groupInitials
          ? `${item.groupInitials} (${item.groupName})`
          : item?.groupName || "",
        groupCPTCode: item?.cptCode,
        groupMember:
          item?.clientNames && Array.isArray(item?.clientNames)
            ? getRolesCountAndFormat(item?.clientNames).formattedRoles
            : "",
        action: [
          { label: "Edit", route: "" },
          { label: "Delete", route: "" },
        ],
        uuid: item.uuid,
      }));
      setTableData(modifiedTableData);
    }
  }, [getAllGroupSettingsData]);

  useEffect(() => {
    switch (deleteGroupSettingsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              typeof deleteGroupSettingsData === "string"
                ? deleteGroupSettingsData
                : GroupSettingsFormValidationMessages.GROUP_SETTING_DELETED_SUCCESSFULLY,
          })
        );
        dispatch(getAllGroupSettings({ size: 10, page: 0, searchString: "" }));
        dispatch(deleteGroupSettingsAction.resetGroupSettingsAction());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              deleteGroupSettingsError ??
              GroupSettingsFormValidationMessages.GROUP_SETTING_DELETED_FAILED,
          })
        );
        dispatch(deleteGroupSettingsAction.resetGroupSettingsAction());
        break;
    }
  }, [
    deleteGroupSettingsStatus,
    dispatch,
    deleteGroupSettingsError,
    deleteGroupSettingsData,
  ]);

  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(
        getAllGroupSettings(
          getGroupSettingsPayload(page, parseInt(pageDisplaySize, 10))
        )
      );
    }, 500);
    return () => clearTimeout(timer);
  }, [dispatch, page, pageDisplaySize]);

  return (
    <Grid2>
      <CustomisedTable
        headCells={groupSettingsTableHeaders}
        tableData={tableData}
        showCPTAndICDPagination
        pageSize={totalPages}
        setPage={handlePageChange}
        page={page}
        setPageDisplaySize={handlePageSizeChange}
        pageDisplaySize={pageDisplaySize}
        handleOpenDrawer={(e: TableRowData) =>
          handleOpenDrawer(e, ViewMode.EDIT)
        }
        setHeight="65vh"
        handleDelete={handleDeleteGroupsettings}
      />
      <DrawerBS
        anchor={"right"}
        open={openGroupSettingsDrawer}
        drawerWidth="60vw"
        onClose={handleCloseDrawer}
        title={
          drawerOpenType === ViewMode.EDIT
            ? GroupSettingsFormLabels.EDIT_GROUP_SETTINGS
            : GroupSettingsFormLabels.ADD_GROUP_SETTINGS
        }
      >
        <AddGroupSettingsDialog
          handleClose={handleCloseDrawer}
          isEdit={drawerOpenType === ViewMode.EDIT}
          groupSettings={selectedGroupSettings}
          uuid={""}
          groupName={""}
          groupCPTCode={""}
        />
      </DrawerBS>
    </Grid2>
  );
};

export default GroupSettings;
