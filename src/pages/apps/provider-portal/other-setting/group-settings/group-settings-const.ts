export interface GroupSettingsData {
  uuid: string;
  groupName: string;
  groupInitials: string;
  cptCode: string;
  clinicianUUID: string;
  familyGroup: boolean;
  billTo: string;
  uuidStringMap: Record<string, string>;
}

export interface AddGroupSettingsDialogProps {
  handleClose: () => void;
  isEdit?: boolean;
  groupSettings?: {
    uuid: string;
    groupName?: string;
    groupCPTCode?: string;
    [key: string]: unknown;
  };
}

export interface GroupSettingsFormData {
  clinicianName: string;
  groupName: string;
  groupInitials?: string;
  groupCptCode: string;
  groupMembers: Array<{ key: string; value: string }>;
  isFamilyGroup: boolean;
  billTo?: string;
}

export interface SupervisingClinicianData {
  [key: string]: string;
}

export interface PatientData {
  uuid: string;
  firstName: string;
  lastName: string;
  description: string;
}

export interface ProcedureCode {
  value: string;
  label: string;
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface EditGroupSettingsResponse {
  message?: string;
  [key: string]: unknown;
}

export interface Group {
  uuid: string;
  clinicianUUID: string;
  groupName: string;
  groupInitials: string;
  cptCode: string;
  clientUuids: string[] | null;
  familyGroup: boolean;
  archive: boolean;
  billTo: string | null;
  clientNames: string[];
  uuidStringMap: Record<string, string> | null;
}

export interface TableRowData {
  groupName: string;
  groupCPTCode: string;
  groupMember: string;
  action: Array<{ label: string; route: string }>;
  uuid: string;
}

export interface GroupSettingsDialogProps {
  uuid: string;
  groupName: string;
  groupCPTCode: string;
  [key: string]: any;
}

export interface Sort {
  sorted: boolean;
  empty: boolean;
  unsorted: boolean;
}

export interface Pageable {
  pageNumber: number;
  pageSize: number;
  sort: Sort;
  offset: number;
  paged: boolean;
  unpaged: boolean;
}

export interface GroupApiResponseData {
  content: Group[];
  pageable: Pageable;
  totalElements: number;
  totalPages: number;
  last: boolean;
  size: number;
  number: number;
  sort: Sort;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
}

export interface GroupApiResponse {
  date: string;
  code: string;
  message: string | null;
  data: GroupApiResponseData;
  path: string;
  requestId: string;
  version: string;
}
