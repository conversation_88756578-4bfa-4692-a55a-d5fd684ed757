import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import WestOutlinedIcon from "@mui/icons-material/WestOutlined";
import { Box, Grid, Tab, Tabs, Typography } from "@mui/material";
import { SelectChangeEvent } from "@mui/material/Select";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import CustomInput from "../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../common-components/custom-select/customSelect";
import DrawerBS from "../../../../common-components/drawer-bs/custom-drawer";
import {
  OtherSettingsTabs,
  SettingsFormConstants,
} from "../../../../constants/formConst";
import { tabLabel, tabSx } from "../../../../constants/tabs-widget";
import { apiStatus } from "../../../../models/apiStatus";
import { RootState } from "../../../../redux/store";
import AddFeeScheduleDialog from "./fee-schedule/add-fee-schedule-dialog";
import AddGroupSettingsDialog from "./group-settings/add-group-setting-dialog";

export const CodeTypeContext = React.createContext<{
  searchString: string;
  codeType: string | null;
} | null>(null);

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const OtherSettingsMasterTabs = () => {
  const [value, setValue] = React.useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const tabRoutes = Object.values(OtherSettingsTabs);
  const [addFeeScheduleDialog, setAddFeeScheduleDialog] = useState(false);
  const [selectedFeeScheduleCodeType, setSelectedFeeScheduleCodeType] =
    useState<string | null>("ALL");
  const [feeScheduleString, setFeeScheduleString] = useState("");
  const [addGroupSettingsDialog, setAddGroupSettingsDialog] = useState(false);
  const { status: archiveFeeScheduleStatus } = useSelector(
    (state: RootState) => state.ArchiveFeeScheduleReducer
  );

  const pageDisplaySize = 10;

  const codeTypeOptions = [
    { value: "CPT", label: "CPT" },
    { value: "SELF_PAY", label: "Self Pay" },
    { value: "ACTIVE", label: "Active" },
    { value: "IN_ACTIVE", label: "Inactive" },
    { value: "ARCHIVE", label: "Archive" },
    { value: "ALL", label: "All" },
  ];

  useEffect(() => {
    if (location.pathname.endsWith("settings-tabs")) {
      navigate("availability");
    }
    const currentPath = location.pathname.split("/").pop();
    const tabIndex = tabRoutes.findIndex(
      (tab) => tab.toLowerCase() === currentPath
    );
    if (tabIndex !== -1) {
      setValue(tabIndex);
    }
  }, [location.pathname, navigate, tabRoutes]);

  useEffect(() => {
    if (archiveFeeScheduleStatus === apiStatus.SUCCEEDED) {
      setSelectedFeeScheduleCodeType("ALL");
    }
  }, [archiveFeeScheduleStatus]);

  const handleFeeScheduleCodeTypeChange = (e: SelectChangeEvent<string>) => {
    setSelectedFeeScheduleCodeType(e.target.value);
  };

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    navigate(tabRoutes[newValue].toLowerCase());
  };

  return (
    <Box>
      <Box display={"flex"} gap={8}>
        <Box
          display={"flex"}
          flexDirection={"row"}
          gap={1}
          alignItems={"center"}
          ml={1}
        >
          <Box sx={{ cursor: "pointer" }}>
            <WestOutlinedIcon
              onClick={() => navigate("/admin/settings-tabs")}
            />
          </Box>

          <Typography variant="titleMediumProfileBold">
            {SettingsFormConstants.OTHER_SETTINGS}
          </Typography>
        </Box>
        <Box
          display="flex"
          flexDirection={"row"}
          justifyContent={"space-between"}
          width={"100%"}
          alignItems="center"
        >
          <Box sx={{ ...tabSx, ml: 0.5 }}>
            <Tabs value={value} onChange={handleChange}>
              {tabRoutes.map((item: string, index: number) => (
                <Tab
                  key={index}
                  label={item}
                  {...a11yProps(index)}
                  sx={tabLabel}
                />
              ))}
            </Tabs>
          </Box>
          <Grid mr={1}>
            {value === 1 && (
              <Grid display={"flex"} flexDirection={"row"} gap={2}>
                <Grid>
                  <CustomInput
                    value={feeScheduleString}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setFeeScheduleString(e.target.value)
                    }
                    placeholder={"Search Fee Schedule"}
                    bgWhite={true}
                    showIcon={<SearchIcon />}
                  />
                </Grid>
                <Grid width={"8vw"}>
                  <CustomSelect
                    placeholder={"Code Type"}
                    value={selectedFeeScheduleCodeType || ""}
                    items={codeTypeOptions}
                    backgroundColor={"#FFF"}
                    onChange={handleFeeScheduleCodeTypeChange}
                  />
                </Grid>
                <Grid>
                  <CustomButton
                    variant="filled"
                    label={SettingsFormConstants.ADD_NEW_FEE_SCHEDULE}
                    startIcon={<AddIcon />}
                    onClick={() => setAddFeeScheduleDialog(true)}
                    isSubmitButtonTwo
                  />
                </Grid>
              </Grid>
            )}
            {value === 3 && (
            <Grid display={"flex"} flexDirection={"row"} gap={2}>
              <Grid>
                <CustomButton
                  variant="filled"
                  label={SettingsFormConstants.ADD_NEW_GROUP_SETTINGS}
                  startIcon={<AddIcon />}
                  onClick={() => setAddGroupSettingsDialog(true)}
                  isSubmitButtonTwo
                />
              </Grid>
            </Grid>
          )}
          </Grid>
        </Box>
      </Box>
      <Box p={1}>
        <CodeTypeContext.Provider
          value={{
            searchString: feeScheduleString,
            codeType: selectedFeeScheduleCodeType,
          }}
        >
          <Outlet />
        </CodeTypeContext.Provider>
      </Box>
      <DrawerBS
        title={SettingsFormConstants.ADD_FEE_SCHEDULE}
        open={addFeeScheduleDialog}
        onClose={() => setAddFeeScheduleDialog(false)}
        anchor={"right"}
        drawerWidth="30vw"
      >
        <AddFeeScheduleDialog
          handleClose={() => setAddFeeScheduleDialog(false)}
          pageDisplaySize={pageDisplaySize.toString()}
        />
      </DrawerBS>
      <DrawerBS
        title={SettingsFormConstants.ADD_NEW_GROUP_SETTINGS}
        open={addGroupSettingsDialog}
        onClose={() => setAddGroupSettingsDialog(false)}
        anchor={"right"}
        drawerWidth="50vw"
      >
        <AddGroupSettingsDialog
          handleClose={() => setAddGroupSettingsDialog(false)}
          isEdit={false}
          uuid={""}
          groupName={""}
          groupCPTCode={""}
        />
      </DrawerBS>
    </Box>
  );
};

export default OtherSettingsMasterTabs;
