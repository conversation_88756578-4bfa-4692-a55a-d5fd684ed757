import { Box, Grid } from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import DrawerBS from "../../../../../common-components/drawer-bs/custom-drawer";
import { feeScheduleTableHeaders } from "../../../../../common-components/headers/all-headers";
import CustomisedTable from "../../../../../common-components/table/table";
import { ViewMode } from "../../../../../constants/formConst";
import { ProcedureCode } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { addFeeScheduleReducerAction } from "../../../../../redux/auth/fee-schedule/add-fee-schedule-reducer";
import {
  archiveFeeSchedule,
  archiveFeeScheduleReducerAction,
} from "../../../../../redux/auth/fee-schedule/archieve-fee-schedule";
import { editFeeScheduleReducerAction } from "../../../../../redux/auth/fee-schedule/edit-fee-schedule-reducer";
import { editFeeScheduleStatus } from "../../../../../redux/auth/fee-schedule/edit-fee-schedule-status";
import { getAllFeeSchedule } from "../../../../../redux/auth/fee-schedule/get-all-fee-schedule-reducer";
import { getFeeScheduleByIdAction } from "../../../../../redux/auth/fee-schedule/get-fee-schedule-by-id-reducer";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { CodeTypeContext } from "../other-settings-master-tabs";
import AddFeeScheduleDialog from "./add-fee-schedule-dialog";
import { AlertSeverity } from "../../../../../common-components/alert/alert";

interface FeeScheduleData {
  uuid: string;
  procedureCode: string;
  rate: string;
  codeType: string;
}

const FeeSchedule = () => {
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);
  const [selectedFeeSchedule, setSelectedFeeSchedule] =
    useState<FeeScheduleData | null>(null);
  const [drawerOpenType, setDrawerOpenType] = useState<ViewMode>();
  const [openFeeScheduleDrawer, setOpenFeeScheduleDrawer] = useState(false);
  const [tableData, setTableData] = useState<FeeScheduleData[]>();
  const context = useContext(CodeTypeContext);

  const dispatch = useDispatch<AppDispatch>();

  const { data: getAllFeeScheduleData, status: getAllFeeScheduleStatus } =
    useSelector((state: RootState) => state.GetAllFeeScheduleReducer);

  const {
    status: archiveFeeScheduleStatus,
    error: archiveFeeScheduleError,
    data: archiveFeeScheduleData,
  } = useSelector((state: RootState) => state.ArchiveFeeScheduleReducer);

  const { status: editFeeScheduleStatusState } = useSelector(
    (state: RootState) => state.EditFeeScheduleStatusReducer
  );

  const fetchFeeScheduleData = () => {
    if (!context) return;
    const { codeType, searchString } = context;

    let payload: {
      codeType?: string;
      page: number;
      pageSize: number;
      searchString?: string;
      filter?: boolean;
    } = {
      page,
      pageSize: parseInt(pageDisplaySize, 10),
      searchString,
    };

    if (codeType === "ARCHIVE") {
      payload.codeType = "ARCHIVE";
    } else if (codeType === "ACTIVE") {
      payload.filter = true;
    } else if (codeType === "IN_ACTIVE") {
      payload.filter = false;
    } else if (codeType === "ALL") {
      payload.codeType = "";
    } else {
      payload.codeType = codeType || undefined;
    }

    dispatch(getAllFeeSchedule(payload));
  };

  const handleArchiveFeeSchedule = async (rowData: FeeScheduleData) => {
    dispatch(archiveFeeSchedule({ uuid: rowData.uuid, flag: true }));
  };

  const handleRestoreFeeSchedule = async (rowData: FeeScheduleData) => {
    dispatch(archiveFeeSchedule({ uuid: rowData.uuid, flag: false }));
  };

  const handleDeleteOrRestore = async (rowData: any, type: ViewMode) => {
    if (type === ViewMode.ARCHIVE) {
      await handleArchiveFeeSchedule(rowData);
    } else if (type === ViewMode.RESTORE) {
      await handleRestoreFeeSchedule(rowData);
    }
  };

  const handlePageChange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
    setTimeout(() => fetchFeeScheduleData(), 0);
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
    setTimeout(() => fetchFeeScheduleData(), 0);
  };

  const handleOpenDrawer = (rowData: FeeScheduleData, type: ViewMode) => {
    dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
    dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());

    setDrawerOpenType(type);
    setSelectedFeeSchedule(rowData);
    setOpenFeeScheduleDrawer(true);
  };

  const handleCloseDrawer = () => {
    dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
    dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());
    dispatch(getFeeScheduleByIdAction.resetFeeScheduleByIdAction());

    setOpenFeeScheduleDrawer(false);
    setSelectedFeeSchedule(null);
  };

  const handleSwitch = async (flag: boolean, uuid: string) => {
    try {
      await dispatch(editFeeScheduleStatus({ uuid, flag }));
      fetchFeeScheduleData();
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "success",
          message: `Fee schedule status ${flag ? "activated" : "deactivated"} successfully`,
        })
      );
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          severity: "error",
          message: "Failed to update fee schedule status",
        })
      );
    }
  };

  useEffect(() => {
    setPage(0);
  }, [context?.searchString, context?.codeType]);

  useEffect(() => {
    switch (getAllFeeScheduleStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllFeeScheduleStatus, dispatch]);

  useEffect(() => {
    switch (archiveFeeScheduleStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.SUCCESS,
            message:
              archiveFeeScheduleData || "Fee Schedule updated successfully",
          })
        );
        fetchFeeScheduleData();
        dispatch(
          archiveFeeScheduleReducerAction.resetArchiveFeeScheduleReducer()
        );
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.ERROR,
            message: archiveFeeScheduleError || "Failed to update fee schedule",
          })
        );
        dispatch(
          archiveFeeScheduleReducerAction.resetArchiveFeeScheduleReducer()
        );
        break;
    }
  }, [
    archiveFeeScheduleStatus,
    archiveFeeScheduleData,
    archiveFeeScheduleError,
    dispatch,
  ]);

  useEffect(() => {
    switch (editFeeScheduleStatusState) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [editFeeScheduleStatusState, dispatch]);

  useEffect(() => {
    const timer = setTimeout(() => fetchFeeScheduleData(), 500);
    return () => clearTimeout(timer);
  }, [dispatch, context, pageDisplaySize, page]);

  useEffect(() => {
    if (getAllFeeScheduleData) {
      const {
        content,
        totalPages: total,
        totalElements: elements,
      } = getAllFeeScheduleData;
      setTotalPages(total);
      setTotalElements(elements);

      const actions =
        context?.codeType === "ARCHIVE"
          ? [{ label: "Restore", route: "" }]
          : [
              { label: "Edit", route: "" },
              { label: "Archive", route: "" },
            ];

      const modifiedTableData = content?.map((item: ProcedureCode) => ({
        ...item,
        rate: `$ ${item.rate}`,
        codeType: item?.codeType === "CPT" ? "CPT" : "Self Pay",
        status: item.active ? "ACTIVE" : "IN_ACTIVE",
        action: actions,
      }));
      setTableData(modifiedTableData);
    }
  }, [getAllFeeScheduleData, context]);

  return (
    <Grid>
      <Box sx={{ width: "100%" }}>
        <CustomisedTable
          headCells={feeScheduleTableHeaders}
          tableData={tableData}
          showCPTAndICDPagination
          setPage={handlePageChange}
          pageSize={totalPages}
          setPageDisplaySize={handlePageSizeChange}
          pageDisplaySize={pageDisplaySize}
          page={page}
          setHeight="65vh"
          handleOpenDrawer={(e) => handleOpenDrawer(e, ViewMode.EDIT)}
          handleSwitch={handleSwitch}
          handleDelete={handleDeleteOrRestore}
          handleArchiveLocation={(e) => handleArchiveFeeSchedule(e)}
        />
      </Box>
      <DrawerBS
        anchor={"right"}
        open={openFeeScheduleDrawer}
        drawerWidth="40vw"
        onClose={handleCloseDrawer}
        title={
          drawerOpenType === ViewMode.EDIT
            ? "Edit Fee Schedule"
            : "Add Fee Schedule"
        }
      >
        <AddFeeScheduleDialog
          handleClose={handleCloseDrawer}
          isEdit={drawerOpenType === ViewMode.EDIT}
          selectedFeeSchedule={
            selectedFeeSchedule
              ? { originalFeeSchedule: selectedFeeSchedule }
              : undefined
          }
          pageDisplaySize={pageDisplaySize}
        />
      </DrawerBS>
    </Grid>
  );
};

export default FeeSchedule;
