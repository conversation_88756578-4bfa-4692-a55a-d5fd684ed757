import { Box, Grid } from "@mui/material";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import {
  FeeScheduleFormLabels,
  FeeScheduleFormPlaceholders,
  LocationFormLabels,
  LocationFormPlaceholders,
  SettingsFormConstants,
} from "../../../../../constants/formConst";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FeeScheduleSchema } from "./fee-schedule-schema";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../redux/store";
import { addFeeSchedule } from "../../../../../redux/auth/fee-schedule/add-fee-schedule-reducer";
import { AppDispatch } from "../../../../../redux/store";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { getAllProcedureCodes } from "../../../../../redux/auth/fee-schedule/get-all-procedure-codes-reducer";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AlertSeverity } from "../../../../../common-components/alert/alert";
import {
  editFeeSchedule,
  editFeeScheduleReducerAction,
} from "../../../../../redux/auth/fee-schedule/edit-fee-schedule-reducer";
import { addFeeScheduleReducerAction } from "../../../../../redux/auth/fee-schedule/add-fee-schedule-reducer";
import { getAllFeeSchedule } from "../../../../../redux/auth/fee-schedule/get-all-fee-schedule-reducer";
import {
  getFeeScheduleById,
  getFeeScheduleByIdAction,
  getFeeScheduleByIdState,
} from "../../../../../redux/auth/fee-schedule/get-fee-schedule-by-id-reducer";
import { ProcedureCode } from "src/models/all-const";

interface SelectedFeeSchedule {
  originalFeeSchedule: {
    uuid?: string;
    procedureCode: string;
    rate: string;
    codeType: string;
  };
}

interface AddFeeScheduleDialogProps {
  handleClose: () => void;
  isEdit?: boolean;
  selectedFeeSchedule?: SelectedFeeSchedule;
  pageDisplaySize: string;
}

const AddFeeScheduleDialog = ({
  handleClose,
  isEdit,
  selectedFeeSchedule,
  pageDisplaySize,
}: AddFeeScheduleDialogProps) => {
  const {
    control,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm({
    defaultValues: {
      procedureCode: "",
      rate: "",
      codeType: "",
      status: "active",
    },
    resolver: yupResolver(FeeScheduleSchema),
  });

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
    dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());

    return () => {
      dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
      dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());
    };
  }, [dispatch]);

  const codeTypeOptions = [
    { value: "CPT", label: "CPT" },
    { value: "SELF_PAY", label: "Self Pay" },
  ];

  const statusOptions = [
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
  ];

  const getAllProcedureCodesData = useSelector(
    (state: RootState) => state.GetAllProcedureCodesReducer?.data
  );

  const addFeeScheduleStatus = useSelector(
    (state: RootState) => state.AddFeeScheduleReducer?.status
  );

  const addFeeScheduleData = useSelector(
    (state: RootState) => state.AddFeeScheduleReducer?.data
  );

  const addFeeScheduleError = useSelector(
    (state: RootState) => state.AddFeeScheduleReducer?.error
  );

  const editFeeScheduleStatus = useSelector(
    (state: RootState) => state.EditFeeScheduleReducer?.status
  );

  const editFeeScheduleData = useSelector(
    (state: RootState) => state.EditFeeScheduleReducer?.data
  );

  const editFeeScheduleError = useSelector(
    (state: RootState) => state.EditFeeScheduleReducer?.error
  );

  const { data: feeScheduleData, status: getFeeScheduleByIdStatus } =
    useSelector<RootState, getFeeScheduleByIdState>(
      (state) => state.GetFeeScheduleByIdReducer
    );

  useEffect(() => {
    if (isEdit && selectedFeeSchedule?.originalFeeSchedule?.uuid) {
      dispatch(
        getFeeScheduleById(selectedFeeSchedule.originalFeeSchedule.uuid)
      );
    }
    return () => {
      dispatch(getFeeScheduleByIdAction.resetFeeScheduleByIdAction());
    };
  }, [isEdit, selectedFeeSchedule, dispatch]);

  useEffect(() => {
    if (
      isEdit &&
      getFeeScheduleByIdStatus === apiStatus.SUCCEEDED &&
      feeScheduleData
    ) {
      setValue("procedureCode", feeScheduleData.procedureCode || "");
      setValue("rate", feeScheduleData.rate.toString() || "");
      setValue("codeType", feeScheduleData.codeType || "");
      setValue("status", feeScheduleData.active ? "active" : "inactive");
    }
  }, [isEdit, getFeeScheduleByIdStatus, feeScheduleData, setValue]);

  const onSubmit = (data: any) => {
    const payload = {
      uuid: isEdit ? selectedFeeSchedule?.originalFeeSchedule?.uuid : undefined,
      procedureCode: data?.procedureCode,
      rate: data?.rate,
      codeType: data?.codeType,
      active: data.status == "active" ? true : false,
    };
    if (isEdit) {
      dispatch(editFeeSchedule(payload as ProcedureCode));
    } else {
      dispatch(addFeeSchedule(payload as ProcedureCode));
    }
  };

  useEffect(() => {
    switch (addFeeScheduleStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: (addFeeScheduleData as string) || "",
          })
        );
        dispatch(
          getAllFeeSchedule({
            page: 0,
            pageSize: parseInt(pageDisplaySize, 10),
          })
        );
        dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        if (addFeeScheduleError) {
          dispatch(
            snackbarAction.showSnackbarAction({
              isSnackbarOpen: true,
              severity: AlertSeverity.ERROR,
              message: addFeeScheduleError,
            })
          );
          dispatch(addFeeScheduleReducerAction.resetAddFeeScheduleReducer());
        }
        break;
    }
  }, [addFeeScheduleStatus, dispatch, addFeeScheduleError, handleClose]);

  useEffect(() => {
    switch (editFeeScheduleStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: (editFeeScheduleData as string) || "",
          })
        );
        dispatch(
          getAllFeeSchedule({
            page: 0,
            pageSize: parseInt(pageDisplaySize, 10),
          })
        );
        dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());
        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        if (editFeeScheduleError) {
          dispatch(
            snackbarAction.showSnackbarAction({
              isSnackbarOpen: true,
              severity: AlertSeverity.ERROR,
              message: editFeeScheduleError,
            })
          );
          dispatch(editFeeScheduleReducerAction.resetEditFeeScheduleReducer());
        }
        break;
    }
  }, [editFeeScheduleStatus, dispatch, editFeeScheduleError, handleClose]);

  const AllProcedureCodes = Array.isArray(getAllProcedureCodesData)
    ? getAllProcedureCodesData?.map((code: string) => ({
        value: code,
        label: code,
      }))
    : [];

  useEffect(() => {
    dispatch(getAllProcedureCodes());
  }, [dispatch]);

  return (
    <Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <CustomLabel
              label={FeeScheduleFormLabels.PROCEDURE_CODE}
              isRequired={true}
            />
            <Controller
              control={control}
              name="procedureCode"
              render={({ field }) => (
                <CustomSelect
                  placeholder={
                    FeeScheduleFormPlaceholders.SELECT_PROCEDURE_CODE
                  }
                  {...field}
                  value={field.value || ""}
                  items={AllProcedureCodes}
                  hasError={!!errors.procedureCode}
                  errorMessage={errors.procedureCode?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <CustomLabel label={FeeScheduleFormLabels.RATE} isRequired={true} />
            <Controller
              control={control}
              name="rate"
              render={({ field }) => (
                <CustomInput
                  placeholder={FeeScheduleFormPlaceholders.ENTER_RATE}
                  {...field}
                  hasError={!!errors.rate}
                  errorMessage={errors.rate?.message}
                  isNumeric={true}
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <CustomLabel
              label={FeeScheduleFormLabels.CODE_TYPE}
              isRequired={true}
            />
            <Controller
              control={control}
              name="codeType"
              render={({ field }) => (
                <CustomSelect
                  placeholder={FeeScheduleFormPlaceholders.SELECT_CODE_TYPE}
                  {...field}
                  value={field.value || ""}
                  items={codeTypeOptions}
                  hasError={!!errors.codeType}
                  errorMessage={errors.codeType?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomLabel label={LocationFormLabels.STATUS} isRequired />
            <Controller
              control={control}
              name="status"
              render={({ field }) => (
                <CustomSelect
                  placeholder={LocationFormPlaceholders.SELECT_STATUS}
                  {...field}
                  value={field.value || ""}
                  items={statusOptions}
                  hasError={!!errors.status}
                  errorMessage={errors.status?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          flexDirection={"row"}
          justifyContent={"flex-end"}
          mt={2}
          sx={{
            display: "flex",
            position: "absolute",
            bottom: "0",
            right: "0",
            width: "100%",
            borderTop: "1px solid #E7E7E7",
            paddingTop: 2,
          }}
        >
          <Grid
            display="flex"
            flexDirection={"row"}
            gap={3}
            sx={{ marginBottom: "1.5vh", marginRight: "1.5vw" }}
          >
            <Grid>
              <CustomButton
                variant="outline"
                label={SettingsFormConstants.CANCEL}
                isSubmitButton
                onClick={handleClose}
              />
            </Grid>
            <Grid>
              <CustomButton
                variant="filled"
                label={SettingsFormConstants.SAVE}
                type="submit"
                changePadding={false}
                isSubmitButton
              />
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddFeeScheduleDialog;
