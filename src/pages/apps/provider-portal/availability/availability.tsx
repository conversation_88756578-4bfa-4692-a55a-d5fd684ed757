import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import {
  Box,
  Grid,
  SelectChangeEvent,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import CustomDatePicker from "../../../../common-components/custom-date-picker/custom-date-picker";
import CustomSelect from "../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../common-components/customLabel/customLabel";
import { SettingsFormConstants } from "../../../../constants/formConst";
import { getAllSupervisingClinicians } from "../../../../redux/auth/profile/get-all-supervising-clinicians";
import { AppDispatch, RootState } from "../../../../redux/store";

import React from "react";
import {
  addAvailability,
  addAvailabilityReducerAction,
} from "../../../../redux/auth/availability/add-availability-reducer";
import { getAvailabilityByClinicianId } from "../../../../redux/auth/availability/get-availability-by-clinician-id";
import AvailabilitySLots from "./availabilitySLots";
import {
  AvailabilityConstants,
  availabilityDataInitialValue,
  AvailabilitySetting,
  DaySlot,
} from "./model/availabilityModel";
import SlotCreation from "./slot-creation";
import { apiStatus } from "../../../../models/apiStatus";
import { loaderAction } from "../../../../redux/auth/loaderReducer";
import { snackbarAction } from "../../../../redux/auth/snackbarReducer";
import { AlertSeverity } from "../../../../common-components/alert/alert";

interface DateRange {
  id: number;
  startDate: string;
  endDate: string;
}

export default function Availability() {
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [allDaySlots, setAllDaySlots] = useState<Map<number, DaySlot[]>>(
    new Map()
  );

  const [currentAvailabilityPayload, setCurrentAvailabilityPayload] =
    useState<AvailabilitySetting>(availabilityDataInitialValue);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [dateRanges, setDateRanges] = useState<DateRange[]>([
    { id: 1, startDate: "", endDate: "" },
  ]);
  const [nextId, setNextId] = useState(2);
  const [activeRangeId, setActiveRangeId] = useState<number | null>(
    dateRanges.length > 0 ? dateRanges[0].id : null
  );

  const {
    data: getAvailabilityByClinicianIdData,
    status: getAvailabilityByIdStatus,
  }: any = useSelector(
    (state: RootState) => state.GetAvailabilityByClinicianIdReducer
  );

  const {
    status: addAvailabilityStatus,
    error: addAvailabilityError,
    data: addAvailabilityData,
  } = useSelector((state: RootState) => state.AddAvailabilityReducer);

  const { data: getAllSupervisingCliniciansData }: any =
    useSelector(
      (state: RootState) => (state as any).GetAllSupervisingCliniciansReducer
    ) || {};

  const displaySupervisingClinicians = Object.entries(
    getAllSupervisingCliniciansData || {}
  ).map(([uuid, value]) => ({
    value: uuid,
    label: String(value),
  }));

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleStartDateChange = (date: string, id: number) => {
    setDateRanges((prevRanges) =>
      prevRanges.map((range) =>
        range.id === id ? { ...range, startDate: date } : range
      )
    );
    if (activeRangeId !== id) {
      setActiveRangeId(id);
    }
  };

  const handleEndDateChange = (date: string, id: number) => {
    setDateRanges((prevRanges) =>
      prevRanges.map((range) =>
        range.id === id ? { ...range, endDate: date } : range
      )
    );
    if (activeRangeId !== id) {
      setActiveRangeId(id);
    }
  };

  const handleAddAvailability = () => {
    const newId = nextId;
    const newRange = { id: newId, startDate: "", endDate: "" };
    setDateRanges((prevRanges) => [...prevRanges, newRange]);
    setAllDaySlots((prevAllSlots) => new Map(prevAllSlots).set(newId, []));
    setActiveRangeId(newId);
    setNextId((prevId) => prevId + 1);
  };

  const handleDeleteDateRange = (id: number) => {
    setDateRanges((prevRanges) =>
      prevRanges.filter((range) => range.id !== id)
    );
    setAllDaySlots((prevAllSlots) => {
      const newAllSlots = new Map(prevAllSlots);
      newAllSlots.delete(id);
      return newAllSlots;
    });
    if (activeRangeId === id) {
      const remainingRanges = dateRanges.filter((r) => r.id !== id);
      setActiveRangeId(
        remainingRanges.length > 0 ? remainingRanges[0]?.id : null
      );
    }
  };

  const updateDaySlotsForRangeCallback = useCallback(
    (rangeId: number, updatedSlots: DaySlot[]) => {
      setAllDaySlots((prev) => {
        const newMap = new Map(prev);
        newMap.set(
          rangeId,
          updatedSlots.map((s) => ({ ...s }))
        );
        return newMap;
      });
    },
    []
  );

  const handleSave = () => {
    setIsSubmitted(true);

    if (!selectedProvider) {
      return;
    }

    const hasIncompleteDateRanges = dateRanges.some(
      (range) => !range?.startDate || !range?.endDate
    );
    if (hasIncompleteDateRanges) {
      return;
    }

    let hasIncompleteDaySlots = false;
    allDaySlots.forEach((slotsInRanges, rangeId) => {
      const range = dateRanges.find((r) => r.id === rangeId);
      if (range && range?.startDate && range?.endDate) {
        if (
          slotsInRanges.some(
            (slot) => !slot?.day || !slot?.startTime || !slot?.endTime
          )
        ) {
          hasIncompleteDaySlots = true;
        }
      }
    });
    if (hasIncompleteDaySlots) {
      return;
    }

    const blockDaysFromPayload = currentAvailabilityPayload.blockDays || [];
    const hasIncompleteBlockDays = blockDaysFromPayload.some(
      (bd) =>
        !bd?.startTime ||
        !bd?.endTime ||
        !bd?.startTime?.includes("T") ||
        !bd?.endTime?.includes("T")
    );
    if (hasIncompleteBlockDays) {
      return;
    }
    const apiPayload = {
      clinicianId: selectedProvider,
      timeZone:
        currentAvailabilityPayload.timezone ||
        availabilityDataInitialValue.timezone,
      blockDays: blockDaysFromPayload.map((bd) => ({
        startTime: bd?.startTime,
        endTime: bd?.endTime,
      })),
      availabilityRanges: dateRanges
        .filter((range) => range?.startDate && range?.endDate)
        .map((range) => {
          const slotsForThisRange = allDaySlots.get(range?.id) || [];
          return {
            startDate: range?.startDate,
            endDate: range?.endDate,
            dayWiseSlots: slotsForThisRange.map((slot) => ({
              day: slot?.day,
              startTime: slot?.startTime,
              endTime: slot?.endTime,
            })),
          };
        }),
    };
    dispatch(addAvailability(apiPayload));
  };

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  useEffect(() => {
    if (
      getAvailabilityByClinicianIdData &&
      Object.keys(getAvailabilityByClinicianIdData).length > 0
    ) {
      const fetchedData = getAvailabilityByClinicianIdData;
      const newDateRanges: DateRange[] = fetchedData?.availabilityRanges?.map(
        (range: any, index: number) => ({
          id: range?.id || index + 1,
          startDate: range?.startDate,
          endDate: range?.endDate,
        })
      );
      const newAllDaySlots = new Map<number, DaySlot[]>();
      fetchedData?.availabilityRanges?.forEach((range: any, index: number) => {
        const rangeId = index + 1;
        newAllDaySlots.set(rangeId, range?.dayWiseSlots);
      });
      setDateRanges(newDateRanges);
      setAllDaySlots(newAllDaySlots);
      setCurrentAvailabilityPayload((prevPayload) => ({
        ...prevPayload,
        providerId: fetchedData?.clinicianId,
        timezone:
          fetchedData?.timeZone || availabilityDataInitialValue.timezone,
        blockDays: [],
        initialConsultTime:
          prevPayload.initialConsultTime ??
          availabilityDataInitialValue.initialConsultTime,
        followupConsultTime:
          prevPayload.followupConsultTime ??
          availabilityDataInitialValue.followupConsultTime,
        bookingWindow:
          prevPayload.bookingWindow ??
          availabilityDataInitialValue.bookingWindow,
        bufferTime:
          prevPayload.bufferTime ?? availabilityDataInitialValue.bufferTime,
        bookBefore:
          prevPayload.bookBefore ?? availabilityDataInitialValue.bookBefore,
      }));

      if (newDateRanges.length > 0) {
        setActiveRangeId(newDateRanges[0]?.id);
        setNextId(newDateRanges[newDateRanges.length - 1]?.id + 1);
      } else {
        setActiveRangeId(null);
        setNextId(1);
      }
    }
  }, [getAvailabilityByClinicianIdData]);

  useEffect(() => {
    switch (addAvailabilityStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              (addAvailabilityData as string) ||
              "Availability Added successfully",
          })
        );

        dispatch(addAvailabilityReducerAction.resetAddAvailabilityReducer());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addAvailabilityError as string,
          })
        );
        dispatch(addAvailabilityReducerAction.resetAddAvailabilityReducer());
        break;
    }
  }, [addAvailabilityStatus, dispatch, addAvailabilityError]);

  useEffect(() => {
    if (activeRangeId !== null && selectedProvider) {
      const activeRangeData = dateRanges.find((r) => r.id === activeRangeId);
      const slotsForActiveRange = allDaySlots.get(activeRangeId) || [];

      setCurrentAvailabilityPayload((prevPayload) => {
        const basePayload = prevPayload;

        if (activeRangeData) {
          return {
            ...basePayload,
            providerId: selectedProvider,
            startDate: activeRangeData?.startDate,
            endDate: activeRangeData?.endDate,
            daySlots: slotsForActiveRange.map((s) => ({ ...s })),
          };
        } else {
          return {
            ...basePayload,
            providerId: selectedProvider,
            startDate: "",
            endDate: "",
            daySlots: [],
          };
        }
      });
    } else if (!selectedProvider) {
      setCurrentAvailabilityPayload(availabilityDataInitialValue);
    }
  }, [activeRangeId, dateRanges, allDaySlots, selectedProvider]);

  useEffect(() => {
    if (dateRanges.length > 0 && !allDaySlots.has(dateRanges[0]?.id)) {
      setAllDaySlots((prev) => new Map(prev).set(dateRanges[0]?.id, []));
      if (activeRangeId === null) {
        setActiveRangeId(dateRanges[0]?.id);
      }
    }
  }, [dateRanges, allDaySlots, activeRangeId]);

  useEffect(() => {
    if (!selectedProvider) {
      const initialRangeId = 1;
      setDateRanges([{ id: initialRangeId, startDate: "", endDate: "" }]);
      setAllDaySlots(new Map().set(initialRangeId, []));
      setActiveRangeId(initialRangeId);
      setNextId(2);
      setCurrentAvailabilityPayload(availabilityDataInitialValue);
    }
  }, [selectedProvider]);

  useEffect(() => {
    switch (getAvailabilityByIdStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAvailabilityByIdStatus, dispatch]);

  useEffect(() => {
    if (
      getAvailabilityByClinicianIdData?.availabilityRanges &&
      getAvailabilityByClinicianIdData?.clinicianId
    ) {
      const fetchedData = getAvailabilityByClinicianIdData;
      const newDateRanges: DateRange[] = fetchedData?.availabilityRanges?.map(
        (range: any, index: number) => ({
          id: range?.id || index + 1,
          startDate: range?.startDate,
          endDate: range?.endDate,
        })
      );

      const newAllDaySlots = new Map<number, DaySlot[]>();
      fetchedData?.availabilityRanges?.forEach(
        (range: any, outerIndex: number) => {
          const rangeMapEntry = newDateRanges.find(
            (dr: DateRange) =>
              dr.startDate === range?.startDate && dr.endDate === range?.endDate
          );
          const rangeIdToUse = rangeMapEntry
            ? rangeMapEntry.id
            : range?.id || outerIndex + 1;
          newAllDaySlots.set(rangeIdToUse, range?.dayWiseSlots);
        }
      );

      setDateRanges(newDateRanges);
      setAllDaySlots(newAllDaySlots);

      setCurrentAvailabilityPayload((prevPayload) => ({
        ...prevPayload,
        providerId: fetchedData?.clinicianId,
        timezone:
          fetchedData?.timeZone || availabilityDataInitialValue.timezone,
        blockDays: fetchedData.blockDays?.map((bd: any) => ({ ...bd })) || [],
        initialConsultTime:
          prevPayload.initialConsultTime ??
          availabilityDataInitialValue.initialConsultTime,
        followupConsultTime:
          prevPayload.followupConsultTime ??
          availabilityDataInitialValue.followupConsultTime,
        bookingWindow:
          prevPayload.bookingWindow ??
          availabilityDataInitialValue.bookingWindow,
        bufferTime:
          prevPayload.bufferTime ?? availabilityDataInitialValue.bufferTime,
        bookBefore:
          prevPayload.bookBefore ?? availabilityDataInitialValue.bookBefore,
      }));

      if (newDateRanges.length > 0) {
        setActiveRangeId(newDateRanges[0]?.id);
        const maxId = newDateRanges.reduce(
          (max: number, range: DateRange) => Math.max(max, range?.id),
          0
        );
        setNextId(maxId + 1);
      } else {
        const initialRangeId = 1;
        setDateRanges([{ id: initialRangeId, startDate: "", endDate: "" }]);
        setAllDaySlots(new Map().set(initialRangeId, []));
        setActiveRangeId(initialRangeId);
        setNextId(2);
        setCurrentAvailabilityPayload(() => ({
          ...availabilityDataInitialValue,
          providerId: fetchedData?.clinicianId,
          timezone:
            fetchedData?.timeZone || availabilityDataInitialValue.timezone,
          blockDays: [],
        }));
      }
    } else if (selectedProvider) {
      const initialRangeId = 1;
      setDateRanges([{ id: initialRangeId, startDate: "", endDate: "" }]);
      setAllDaySlots(new Map().set(initialRangeId, []));
      setActiveRangeId(initialRangeId);
      setNextId(2);
      setCurrentAvailabilityPayload(() => ({
        ...availabilityDataInitialValue,
        providerId: selectedProvider,
        timezone: availabilityDataInitialValue.timezone,
        blockDays: [],
      }));
    }
  }, [
    getAvailabilityByClinicianIdData,
    selectedProvider,
    availabilityDataInitialValue,
  ]);

  return (
    <Box
      sx={{
        backgroundColor: "#F5F5F5",
        borderRadius: 2,
        minHeight: "calc(100vh - 100px)",
        overflowY: "auto",
        height: "82vh",
      }}
    >
      <Grid
        container
        sx={{
          backgroundColor: "#FFFFFF",
          borderRadius: 1,
          pl: 2,
          pr: 2,
          pb: 2,

          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
          position: "relative",
          direction: "row",
        }}
      >
        <Grid item xs={12} lg={3} mt={1}>
          <Grid mb={1}>
            <CustomLabel label={AvailabilityConstants.CLINICIAN_NAME} />
          </Grid>
          <CustomSelect
            placeholder={AvailabilityConstants.SELECT_CLINICIAN_NAME}
            name="selectedProvider"
            onChange={(event: SelectChangeEvent<string>) => {
              setSelectedProvider(event.target.value);
              dispatch(getAvailabilityByClinicianId(event.target.value));
            }}
            value={selectedProvider}
            items={displaySupervisingClinicians}
          />
        </Grid>

        {selectedProvider && (
          <>
            <Grid
              container
              mt={1}
              sx={{
                height: "65vh",
              }}
              spacing={isSmallScreen ? 2 : 0}
            >
              <Grid item xs={12} md={6}>
                <Grid
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={1}
                >
                  <Typography variant="inputTitleMedium">
                    {AvailabilityConstants.DAY_WISE_AVAILABILITY}
                  </Typography>
                  <CustomButton
                    startIcon={<AddIcon />}
                    label={AvailabilityConstants.ADD_AVAILABILITY}
                    variant="editButton"
                    onClick={handleAddAvailability}
                    isSubmitButtonTwo
                  />
                </Grid>
                <Grid
                  sx={{
                    height: "55vh",
                    overflowY: "auto",
                    overflowX: "hidden",
                    "&::-webkit-scrollbar": {
                      display: "none",
                    },
                  }}
                >
                  {dateRanges.map((range) => (
                    <Grid
                      key={range.id}
                      mt={1}
                      p={1.5}
                      borderRadius={3}
                      border={"1px solid #E0E0E0"}
                      bgcolor={"#F5F5F5"}
                      width={"100%"}
                    >
                      <Grid item container>
                        <Grid
                          item
                          xs={12}
                          sm={11.68}
                          alignItems="center"
                          spacing={isSmallScreen ? 1 : 2}
                          display={"flex"}
                          flexDirection={"row"}
                        >
                          <Grid xs={3}>
                            {" "}
                            <Typography variant="inputTitleMedium">
                              {AvailabilityConstants.SELECT_DATE_RANGE}
                            </Typography>
                          </Grid>
                          <Grid xs={1} ml={2}>
                            <Typography variant="inputTitleMedium">
                              {AvailabilityConstants.FROM}
                            </Typography>
                          </Grid>
                          <Grid xs={3.5} ml={2}>
                            <CustomDatePicker
                              value={range?.startDate}
                              placeholder={AvailabilityConstants.START_DATE}
                              handleDateChange={(date: string) =>
                                handleStartDateChange(date, range.id)
                              }
                              hasError={isSubmitted && !range?.startDate}
                              errorMessage={
                                AvailabilityConstants.START_DATE_REQD
                              }
                              disablePast
                            />
                          </Grid>
                          {!isSmallScreen && (
                            <Grid
                              item
                              xs="auto"
                              sx={{ textAlign: "center" }}
                              ml={1.5}
                            >
                              <Typography variant="inputTitleMedium">
                                {AvailabilityConstants.TO}
                              </Typography>
                            </Grid>
                          )}
                          <Grid xs={3.7} ml={2}>
                            <CustomDatePicker
                              value={range?.endDate}
                              placeholder={AvailabilityConstants.END_DATE}
                              handleDateChange={(date: string) =>
                                handleEndDateChange(date, range.id)
                              }
                              hasError={isSubmitted && !range?.endDate}
                              errorMessage={AvailabilityConstants.END_DATE_REQD}
                              disablePast
                            />
                          </Grid>
                          <Grid
                            item
                            xs={12}
                            sm={1.5}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "flex-end",
                            }}
                          >
                            <DeleteOutlineOutlinedIcon
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                handleDeleteDateRange(range.id);
                              }}
                              style={{ cursor: "pointer" }}
                            />
                          </Grid>
                        </Grid>

                        <Box width="100%">
                          <AvailabilitySLots
                            rangeId={range.id}
                            rangeStartDate={range?.startDate}
                            rangeEndDate={range?.endDate}
                            currentDaySlots={allDaySlots.get(range.id) || []}
                            updateDaySlotsForRange={
                              updateDaySlotsForRangeCallback
                            }
                            isSubmitted={isSubmitted}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    maxHeight: "63vh",
                    overflowY: "auto",
                    overflowX: "hidden",
                    pr: 1,
                    pl: { md: 2 },
                    borderRadius: 1,
                    p: 1,
                    ml: 5,
                  }}
                >
                  <SlotCreation
                    availabilityPayload={currentAvailabilityPayload}
                    setAvailabilityPayload={setCurrentAvailabilityPayload}
                    isSubmitted={isSubmitted}
                  />
                </Box>
              </Grid>
            </Grid>

            <Grid
              item
              xs={12}
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                pb: 1,
                pt: 1,
                pr: 2,
                width: "100%",
                position: "absolute",
                bottom: 0,
                left: 0,

                zIndex: 10,
                backgroundColor: "#fff",
              }}
            >
              <Grid
                display="flex"
                flexDirection={{ xs: "column", sm: "row" }}
                gap={2}
                sx={{ width: { xs: "100%", sm: "auto" } }}
              >
                <CustomButton
                  variant="outline"
                  label={SettingsFormConstants.CANCEL}
                  isSubmitButton
                  onClick={() => {
                    setSelectedProvider("");
                  }}
                />
                <CustomButton
                  variant="filled"
                  label={SettingsFormConstants.SAVE}
                  onClick={handleSave}
                  type="button"
                  changePadding={false}
                  isSubmitButton
                />
              </Grid>
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
}
