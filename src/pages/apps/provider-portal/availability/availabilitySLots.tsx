import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { Box, Grid } from "@mui/material";
import { AvailabilityConstants, DaySlot } from "./model/availabilityModel";

import { SelectChangeEvent } from "@mui/material";
import React, { useEffect } from "react";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import CustomSelect from "../../../../common-components/custom-select/customSelect";
import CustomTimePicker from "../../../../common-components/custom-time-picker/custom-time-picker";
import CustomLabel from "../../../../common-components/customLabel/customLabel";
import { toCamelCase } from "../../../../common-components/utils/toCamelCase";

interface AvailabilitySLotsProps {
  rangeId: number;
  rangeStartDate: string;
  rangeEndDate: string;
  currentDaySlots: DaySlot[];
  updateDaySlotsForRange: (rangeId: number, updatedSlots: DaySlot[]) => void;
  isSubmitted: boolean;
}
const AvailabilitySLots: React.FC<AvailabilitySLotsProps> = React.memo(
  (props) => {
    const {
      rangeId,
      rangeStartDate,
      rangeEndDate,
      currentDaySlots,
      updateDaySlotsForRange,
      isSubmitted,
    } = props;

    useEffect(() => {
      if (rangeStartDate && rangeEndDate && currentDaySlots.length === 0) {
        const newBlankSlot: DaySlot = {
          dateRangeId: rangeId,
          day: "",
          startTime: "",
          endTime: "",
          locationUuid: "",
          availabilityMode: "IN_PERSON",
          location: {},
          locationName: "",
        };
        updateDaySlotsForRange(rangeId, [newBlankSlot]);
      }
    }, [
      rangeStartDate,
      rangeEndDate,
      currentDaySlots,
      rangeId,
      updateDaySlotsForRange,
    ]);

    function getUniqueWeekdays(startDate: Date, endDate: Date): string[] {
      const dayNames = [
        "SUNDAY",
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
      ];
      const uniqueDays = new Set<string>();

      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        const start = new Date(startDate.setHours(0, 0, 0, 0));
        const end = new Date(endDate.setHours(0, 0, 0, 0));
        for (
          let date = new Date(start);
          date <= end;
          date.setDate(date.getDate() + 1)
        ) {
          uniqueDays.add(dayNames[date.getDay()]);
        }
      }

      return Array.from(uniqueDays);
    }

    const start = new Date(rangeStartDate);
    const end = new Date(rangeEndDate);

    const dayOptions = Object.values(getUniqueWeekdays(start, end)).map(
      (day) => ({
        value: day,
        label: toCamelCase(day as string),
      })
    );

    const handleLocalSlotUpdate = (updatedDaySlots: DaySlot[]) => {
      updateDaySlotsForRange(rangeId, updatedDaySlots);
    };

    const handleDelete = (index: number) => {
      const updated = [...currentDaySlots];
      updated.splice(index, 1);
      handleLocalSlotUpdate(updated);
    };

    const handleAdd = () => {
      const newDaySlot: DaySlot = {
        dateRangeId: rangeId,
        day: "",
        startTime: "",
        endTime: "",
        locationUuid: "",
        availabilityMode: "IN_PERSON",
        location: {},
        locationName: "",
      };
      handleLocalSlotUpdate([...currentDaySlots, newDaySlot]);
    };

    const handleDayChange = (index: number, value: string) => {
      const updated = currentDaySlots.map((slot: DaySlot, i: number) =>
        i === index ? { ...slot, day: value } : slot
      );
      handleLocalSlotUpdate(updated);
    };

    const handleEndTime = (time: string, index: number) => {
      const updated = currentDaySlots.map((slot: DaySlot, i: number) =>
        i === index ? { ...slot, endTime: time } : slot
      );
      handleLocalSlotUpdate(updated);
    };

    const handleStartTime = (time: string, index: number) => {
      const updated = currentDaySlots.map((slot: DaySlot, i: number) =>
        i === index ? { ...slot, startTime: time } : slot
      );
      handleLocalSlotUpdate(updated);
    };

    return (
      <Box mt={1} mb={1}>
        {currentDaySlots.map((item: DaySlot, index: number) => (
          <Grid
            container
            spacing={2}
            key={`${rangeId}-slot-${index}-${item?.day}`}
            sx={{ mt: index !== 0 ? 0.2 : 0 }}
          >
            <Grid item xs={12} sm={3.6}>
              <CustomLabel label={AvailabilityConstants.DAY} isRequired />
              <CustomSelect
                name="day"
                hasError={isSubmitted && !item?.day}
                errorMessage={AvailabilityConstants.DAY_REQUIRED}
                value={item?.day}
                placeholder={AvailabilityConstants.SELECT_DAY}
                items={dayOptions}
                onChange={(e: SelectChangeEvent<string>) =>
                  handleDayChange(index, (e.target as HTMLSelectElement).value)
                }
              />
            </Grid>
            <Grid item xs={12} sm={3.6}>
              <CustomLabel label={AvailabilityConstants.FROM} isRequired />
              <CustomTimePicker
                hasError={isSubmitted && !item?.startTime}
                errorMessage={AvailabilityConstants.ENTER_FROM_TIME}
                handleTimeChange={(timeValue: string) =>
                  handleStartTime(timeValue, index)
                }
                value={item?.startTime}
              />
            </Grid>
            <Grid item xs={12} sm={3.6}>
              <CustomLabel label={AvailabilityConstants.TILL} isRequired />
              <CustomTimePicker
                hasError={isSubmitted && !item?.endTime}
                errorMessage={AvailabilityConstants.ENTER_TILL_TIME}
                handleTimeChange={(timeValue: string) =>
                  handleEndTime(timeValue, index)
                }
                value={item?.endTime}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={1.2}
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mt: 1.5,
              }}
            >
              <DeleteOutlineOutlinedIcon
                style={{
                  cursor: "pointer",
                  opacity: currentDaySlots.length === 1 ? 0.5 : 1,
                  pointerEvents: currentDaySlots.length === 1 ? "none" : "auto",
                }}
                onClick={() => handleDelete(index)}
              />
            </Grid>
          </Grid>
        ))}
        <Grid item xs={12} mt={2}>
          <CustomButton
            startIcon={<AddIcon />}
            label={AvailabilityConstants.ADD_MORE}
            onClick={handleAdd}
            isSubmitButtonTwo
            variant="editButton"
          />
        </Grid>
      </Box>
    );
  }
);

export default AvailabilitySLots;
