import { Grid, Paper, Switch, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "src/redux/store";
import { AlertSeverity } from "../../../../common-components/alert/alert";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import SignatureCapture from "../../../../common-components/signature-canvas/signatureCanvas";
import {
  capitalizeFirstLetter,
  formatPhoneNumber,
} from "../../../../common-components/utils/stringUtils";
import {
  SettingsFormConstants,
  UserProfileEnum,
} from "../../../../constants/formConst";
import { ProfileTypographyVariants } from "../../../../constants/typography-variants";
import { apiStatus } from "../../../../models/apiStatus";
import { loaderAction } from "../../../../redux/auth/loaderReducer";
import {
  addClinicianSignature,
  addClinicianSignatureReducerAction,
} from "../../../../redux/auth/profile/add-clinician-signature";
import { getUserById } from "../../../../redux/auth/profile/get-user-by-id";
import { snackbarAction } from "../../../../redux/auth/snackbarReducer";
import CreateOutlinedIcon from "@mui/icons-material/CreateOutlined";
import DrawerBS from "../../../../common-components/drawer-bs/custom-drawer";
import ChangePasswordDialog from "./change-password-dialog";
import { changeTwoFactorAuthenticationClinician } from "../../../../redux/auth/profile/allow-two-factor-authentication-clinician";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
interface FieldData {
  label: string;
  value: string;
}

const UserProfile = () => {
  const { data: getUserByIdData, status: getUserByIdStatus } = useSelector(
    (state: RootState) => state.GetUserProfileByIdReducer
  ) as {
    data: any;
    status: string;
  };

  const {
    data: addSignatureData,
    status: addSignatureStatus,
    error: addSignatureError,
  } = useSelector((state: RootState) => state.AddClinicianSignatureReducer);

  const {
    data: changeTwoFactorAuthenticationData,
    status: changeTwoFactorAuthenticationStatus,
    error: changeTwoFactorAuthenticationError,
  } = useSelector(
    (state: RootState) => state.ChangeTwoFactorAuthenticationClinicianReducer
  );

  const dispatch = useDispatch<AppDispatch>();
  const [leftColumnFields, setLeftColumnFields] = useState<FieldData[]>([]);
  const [rightColumnFields, setRightColumnFields] = useState<FieldData[]>([]);
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [signature, setSignature] = useState("");
  const [, setSignatureChanged] = useState(false);
  const [, setSignatureBase64] = useState("");
  const [changePasswordDialog, setChangePasswordDialog] = useState(false);

  const handleSignatureSave = (dataURL: string) => {
    setSignature(dataURL);
    setShowSignatureModal(false);
    setSignatureChanged(true);

    const base64WithoutPrefix = dataURL.substring(dataURL.indexOf(",") + 1);
    setSignatureBase64(base64WithoutPrefix);

    if (getUserByIdData?.uuid) {
      dispatch(
        addClinicianSignature({
          clinicianId: getUserByIdData.uuid,
          base64: base64WithoutPrefix,
        })
      );
    }
  };

  const handleSignatureCancel = () => {
    setShowSignatureModal(false);
  };

  const handleSignature = () => {
    setShowSignatureModal(true);
  };

  useEffect(() => {
    if (getUserByIdData) {
      const profileData = getUserByIdData as any;

      setLeftColumnFields([
        {
          label: UserProfileEnum.NAME,
          value: `${getUserByIdData?.firstName} ${profileData?.lastName}`,
        },
        {
          label: UserProfileEnum.ROLE,
          value: capitalizeFirstLetter(profileData?.roles?.join(", ")),
        },
        {
          label: UserProfileEnum.EMAIL_ID,
          value: profileData?.emailId,
        },
        {
          label: UserProfileEnum.CONTACT_NUMBER,
          value: `${formatPhoneNumber(profileData?.contactNumber)}`,
        },
      ]);

      setRightColumnFields([
        {
          label: UserProfileEnum.NPI_NUMBER,
          value: profileData?.npiNumber || "-",
        },
        {
          label: UserProfileEnum.WORK_LOCATIONS,
          value: profileData?.locationNames
            ? profileData?.locationNames.join(", ")
            : "-",
        },
        {
          label: UserProfileEnum.LANGUAGES_SPOKEN,
          value: profileData?.languagesSpoken
            ? profileData.languagesSpoken.join(", ")
            : "-",
        },
      ]);
    }
  }, [getUserByIdData]);

  useEffect(() => {
    switch (getUserByIdStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getUserByIdStatus, dispatch]);

  useEffect(() => {
    switch (addSignatureStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: addSignatureData || "Signature added successfully",
          })
        );
        dispatch(
          addClinicianSignatureReducerAction.resetAddClinicianSignatureReducer()
        );
        setSignatureChanged(false);
        dispatch(getUserById());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addSignatureError || "Failed to add signature",
          })
        );
        dispatch(
          addClinicianSignatureReducerAction.resetAddClinicianSignatureReducer()
        );
        break;
    }
  }, [addSignatureStatus, addSignatureData, addSignatureError, dispatch]);

  const handleTwoFactorAuthenticationChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (getUserByIdData?.uuid) {
      dispatch(
        changeTwoFactorAuthenticationClinician({
          clinicianUuid: getUserByIdData.uuid,
          flag: event.target.checked,
        })
      );
    }
  };

  useEffect(() => {
    switch (changeTwoFactorAuthenticationStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              changeTwoFactorAuthenticationData ||
              "Two-Factor Authentication updated successfully",
          })
        );
        dispatch(getUserById());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              changeTwoFactorAuthenticationError ||
              "Failed to update Two-Factor Authentication",
          })
        );
        break;
    }
  }, [
    changeTwoFactorAuthenticationStatus,
    changeTwoFactorAuthenticationData,
    changeTwoFactorAuthenticationError,
    dispatch,
  ]);

  useEffect(() => {
    dispatch(getUserById());
  }, [dispatch]);

  return (
    <Grid>
      <Grid display={"flex"} alignItems={"center"} mb={2}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography
              variant={ProfileTypographyVariants.TITLE_MEDIUM_PROFILE_BOLD}
            >
              {SettingsFormConstants.PROFILE}
            </Typography>
          </Grid>

          {/* Right-aligned: 2FA + Change Password */}
          <Grid item>
            <Grid container alignItems="center" spacing={2}>
              <Grid item>
                <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                  {UserProfileEnum.TWO_FACTOR_AUTHENTICATION}
                </Typography>
              </Grid>
              <Grid item>
                <Switch
                  checked={!!getUserByIdData?.twoFactorAuthentication}
                  onChange={handleTwoFactorAuthenticationChange}
                />
              </Grid>
              <Grid item>
                <CustomButton
                  label="Change Password"
                  variant="editButton"
                  startIcon={<CreateOutlinedIcon />}
                  onClick={() => setChangePasswordDialog(true)}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Paper
        sx={{
          p: 1,
          borderRadius: 1,
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
          width: "100%",
        }}
      >
        <Grid
          display={"flex"}
          flexDirection={{ xs: "column", md: "row" }}
          gap={2}
        >
          <Grid
            container
            spacing={2}
            display={"flex"}
            flexDirection={"column"}
            mt={"1px"}
            ml={1}
          >
            <Grid container flexDirection={{ xs: "column", md: "row" }}>
              <Grid
                item
                width={{ xs: "100%", md: "28vw" }}
                ml={1}
                mt={1}
                display={"flex"}
                flexDirection={"column"}
                gap={2}
                xs={6}
                md={4}
              >
                {leftColumnFields.map((field, index) => (
                  <Grid
                    key={index}
                    display={"flex"}
                    flexDirection={"row"}
                    gap={5}
                    width={"100%"}
                  >
                    <Grid xs={3}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                        }
                      >
                        {field.label}
                      </Typography>
                    </Grid>
                    <Grid xs={9}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                        }
                      >
                        :&nbsp;&nbsp;&nbsp;&nbsp;{field.value || "-"}
                      </Typography>
                    </Grid>
                  </Grid>
                ))}
              </Grid>

              <Grid
                item
                width={{ xs: "100%", md: "40vw" }}
                display={"flex"}
                flexDirection={"column"}
                gap={2}
                mt={2}
                xs={6}
                md={4}
              >
                {rightColumnFields.map((field, index) => (
                  <Grid
                    key={index}
                    display={"flex"}
                    flexDirection={"row"}
                    gap={2}
                  >
                    <Grid xs={3}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                        }
                      >
                        {field.label}
                      </Typography>
                    </Grid>
                    <Grid xs={9}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                        }
                      >
                        :&nbsp;&nbsp;&nbsp;&nbsp;{field.value || "-"}
                      </Typography>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {getUserByIdData?.signature ? (
          <Grid
            container
            spacing={2}
            mt={2}
            ml={0.5}
            display={"flex"}
            flexDirection={"column"}
          >
            <Grid display={"flex"} flexDirection={"row"} gap={5}>
              <Grid item mt={5} ml={1.5}>
                <Typography
                  variant={ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY}
                >
                  Signature:
                </Typography>
              </Grid>
              <Grid item>
                <img
                  src={getUserByIdData?.signature}
                  alt="Signature"
                  style={{ maxWidth: "10vw", height: "auto" }}
                />
              </Grid>
            </Grid>
            <Grid item>
              <CustomButton
                onClick={handleSignature}
                label="Update Signature"
                variant="filled"
              />
            </Grid>
          </Grid>
        ) : (
          <Grid container spacing={2} mt={2} ml={1}>
            <Grid item>
              <CustomButton
                onClick={handleSignature}
                label={signature ? "Update Signature" : "Add Signature"}
                variant="filled"
                startIcon={<AddOutlinedIcon />}
              />
            </Grid>
          </Grid>
        )}

        {showSignatureModal && (
          <SignatureCapture
            onSave={handleSignatureSave}
            onCancel={handleSignatureCancel}
            open={showSignatureModal}
          />
        )}
      </Paper>
      <DrawerBS
        title={SettingsFormConstants.CHANGE_PASSWORD}
        open={changePasswordDialog}
        onClose={() => setChangePasswordDialog(false)}
        anchor={"right"}
        drawerWidth="30vw"
      >
        <ChangePasswordDialog
          handleClose={() => setChangePasswordDialog(false)}
        />
      </DrawerBS>
    </Grid>
  );
};

export default UserProfile;
