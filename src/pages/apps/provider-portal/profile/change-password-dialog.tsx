import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { Grid } from "@mui/material";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import CustomButton from "../../../../common-components/custom-button/custom-button";
import CustomInput from "../../../../common-components/custom-input/customInput";
import CustomLabel from "../../../../common-components/customLabel/customLabel";
import { SetPasswordSchema } from "../../../../common-components/login/login-pages/login-pages-schema/login-pages-schema";
import { AlertSeverity } from "../../../../common-components/snackbar-alert/snackbar-alert";
import { loginConstants } from "../../../../constants/common-component";
import { apiStatus } from "../../../../models/apiStatus";
import { loaderAction } from "../../../../redux/auth/loaderReducer";
import {
  changeUserPassword,
  changeUserPasswordReducerAction,
} from "../../../../redux/auth/profile/change-user-password";
import { snackbarAction } from "../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../redux/store";

interface SetPasswordForm {
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordDialogProps {
  handleClose: () => void;
}

interface ChangePasswordForm {
  newPassword: string;
}

const ChangePasswordDialog = (props: ChangePasswordDialogProps) => {
  const { handleClose } = props;
  const {
    control,
    formState: { errors },
    handleSubmit,
  } = useForm<SetPasswordForm>({
    mode: "onChange",
    resolver: yupResolver(SetPasswordSchema),
  });
  const dispatch = useDispatch<AppDispatch>();

  const {
    status: changeUserPasswordStatus,
    error: changeUserPasswordError,
    data: changeUserPasswordData,
  }: any = useSelector((state: RootState) => state.ChangeUserPasswordReducer);

  const onSubmit = (data: ChangePasswordForm) => {
    dispatch(
      changeUserPassword({
        newPassword: data?.newPassword,
      })
    );
  };

  useEffect(() => {
    return () => {
      dispatch(
        changeUserPasswordReducerAction.resetChangeUserPasswordReducer()
      );
    };
  }, [dispatch]);

  useEffect(() => {
    switch (changeUserPasswordStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              (changeUserPasswordData as string) ||
              "Password Changed Successfully",
          })
        );

        handleClose();
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: changeUserPasswordError,
          })
        );
        break;
    }
  }, [
    changeUserPasswordStatus,
    dispatch,
    changeUserPasswordError,
    handleClose,
    changeUserPasswordData,
  ]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        container
        spacing={3}
        width={"25vw"}
        ml={
          3
        }
        mt={5}
        alignItems={"center"}
        justifyContent={"center"}
      >
        <Grid display={"flex"} flexDirection={"column"} width={"25vw"}>
          <Grid mb={3}>
            <CustomLabel
              label={loginConstants.NEW_PASSWORD}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="newPassword"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  isPassword={true}
                  placeholder={loginConstants.ENTER_NEW_PASS}
                  {...field}
                  hasError={!!errors.newPassword}
                  errorMessage={errors.newPassword?.message}
                  isNumeric={false}
                />
              )}
            />
          </Grid>
          <Grid>
            <CustomLabel
              label={loginConstants.CONFIRM_PASSWORD}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="confirmPassword"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  isPassword={true}
                  placeholder={loginConstants.CONFIRM_PASSWORD}
                  {...field}
                  hasError={!!errors.confirmPassword}
                  errorMessage={errors.confirmPassword?.message}
                  isNumeric={false}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid xs={12} mt={4.5}>
          <CustomButton
            label={loginConstants.SET_PASSWORD}
            variant="filled"
            fullWidth
            type="submit"
          />
        </Grid>
      </Grid>
    </form>
  );
};

export default ChangePasswordDialog;
