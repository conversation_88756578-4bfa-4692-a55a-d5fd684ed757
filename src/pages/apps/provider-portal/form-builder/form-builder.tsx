import { Box } from "@mui/material";
import { FormBuilder } from "@thinkitive/form-builder";
import type {
  FormBuilderSchema,
  FormBuilderUiSchema,
} from "@thinkitive/form-builder";

const CustomFormBuilder = () => {
  type FormBuilderSchema = typeof FormBuilderSchema;
  type FormBuilderUiSchema = typeof FormBuilderUiSchema;

  const handleSchema = (schema: FormBuilderSchema) =>
    console.log("SCHEMA:", schema);
  const handleUiSchema = (uiSchema: FormBuilderUiSchema) =>
    console.log("UISchema:", uiSchema);
  const handleData = (data: unknown) => console.log("DATA:", data);

  return (
    <Box sx={{ height: "80vh", overflowY: "scroll" }}>
      <FormBuilder
        onSchemaChange={handleSchema}
        onUiSchemaChange={handleUiSchema}
        onDataChange={handleData}
      />
      {/* <FormRenderer
        schema={schema}
        uischema={uischema}
        data={data}
        onChange={(updatedData: unknown) =>
          console.log("Updated data:", updatedData)
        }
        onSave={(formData: unknown) => console.log("Form submitted:", formData)}
        onErrors={(errors: FormError) =>
          console.log("Validation errors:", errors)
        }
      /> */}
    </Box>
  );
};

export default CustomFormBuilder;
