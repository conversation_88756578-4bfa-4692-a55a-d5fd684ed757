import { yupResolver } from "@hookform/resolvers/yup";
import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { AlertSeverity } from "../../../../../common-components/alert/alert";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
// import CustomContactInputNew from "../../../../../common-components/custom-contact-input/custom-contact-field";
import * as yup from "yup";
import CustomContactInputNew from "../../../../../common-components/custom-contact-field/custom-contact-field";
import CustomDatePicker from "../../../../../common-components/custom-date-picker/custom-date-picker";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import ImageUpload from "../../../../../common-components/image-upload/image-upload";
import CustomMultipleFilesUpload from "../../../../../common-components/multiple-files-upload copy/custom-multiple-files-upload";
import {
  PatientEthnicityOptions,
  PatientFormButtons,
  PatientFormLabels,
  PatientFormPlaceholders,
  PatientFormSectionTitles,
  PatientGenderOptions,
  PatientInsuranceOptions,
  PatientLanguageOptions,
  PatientRelationshipOptions,
  PatientRelationshipWithPatientOptions,
  UploadFileComponentConstants,
} from "../../../../../constants/formConst";
import { AllTypes } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { PatientTypes } from "../../../../../models/providerGroup";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  addPatient,
  addPatientReducerAction,
} from "../../../../../redux/auth/patient/add-patient-reducer";
import {
  editPatient,
  editPatientReducerAction,
} from "../../../../../redux/auth/patient/edit-patient-reducer";
import { getAllPatients } from "../../../../../redux/auth/patient/get-all-patients-reducer";
import {
  getPatientById,
  getPatientByIdAction,
  getPatientByIdState,
} from "../../../../../redux/auth/patient/get-patient-by-id";
import { getAllAmericanStates } from "../../../../../redux/auth/profile/get-all-states-reducer";
import { getAllSupervisingClinicians } from "../../../../../redux/auth/profile/get-all-supervising-clinicians";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { AddPatientSchema } from "./add-patients-schema";

interface FormData {
  profileImage?: File | null;
  firstName: string;
  middleName?: string;
  lastName: string;
  preferredName?: string;
  dateOfBirth: string;
  legalSex?: string;
  genderIdentity?: string;
  emailId?: string;
  phoneNumber: string;
  ethnicity?: string;
  race?: string;
  preferredLanguage?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipcode?: string;
  emergencyName?: string;
  emergencyPhone?: string;
  emergencyRelationship?: string;
  relationship?: string;
  isResponsibleParty?: boolean;
  phoneAppointmentReminders?: boolean;
  emailAppointmentReminders?: boolean;
  primaryClinician: string;
  secondaryClinician?: string;
  sameAsEmergencyContact?: boolean;
  isResponsiblePartyClinician?: boolean;
  paymentMethod: string;
  insuranceName?: string;
  memberId?: string;
  groupId?: string;
  patientRelationship?: string;
  subscriberFirstName?: string;
  subscriberLastName?: string;
  subscriberDateOfBirth?: string;
  secondaryInsuranceName?: string;
  secondaryMemberId?: string;
  secondaryGroupId?: string;
  secondaryPatientRelationship?: string;
  secondarySubscriberFirstName?: string;
  secondarySubscriberLastName?: string;
  secondarySubscriberDateOfBirth?: string;
  showSecondaryInsurance?: boolean;
  guardianName?: string;
  guardianPhone?: string;
  guardianRelationship?: string;
  startDatePrimary?: string;
  endDatePrimary?: string;
  startDateSecondary?: string;
  endDateSecondary?: string;
  subscriberDateOfBirthPrimary?: string;
}

const DemographicTab = () => {
  const [showSecondaryInsurance, setShowSecondaryInsurance] = useState(false);
  const [primaryFrontPhoto, setPrimaryFrontPhoto] = useState<string | null>(
    null
  );
  const [primaryBackPhoto, setPrimaryBackPhoto] = useState<string | null>(null);
  const [secondaryFrontPhoto, setSecondaryFrontPhoto] = useState<string | null>(
    null
  );
  const [secondaryBackphoto, setSecondaryBackPhoto] = useState<string | null>(
    null
  );
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImageBase64, setProfileImageBase64] = useState<string | null>(
    null
  );
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const uuid = window.location.pathname.split("/").pop();

  const {
    data: getPatientByIdData,
    status: getPatientByIdStatus,
    error: getPatientByIdError,
  }: getPatientByIdState = useSelector(
    (state: RootState) => state.GetPatientByIdReducer
  ) || {};

  const urlToBase64 = async (url: string | null): Promise<string> => {
    try {
      if (!url) {
        return "";
      }

      dispatch(loaderAction.showLoader());
      if (url.startsWith("data:image")) {
        dispatch(loaderAction.hideLoader());
        return url.split(",")[1];
      }
      if (url.startsWith("http")) {
        const response = await fetch(url, {
          mode: "cors",
          cache: "no-cache",
          headers: {
            "Cache-Control": "no-cache",
          },
        });

        if (!response.ok) {
          dispatch(loaderAction.hideLoader());
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }

        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();

          reader.onloadend = () => {
            try {
              if (typeof reader.result === "string") {
                const base64Data = reader.result.split(",")[1];
                if (!base64Data) {
                  dispatch(loaderAction.hideLoader());
                  reject(new Error("Failed to extract base64 data"));
                  return;
                }
                dispatch(loaderAction.hideLoader());
                resolve(base64Data);
              } else {
                dispatch(loaderAction.hideLoader());
                reject(new Error("FileReader result is not a string"));
              }
            } catch (error) {
              dispatch(loaderAction.hideLoader());
              reject(new Error(`Error processing FileReader result: ${error}`));
            }
          };

          reader.onerror = () => {
            dispatch(loaderAction.hideLoader());
            reject(new Error(`FileReader error: ${reader.error}`));
          };

          reader.readAsDataURL(blob);
        });
      }
      if (/^[A-Za-z0-9+/=]+$/.test(url)) {
        dispatch(loaderAction.hideLoader());
        return url;
      }

      dispatch(loaderAction.hideLoader());
      return "";
    } catch (error) {
      dispatch(loaderAction.hideLoader());
      console.error("Error converting URL to Base64:", error);
      return "";
    }
  };

  const fileToBase64Promise = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      try {
        dispatch(loaderAction.showLoader());
        const reader = new FileReader();

        reader.onload = () => {
          try {
            if (typeof reader.result === "string") {
              const base64Data = reader.result.split(",")[1];
              if (!base64Data) {
                dispatch(loaderAction.hideLoader());
                reject(new Error("Failed to extract base64 data from file"));
                return;
              }
              dispatch(loaderAction.hideLoader());
              resolve(base64Data);
            } else {
              dispatch(loaderAction.hideLoader());
              reject(new Error("FileReader result is not a string"));
            }
          } catch (error) {
            dispatch(loaderAction.hideLoader());
            reject(new Error(`Error processing FileReader result: ${error}`));
          }
        };

        reader.onerror = () => {
          dispatch(loaderAction.hideLoader());
          reject(new Error(`FileReader error: ${reader.error}`));
        };

        reader.readAsDataURL(file);
      } catch (error) {
        dispatch(loaderAction.hideLoader());
        reject(new Error(`Error setting up FileReader: ${error}`));
      }
    });
  };

  const convertFileToBase64 = async (file: File): Promise<string> => {
    try {
      dispatch(loaderAction.showLoader());
      const base64 = await fileToBase64Promise(file);
      dispatch(loaderAction.hideLoader());
      return base64;
    } catch (error) {
      dispatch(loaderAction.hideLoader());
      console.error("Error converting file to base64:", error);
      return "";
    }
  };

  const schema = AddPatientSchema as yup.ObjectSchema<FormData>;

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(schema),
  });

  const navigate = useNavigate();

  const {
    status: addPatientStatus,
    error: addPatientError,
    data: addPatientData,
  }: any = useSelector((state: RootState) => state.AddPatientReducer);

  const {
    status: editPatientStatus,
    error: editPatientError,
    data: editPatientData,
  }: any = useSelector((state: RootState) => state.EditPatientReducer);

  const { data: getAllAmericanStatesData }: any = useSelector(
    (state: RootState) => state.GetAllAmericanStatesReducer
  );

  const stateOptions =
    getAllAmericanStatesData?.map((state: string) => ({
      value: state,
      label: state,
    })) || [];
  const location = useLocation();

  const sameAsEmergencyContact = watch("sameAsEmergencyContact");
  const emergencyName = watch("emergencyName");
  const emergencyPhone = watch("emergencyPhone");
  const emergencyRelationship = watch("emergencyRelationship");
  const isResponsibleParty = watch("isResponsibleParty");
  const patientRelationship = watch("patientRelationship");
  const firstName = watch("firstName");
  const lastName = watch("lastName");
  const dateOfBirth = watch("dateOfBirth");

  const patchValues = useCallback(() => {
    setValue("guardianName", emergencyName || "");
    setValue("guardianPhone", emergencyPhone || "");
    setValue("guardianRelationship", emergencyRelationship);
    setValue("isResponsiblePartyClinician", isResponsibleParty);
  }, [
    emergencyName,
    emergencyPhone,
    emergencyRelationship,
    isResponsibleParty,
    setValue,
  ]);

  const clearGuardianFields = useCallback(() => {
    setValue("guardianName", "");
    setValue("guardianPhone", "");
    setValue("guardianRelationship", "");
    setValue("isResponsiblePartyClinician", false);
  }, [setValue]);

  const handleImageChange = async (file: File) => {
    try {
      const base64 = await convertFileToBase64(file);
      const base64WithoutPrefix = base64.substring(base64.indexOf(",") + 1);
      setValue("profileImage", file);
      setProfileImageFile(file);
      setProfileImageBase64(base64WithoutPrefix);
    } catch (error) {
      console.error("Error converting profile image to base64:", error);
    }
  };

  const handleRemoveProfileImage = () => {
    setValue("profileImage", null);
    setProfileImageFile(null);
    setProfileImageBase64(null);
    setProfileImageUrl(null);
  };

  const handleToggleSecondaryInsurance = () => {
    const newValue = !showSecondaryInsurance;
    setShowSecondaryInsurance(newValue);
    setValue("showSecondaryInsurance", newValue);
  };

  const paymentMethod = watch("paymentMethod");

  const { data: getAllSupervisingCliniciansData }: any =
    useSelector(
      (state: RootState) => state.GetAllSupervisingCliniciansReducer
    ) || {};

  const displaySupervisingClinicians = Object.entries(
    getAllSupervisingCliniciansData || {}
  ).map(([uuid, value]) => ({
    value: uuid,
    label: String(value),
  }));

  const onSubmit = async (data: any) => {
    try {
      const payload: any = {
        firstName: data?.firstName,
        middleName: data?.middleName,
        lastName: data?.lastName,
        preferredName: data?.preferredName,
        dateOfBirth: data?.dateOfBirth,
        legalSex: data?.legalSex,
        genderIdentity: data?.genderIdentity,
        emailId: data?.emailId || null,
        phoneNumber: data?.phoneNumber,
        ethnicity: data?.ethnicity,
        race: data?.race,
        preferredLanguage: data?.preferredLanguage,
        address: {
          line1: data?.addressLine1 || "",
          line2: data?.addressLine2 || "",
          city: data?.city || "",
          state: data?.state || "",
          zipcode: data?.zipcode || "",
        },
        emergencyContact: {
          name: data?.emergencyName || "",
          phoneNumber: data?.emergencyPhone || "",
          relationship: data?.emergencyRelationship || null,
          responsibleParty: data?.isResponsibleParty,
        },
        guardianContact: {
          name: data?.guardianName,
          phoneNumber: data?.guardianPhone,
          relationship: data?.guardianRelationship || null,
          responsibleParty: data?.isResponsiblePartyClinician,
        },
        primaryClinicianId: data?.primaryClinician,
        referringClinicianId: data?.secondaryClinician,

        phoneAppointmentReminder: data?.phoneAppointmentReminders,
        emailAppointmentRemainder: data?.emailAppointmentReminders,
        paymentMethod: data?.paymentMethod,
      };

      // Handle profile image
      let profileImageBase64Data = "";
      if (location?.state?.isEdit) {
        // In edit mode, only send profile image if it's changed
        if (profileImageBase64) {
          profileImageBase64Data = profileImageBase64;
        } else if (profileImageUrl) {
          profileImageBase64Data = await urlToBase64(profileImageUrl);
        }
      } else if (profileImageBase64) {
        // In create mode, send if image exists
        profileImageBase64Data = profileImageBase64;
      }

      if (profileImageBase64Data) {
        payload.profileImageUrl = profileImageBase64Data;
      }

      if (data?.paymentMethod === "INSURANCE") {
        const primaryInsurance = {
          insuranceName: data?.insuranceName || null,
          memberId: data?.memberId || null,
          groupId: data?.groupId || "",
          relationship: data?.patientRelationship || null,
          startDate: data?.startDatePrimary || null,
          endDate: data?.endDatePrimary || null,
          subscriberFirstName: data?.subscriberFirstName || null,
          subscriberLastName: data?.subscriberLastName || null,
          subscriberBirthDate: data?.subscriberDateOfBirthPrimary || null,
          insuranceType: "PRIMARY",
          insuranceCardFront: null as string | null,
          insuranceCardBack: null as string | null,
        };

        let primaryFrontBase64 = null;
        let primaryBackBase64 = null;

        if (location?.state?.isEdit) {
          if (primaryFrontPhoto) {
            primaryFrontBase64 = await urlToBase64(primaryFrontPhoto);
          }
          if (primaryBackPhoto) {
            primaryBackBase64 = await urlToBase64(primaryBackPhoto);
          }
        } else {
          if (primaryFrontPhoto) {
            primaryFrontBase64 = await urlToBase64(primaryFrontPhoto);
          }
          if (primaryBackPhoto) {
            primaryBackBase64 = await urlToBase64(primaryBackPhoto);
          }
        }

        primaryInsurance.insuranceCardFront = primaryFrontBase64;
        primaryInsurance.insuranceCardBack = primaryBackBase64;

        payload.clientInsurances = [primaryInsurance];

        if (showSecondaryInsurance) {
          const secondaryInsurance = {
            insuranceName: data?.secondaryInsuranceName || null,
            memberId: data?.secondaryMemberId || null,
            groupId: data?.secondaryGroupId || "",
            relationship: data?.secondaryPatientRelationship || null,
            startDate: data?.startDateSecondary || null,
            endDate: data?.endDateSecondary || null,
            subscriberFirstName: data?.secondarySubscriberFirstName || null,
            subscriberLastName: data?.secondarySubscriberLastName || null,
            subscriberBirthDate: data?.secondarySubscriberDateOfBirth || null,
            insuranceType: "SECONDARY",
            insuranceCardFront: null as string | null,
            insuranceCardBack: null as string | null,
          };

          let secondaryFrontBase64 = null;
          let secondaryBackBase64 = null;

          if (location?.state?.isEdit) {
            if (secondaryFrontPhoto) {
              secondaryFrontBase64 = await urlToBase64(secondaryFrontPhoto);
            }
            if (secondaryBackphoto) {
              secondaryBackBase64 = await urlToBase64(secondaryBackphoto);
            }
          } else {
            if (secondaryFrontPhoto) {
              secondaryFrontBase64 = await urlToBase64(secondaryFrontPhoto);
            }
            if (secondaryBackphoto) {
              secondaryBackBase64 = await urlToBase64(secondaryBackphoto);
            }
          }

          secondaryInsurance.insuranceCardFront = secondaryFrontBase64;
          secondaryInsurance.insuranceCardBack = secondaryBackBase64;

          payload.clientInsurances.push(secondaryInsurance);
        }
      }

      const updatedPayload = {
        ...payload,
        uuid: location?.state?.patientUuid,
      };

      if (location?.state?.isEdit) {
        dispatch(editPatient(updatedPayload as unknown as PatientTypes));
      } else {
        dispatch(addPatient(payload as unknown as AllTypes));
      }
    } catch (error) {
      dispatch(
        snackbarAction.showSnackbarAction({
          isSnackbarOpen: true,
          severity: AlertSeverity.ERROR,
          message: "Error processing images. Please try again.",
        })
      );
    }
  };

  useEffect(() => {
    dispatch(getAllAmericanStates());
  }, [dispatch]);

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  useEffect(() => {
    if (location?.state?.isEdit) {
      dispatch(getPatientById(uuid as string));
    }
  }, [dispatch, uuid]);

  useEffect(() => {
    if (getPatientByIdStatus === apiStatus.SUCCEEDED) {
      dispatch(getPatientByIdAction.resetCliniciansAction());
    }
    if (getPatientByIdStatus === apiStatus.LOADING) {
      dispatch(loaderAction.showLoader());
    } else {
      dispatch(loaderAction.hideLoader());
      if (getPatientByIdStatus === apiStatus.FAILED) {
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: getPatientByIdError || "Failed to get patient",
          })
        );
      }
    }
    dispatch(getPatientByIdAction.resetCliniciansAction());
  }, [getPatientByIdStatus, dispatch, getPatientByIdError]);

  useEffect(() => {
    switch (addPatientStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: addPatientData || "Client added successfully",
          })
        );
        dispatch(
          getAllPatients({
            xTenant: "default",
            size: 10,
            page: 0,
            searchString: "",
          } as any)
        );
        navigate(-1);
        dispatch(addPatientReducerAction.resetAddPatientReducer());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addPatientError,
          })
        );
        dispatch(addPatientReducerAction.resetAddPatientReducer());
        break;
    }
  }, [addPatientStatus, dispatch]);

  useEffect(() => {
    switch (editPatientStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: editPatientData || "Client updated successfully",
          })
        );
        dispatch(
          getAllPatients({
            xTenant: "default",
            size: 10,
            page: 0,
            searchString: "",
          } as any)
        );
        navigate(-1);
        dispatch(editPatientReducerAction.resetEditPatientReducer());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: editPatientError || "Failed to update client",
          })
        );
        dispatch(editPatientReducerAction.resetEditPatientReducer());
        break;
    }
  }, [editPatientStatus, dispatch]);

  useEffect(() => {
    if (sameAsEmergencyContact) {
      patchValues();
    } else {
      clearGuardianFields();
    }
  }, [sameAsEmergencyContact, patchValues, clearGuardianFields]);

  useEffect(() => {
    if (patientRelationship === "SELF") {
      setValue("subscriberFirstName", firstName);
      setValue("subscriberLastName", lastName);
      setValue("subscriberDateOfBirthPrimary", dateOfBirth);
    } else if (!uuid) {
      setValue("subscriberFirstName", "");
      setValue("subscriberLastName", "");
      setValue("subscriberDateOfBirthPrimary", "");
    }
  }, [patientRelationship, firstName, lastName, dateOfBirth, setValue, uuid]);

  const secondaryPatientRelationship = watch("secondaryPatientRelationship");

  useEffect(() => {
    if (secondaryPatientRelationship === "SELF" && !uuid) {
      setValue("secondarySubscriberFirstName", firstName);
      setValue("secondarySubscriberLastName", lastName);
      setValue("secondarySubscriberDateOfBirth", dateOfBirth);
    }
  }, [
    secondaryPatientRelationship,
    firstName,
    lastName,
    dateOfBirth,
    setValue,
    uuid,
  ]);

  useEffect(() => {
    if (
      !uuid &&
      getPatientByIdData === null &&
      addPatientStatus !== apiStatus.SUCCEEDED
    ) {
      reset();
      setProfileImageUrl(null);
      setPrimaryFrontPhoto(null);
      setPrimaryBackPhoto(null);
      setSecondaryFrontPhoto(null);
      setSecondaryBackPhoto(null);
      setShowSecondaryInsurance(false);
    }
  }, [getPatientByIdData, addPatientStatus, reset, uuid]);

  useEffect(() => {
    if (
      getPatientByIdData &&
      uuid &&
      getPatientByIdStatus === apiStatus.SUCCEEDED &&
      location?.state?.isEdit
    ) {
      const data = getPatientByIdData;
      setValue("firstName", data.firstName);
      setValue("middleName", data.middleName);
      setValue("lastName", data.lastName);
      setValue("preferredName", data.preferredName);
      setValue("dateOfBirth", data.dateOfBirth);
      setValue("legalSex", data.legalSex);
      setValue("genderIdentity", data.genderIdentity);
      setValue("emailId", data.emailId || "");
      setValue("phoneNumber", data.phoneNumber);
      setValue("ethnicity", data.ethnicity);
      setValue("race", data.race);
      setValue("preferredLanguage", data.preferredLanguage);

      if (data.address) {
        setValue("addressLine1", data.address.line1 || "");
        setValue("addressLine2", data.address.line2 || "");
        setValue("city", data.address.city || "");
        setValue("state", data.address.state || "");
        setValue("zipcode", data.address.zipcode || "");
      }

      if (data.emergencyContact) {
        setValue("emergencyName", data.emergencyContact.name || "");
        setValue("emergencyPhone", data.emergencyContact.phoneNumber || "");
        setValue("emergencyRelationship", data.emergencyContact.relationship);
        setValue("isResponsibleParty", data.emergencyContact.responsibleParty);
      }

      if (data.guardianContact) {
        setTimeout(() => {
          setValue("guardianName", data?.guardianContact?.name, {
            shouldDirty: true,
          });
          setValue("guardianPhone", data?.guardianContact?.phoneNumber, {
            shouldDirty: true,
          });
          setValue(
            "guardianRelationship",
            data?.guardianContact?.relationship,
            {
              shouldDirty: true,
            }
          );
          setValue(
            "isResponsiblePartyClinician",
            data?.guardianContact?.responsibleParty,
            { shouldDirty: true }
          );
        }, 0);
      }
      if (
        data.emergencyContact &&
        data.guardianContact &&
        data.emergencyContact.name === data.guardianContact.name &&
        data.emergencyContact.phoneNumber ===
          data.guardianContact.phoneNumber &&
        data.emergencyContact.relationship ===
          data.guardianContact.relationship &&
        data.emergencyContact.responsibleParty ===
          data.guardianContact.responsibleParty
      ) {
        setValue("sameAsEmergencyContact", true);
      } else {
        setValue("sameAsEmergencyContact", false);
      }
      setValue("primaryClinician", data.primaryClinicianId);
      setValue("secondaryClinician", data.referringClinicianId);
      setValue("phoneAppointmentReminders", data.phoneAppointmentReminder);
      setValue("emailAppointmentReminders", data.emailAppointmentRemainder);
      setValue("paymentMethod", data.paymentMethod);
      if (data.paymentMethod === "INSURANCE" && data.clientInsurances) {
        if (data.clientInsurances.length > 0) {
          const primaryInsurance = data.clientInsurances[0];
          setValue("insuranceName", primaryInsurance.insuranceName);
          setValue("memberId", primaryInsurance.memberId);
          setValue("groupId", primaryInsurance?.groupId || "");
          setValue("startDatePrimary", primaryInsurance.startDate);
          setValue("endDatePrimary", primaryInsurance.endDate);
          setValue("patientRelationship", primaryInsurance.relationship);
          setValue("subscriberFirstName", primaryInsurance.subscriberFirstName);
          setValue("subscriberLastName", primaryInsurance.subscriberLastName);
          setValue(
            "subscriberDateOfBirthPrimary",
            primaryInsurance.subscriberBirthDate
          );
          setPrimaryFrontPhoto(primaryInsurance.insuranceCardFront);
          setPrimaryBackPhoto(primaryInsurance.insuranceCardBack);
        }

        if (data.clientInsurances.length > 1) {
          const secondaryInsurance = data.clientInsurances[1];
          setShowSecondaryInsurance(true);
          setValue("showSecondaryInsurance", true);
          setValue("secondaryInsuranceName", secondaryInsurance.insuranceName);
          setValue("secondaryMemberId", secondaryInsurance.memberId);
          setValue("secondaryGroupId", secondaryInsurance?.groupId || "");
          setValue("startDateSecondary", secondaryInsurance.startDate);
          setValue("endDateSecondary", secondaryInsurance.endDate);
          setValue(
            "secondaryPatientRelationship",
            secondaryInsurance.relationship
          );
          setValue(
            "secondarySubscriberFirstName",
            secondaryInsurance.subscriberFirstName
          );
          setValue(
            "secondarySubscriberLastName",
            secondaryInsurance.subscriberLastName
          );
          setValue(
            "secondarySubscriberDateOfBirth",
            secondaryInsurance.subscriberBirthDate
          );
          setSecondaryFrontPhoto(secondaryInsurance.insuranceCardFront);
          setSecondaryBackPhoto(secondaryInsurance.insuranceCardBack);
        }
      }
      setProfileImageUrl(data.profileImageUrl);
    }
  }, [getPatientByIdData, setValue, uuid, getPatientByIdStatus]);

  return (
    <Grid
      sx={{
        position: "relative",
        maxHeight: "calc(100vh - 100px)",
        height: "calc(100vh - 100px)",
        overflow: "auto",
        display: "flex",
        flexDirection: "column",
        padding: "8px",
        "&::-webkit-scrollbar": {
          display: "none",
        },
        msOverflowStyle: "none",
        scrollbarWidth: "none",
      }}
    >
      <Grid
        display={"flex"}
        flexDirection={"row"}
        alignItems={"center"}
        justifyContent={"space-between"}
        bgcolor={"#F5F5F5"}
        p={0.5}
        borderRadius={2}
        mb={1}
        mr={2}
      >
        <Grid ml={1}>
          <Typography variant="bodyMedium4">
            {PatientFormSectionTitles.PATIENT_INFORMATION}
          </Typography>
        </Grid>
      </Grid>
      <form
        onSubmit={handleSubmit(onSubmit, (errors) =>
          console.log("Form errors on submit:", errors)
        )}
        style={{ minHeight: "100%" }}
      >
        <Grid bgcolor={"#FFFFFF"} borderRadius={2}>
          <Grid container borderRadius={2} pr={2} pl={2} pt={1} pb={1}>
            <Grid
              item
              xs={12}
              md={1.5}
              sx={{ display: "flex", justifyContent: "center" }}
            >
              <ImageUpload
                initialImage={profileImageUrl || ""}
                onImageChange={handleImageChange}
                size={150}
                showRemoveIcon={!!profileImageFile || !!profileImageUrl}
                onRemoveImage={handleRemoveProfileImage}
              />
            </Grid>

            <Grid item xs={12} md={10.5} pl={2}>
              <Grid container spacing={1.8}>
                <Grid item xs={12} md={2}>
                  <CustomLabel
                    label={PatientFormLabels.FIRST_NAME}
                    isRequired
                  />
                  <Controller
                    control={control}
                    name="firstName"
                    render={({ field }) => (
                      <CustomInput
                        placeholder={PatientFormPlaceholders.ENTER_FIRST_NAME}
                        {...field}
                        hasError={!!errors.firstName}
                        errorMessage={errors.firstName?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.MIDDLE_NAME} />
                  <Controller
                    control={control}
                    name="middleName"
                    render={({ field }) => (
                      <CustomInput
                        placeholder={PatientFormPlaceholders.ENTER_MIDDLE_NAME}
                        {...field}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.LAST_NAME} isRequired />
                  <Controller
                    control={control}
                    name="lastName"
                    render={({ field }) => (
                      <CustomInput
                        placeholder={PatientFormPlaceholders.ENTER_LAST_NAME}
                        {...field}
                        hasError={!!errors.lastName}
                        errorMessage={errors.lastName?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.PREFERRED_NAME} />
                  <Controller
                    control={control}
                    name="preferredName"
                    render={({ field }) => (
                      <CustomInput
                        placeholder={
                          PatientFormPlaceholders.ENTER_PREFERRED_NAME
                        }
                        {...field}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel
                    label={PatientFormLabels.DATE_OF_BIRTH}
                    isRequired
                  />
                  <Controller
                    control={control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <CustomDatePicker
                        placeholder={PatientFormPlaceholders.SELECT_DATE}
                        value={field.value || ""}
                        handleDateChange={field.onChange}
                        hasError={!!errors.dateOfBirth}
                        errorMessage={errors.dateOfBirth?.message}
                        disableFuture
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.LEGAL_SEX} />
                  <Controller
                    control={control}
                    name="legalSex"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={PatientFormPlaceholders.SELECT_LEGAL_SEX}
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(PatientGenderOptions).map(
                          ([value, label]) => ({
                            value: value,
                            label,
                          })
                        )}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.GENDER_IDENTITY} />
                  <Controller
                    control={control}
                    name="genderIdentity"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={
                          PatientFormPlaceholders.SELECT_GENDER_IDENTITY
                        }
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(PatientGenderOptions).map(
                          ([value, label]) => ({
                            value: value,
                            label,
                          })
                        )}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.EMAIL_ID} />
                  <Controller
                    control={control}
                    name="emailId"
                    render={({ field }) => (
                      <CustomInput
                        placeholder={PatientFormPlaceholders.ENTER_EMAIL_ID}
                        {...field}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel
                    label={PatientFormLabels.PHONE_NUMBER}
                    isRequired
                  />
                  <Controller
                    control={control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <CustomContactInputNew
                        {...field}
                        hasError={!!errors.phoneNumber}
                        errorMessage={errors.phoneNumber?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.ETHNICITY} />
                  <Controller
                    control={control}
                    name="ethnicity"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={PatientFormPlaceholders.SELECT_ETHNICITY}
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(PatientEthnicityOptions).map(
                          ([value, label]) => ({
                            value: value,
                            label,
                          })
                        )}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.RACE} />
                  <Controller
                    control={control}
                    name="race"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={PatientFormPlaceholders.SELECT_RACE}
                        {...field}
                        value={field.value || ""}
                        items={[
                          {
                            value: "hispanic_or_latino",
                            label: "Hispanic or Latino",
                          },
                          {
                            value: "not_hispanic_or_latino",
                            label: "Not Hispanic or Latino",
                          },
                          { value: "unknown", label: "Unknown" },
                          {
                            value: "declined_to_specify",
                            label: "Declined to Specify",
                          },
                          {
                            value: "american_indian_or_alaska_native",
                            label: "American Indian or Alaska Native",
                          },
                          { value: "asian", label: "Asian" },
                          {
                            value: "black_or_african_american",
                            label: "Black or African American",
                          },
                          {
                            value: "native_hawaiian_or_other_pacific_islander",
                            label: "Native Hawaiian or Other Pacific Islander",
                          },
                          { value: "white", label: "White" },
                          {
                            value: "two_or_more_races",
                            label: "Two or More Races",
                          },
                          { value: "other", label: "Other" },
                        ]}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={2}>
                  <CustomLabel label={PatientFormLabels.PREFERRED_LANGUAGE} />
                  <Controller
                    control={control}
                    name="preferredLanguage"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={
                          PatientFormPlaceholders.SELECT_PREFERRED_LANGUAGE
                        }
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(PatientLanguageOptions).map(
                          ([value, label]) => ({
                            value: value,
                            label,
                          })
                        )}
                      />
                    )}
                  />
                </Grid>

                <Grid
                  item
                  xs={12}
                  md={12}
                  display={"flex"}
                  gap={2}
                  flexDirection={"row"}
                >
                  <Grid item xs={12} md={1.89}>
                    <CustomLabel label={PatientFormLabels.ADDRESS_LINE_1} />
                    <Controller
                      control={control}
                      name="addressLine1"
                      render={({ field }) => (
                        <CustomInput
                          placeholder={
                            PatientFormPlaceholders.ENTER_ADDRESS_LINE_1
                          }
                          {...field}
                          hasError={!!errors.addressLine1}
                          errorMessage={errors.addressLine1?.message}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={1.89}>
                    <CustomLabel label={PatientFormLabels.ADDRESS_LINE_2} />
                    <Controller
                      control={control}
                      name="addressLine2"
                      render={({ field }) => (
                        <CustomInput
                          placeholder={
                            PatientFormPlaceholders.ENTER_ADDRESS_LINE_2
                          }
                          {...field}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={1.89}>
                    <CustomLabel label={PatientFormLabels.CITY} />
                    <Controller
                      control={control}
                      name="city"
                      render={({ field }) => (
                        <CustomInput
                          placeholder={PatientFormPlaceholders.ENTER_CITY}
                          {...field}
                          hasError={!!errors.city}
                          errorMessage={errors.city?.message}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={1.89}>
                    <CustomLabel label={PatientFormLabels.STATE} />
                    <Controller
                      control={control}
                      name="state"
                      render={({ field }) => (
                        <CustomSelect
                          placeholder={PatientFormPlaceholders.SELECT_STATE}
                          {...field}
                          value={field.value || ""}
                          items={stateOptions}
                          hasError={!!errors.state}
                          errorMessage={errors.state?.message}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={1.89}>
                    <CustomLabel label={PatientFormLabels.ZIPCODE} />
                    <Controller
                      control={control}
                      name="zipcode"
                      render={({ field }) => (
                        <CustomInput
                          placeholder={PatientFormPlaceholders.ENTER_ZIPCODE}
                          {...field}
                          hasError={!!errors.zipcode}
                          errorMessage={errors.zipcode?.message}
                          isNumeric={true}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <Grid
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          bgcolor={"#F5F5F5"}
          p={0.5}
          borderRadius={2}
          mb={1}
          mt={1}
          mr={2}
        >
          <Grid ml={1}>
            <Typography variant="bodyMedium4">
              {PatientFormSectionTitles.EMERGENCY_CONTACT}
            </Typography>
          </Grid>
        </Grid>

        <Grid borderRadius={2} ml={1} mb={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={2}>
              <CustomLabel label={PatientFormLabels.EMERGENCY_NAME} />
              <Controller
                control={control}
                name="emergencyName"
                render={({ field }) => (
                  <CustomInput
                    placeholder={PatientFormPlaceholders.ENTER_NAME}
                    {...field}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <CustomLabel label={PatientFormLabels.EMERGENCY_PHONE} />
              <Controller
                control={control}
                name="emergencyPhone"
                render={({ field }) => (
                  <CustomContactInputNew
                    {...field}
                    hasError={!!errors.emergencyPhone}
                    errorMessage={errors.emergencyPhone?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Grid container alignItems="flex-start" spacing={2}>
                <Grid item xs={9}>
                  <CustomLabel label={PatientFormLabels.RELATIONSHIP} />
                  <Controller
                    control={control}
                    name="emergencyRelationship"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={
                          PatientFormPlaceholders.SELECT_RELATIONSHIP
                        }
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(
                          PatientRelationshipWithPatientOptions
                        ).map(([value, label]) => ({
                          value: value,
                          label,
                        }))}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  item
                  xs={3}
                  width={"100%"}
                  display={"flex"}
                  alignItems={"center"}
                >
                  <FormControlLabel
                    control={
                      <Controller
                        name="isResponsibleParty"
                        control={control}
                        render={({ field }) => (
                          <Checkbox {...field} checked={!!field.value} />
                        )}
                      />
                    }
                    label={PatientFormLabels.RESPONSIBLE_PARTY}
                    sx={{
                      whiteSpace: "nowrap",
                      mt: 3,
                      "& .MuiFormControlLabel-label": {
                        whiteSpace: "nowrap",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <Grid
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          bgcolor={"#F5F5F5"}
          borderRadius={2}
          mb={1}
          mt={1}
          mr={2}
        >
          <Grid ml={1}>
            <Typography variant="bodyMedium4">
              {PatientFormSectionTitles.GUARDIAN_INFORMATION}
            </Typography>
          </Grid>
          <Grid mr={2} display={"flex"} flexDirection={"row"} gap={2}>
            <Grid display={"flex"} flexDirection={"row"} gap={2}>
              <Grid item xs={3} width={"100%"}>
                <FormControlLabel
                  control={
                    <Controller
                      name="sameAsEmergencyContact"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          checked={!!field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      )}
                    />
                  }
                  label={PatientFormLabels.SAME_AS_EMERGENCY_CONTACT}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <Grid borderRadius={2} ml={1} mb={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={2}>
              <CustomLabel label={PatientFormLabels.EMERGENCY_NAME} />
              <Controller
                control={control}
                name="guardianName"
                defaultValue=""
                render={({ field }) => (
                  <CustomInput
                    placeholder={PatientFormPlaceholders.ENTER_NAME}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <CustomLabel label={PatientFormLabels.EMERGENCY_PHONE} />
              <Controller
                control={control}
                name="guardianPhone"
                render={({ field }) => (
                  <CustomContactInputNew
                    {...field}
                    hasError={!!errors.guardianPhone}
                    errorMessage={errors.guardianPhone?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Grid container alignItems="flex-start" spacing={2}>
                <Grid item xs={9}>
                  <CustomLabel label={PatientFormLabels.RELATIONSHIP} />
                  <Controller
                    control={control}
                    name="guardianRelationship"
                    render={({ field }) => (
                      <CustomSelect
                        placeholder={
                          PatientFormPlaceholders.SELECT_RELATIONSHIP
                        }
                        {...field}
                        value={field.value || ""}
                        items={Object.entries(
                          PatientRelationshipWithPatientOptions
                        ).map(([value, label]) => ({
                          value: value,
                          label,
                        }))}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={3} width={"100%"}>
                  <FormControlLabel
                    control={
                      <Controller
                        name="isResponsiblePartyClinician"
                        control={control}
                        render={({ field }) => (
                          <Checkbox {...field} checked={!!field.value} />
                        )}
                      />
                    }
                    label={PatientFormLabels.RESPONSIBLE_PARTY}
                    sx={{
                      whiteSpace: "nowrap",
                      mt: 3,
                      "& .MuiFormControlLabel-label": {
                        whiteSpace: "nowrap",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <Grid
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          bgcolor={"#F5F5F5"}
          p={0.5}
          borderRadius={2}
          mb={1}
          mt={1}
          mr={2}
        >
          <Grid ml={1}>
            <Typography variant="bodyMedium4">
              {PatientFormSectionTitles.CLINICIAN_INFORMATION}
            </Typography>
          </Grid>
        </Grid>
        <Grid borderRadius={2} ml={1} mb={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={2}>
              <CustomLabel
                label={PatientFormLabels.PRIMARY_CLINICIAN}
                isRequired
              />
              <Controller
                control={control}
                name="primaryClinician"
                render={({ field }) => (
                  <CustomSelect
                    items={displaySupervisingClinicians}
                    placeholder={
                      PatientFormPlaceholders.SELECT_PRIMARY_CLINICIAN
                    }
                    {...field}
                    value={field.value || ""}
                    hasError={!!errors.primaryClinician}
                    errorMessage={errors.primaryClinician?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <CustomLabel label={PatientFormLabels.SECONDARY_CLINICIAN} />
              <Controller
                control={control}
                name="secondaryClinician"
                render={({ field }) => (
                  <CustomSelect
                    items={displaySupervisingClinicians}
                    placeholder={
                      PatientFormPlaceholders.SELECT_SECONDARY_CLINICIAN
                    }
                    {...field}
                    value={field.value || ""}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid
          display={"flex"}
          flexDirection={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          bgcolor={"#F5F5F5"}
          p={0.5}
          borderRadius={2}
          mb={1}
          mt={1}
          mr={2}
        >
          <Grid ml={1}>
            <Typography variant="bodyMedium4">
              {PatientFormSectionTitles.PRIVACY_CONSENT}
            </Typography>
          </Grid>
        </Grid>
        <Grid borderRadius={2} mb={2.1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={2} ml={1.5}>
              <Grid
                item
                xs={3}
                width={"100%"}
                display={"flex"}
                alignItems={"center"}
              >
                <FormControlLabel
                  control={
                    <Controller
                      name="phoneAppointmentReminders"
                      control={control}
                      render={({ field }) => (
                        <Checkbox {...field} checked={!!field.value} />
                      )}
                    />
                  }
                  label={PatientFormLabels.PHONE_APPOINTMENT_REMINDERS}
                  sx={{
                    whiteSpace: "nowrap",
                    "& .MuiFormControlLabel-label": {
                      whiteSpace: "nowrap",
                    },
                  }}
                />
              </Grid>
            </Grid>

            <Grid item xs={12} md={2}>
              <Grid
                item
                xs={3}
                width={"100%"}
                display={"flex"}
                alignItems={"center"}
              >
                <FormControlLabel
                  control={
                    <Controller
                      name="emailAppointmentReminders"
                      control={control}
                      render={({ field }) => (
                        <Checkbox {...field} checked={!!field.value} />
                      )}
                    />
                  }
                  label={PatientFormLabels.EMAIL_APPOINTMENT_REMINDERS}
                  sx={{
                    whiteSpace: "nowrap",
                    "& .MuiFormControlLabel-label": {
                      whiteSpace: "nowrap",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid pb={2}>
          <Box sx={{ width: "100%" }}>
            <Box sx={{ width: "100%" }}>
              <Grid
                display={"flex"}
                flexDirection={"row"}
                alignItems={"center"}
                justifyContent={"space-between"}
                bgcolor={"#F5F5F5"}
                p={0.5}
                borderRadius={2}
                mb={1}
                mr={2}
              >
                <Grid ml={1}>
                  <Typography variant="bodyMedium4">
                    {PatientFormSectionTitles.PAYMENT_METHOD}
                  </Typography>
                </Grid>
              </Grid>

              <Grid
                bgcolor={"#FFFFFF"}
                borderRadius={2}
                mb={1}
                display={"flex"}
                flexDirection={"column"}
              >
                <Grid container borderRadius={2}>
                  <Controller
                    control={control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <RadioGroup
                        row
                        {...field}
                        value={field.value || ""}
                        sx={{ gap: 4, marginLeft: 1 }}
                      >
                        <FormControlLabel
                          value="SELF_PAY"
                          control={<Radio />}
                          label="Self Pay"
                        />
                        <FormControlLabel
                          value="INSURANCE"
                          control={<Radio />}
                          label="Insurance"
                        />
                      </RadioGroup>
                    )}
                  />
                </Grid>
                <Grid ml={1}>
                  {errors.paymentMethod && (
                    <Typography color="error" variant="bodyRegular5">
                      {errors.paymentMethod.message}
                    </Typography>
                  )}
                </Grid>
              </Grid>

              {paymentMethod === "INSURANCE" && (
                <>
                  <Grid
                    display={"flex"}
                    flexDirection={"row"}
                    alignItems={"center"}
                    justifyContent={"space-between"}
                    bgcolor={"#F5F5F5"}
                    p={0.5}
                    borderRadius={2}
                    mb={1}
                    mt={1}
                    mr={2}
                  >
                    <Grid ml={1}>
                      <Typography variant="bodyMedium4">
                        {PatientFormSectionTitles.PRIMARY_INSURANCE}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Box mb={2}>
                    <Grid
                      bgcolor={"#FFFFFF"}
                      borderRadius={2}
                      sx={{ width: "100%" }}
                    >
                      <Grid container p={1} sx={{ width: "100%" }}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormLabels.INSURANCE_NAME}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="insuranceName"
                              render={({ field }) => (
                                <CustomSelect
                                  placeholder={
                                    PatientFormPlaceholders.SELECT_INSURANCE_NAME
                                  }
                                  name={field.name}
                                  value={field.value || ""}
                                  onChange={(e) => {
                                    field.onChange(e.target.value);
                                  }}
                                  items={[
                                    {
                                      value: "Aetna Insurance",
                                      label: PatientInsuranceOptions.AETNA,
                                    },
                                    {
                                      value: "Bluecross Insurance",
                                      label: PatientInsuranceOptions.BLUE_CROSS,
                                    },
                                    {
                                      value: "United Insurance",
                                      label: PatientInsuranceOptions.UNITED,
                                    },
                                    {
                                      value: "Cigna Insurance",
                                      label: PatientInsuranceOptions.CIGNA,
                                    },
                                  ]}
                                  hasError={!!errors.insuranceName}
                                  errorMessage={errors.insuranceName?.message}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormLabels.MEMBER_ID}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="memberId"
                              render={({ field }) => (
                                <CustomInput
                                  placeholder={
                                    PatientFormPlaceholders.ENTER_MEMBER_ID
                                  }
                                  {...field}
                                  hasError={!!errors.memberId}
                                  errorMessage={errors.memberId?.message}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={2}>
                            <CustomLabel label={PatientFormLabels.GROUP_ID} />
                            <Controller
                              control={control}
                              name="groupId"
                              render={({ field }) => (
                                <CustomInput
                                  placeholder={
                                    PatientFormPlaceholders.ENTER_GROUP_ID
                                  }
                                  {...field}
                                  hasError={!!errors.groupId}
                                  errorMessage={errors.groupId?.message}
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormPlaceholders.START_DATE}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="startDatePrimary"
                              render={({ field }) => (
                                <CustomDatePicker
                                  placeholder={
                                    PatientFormPlaceholders.SELECT_DATE
                                  }
                                  value={field.value || ""}
                                  handleDateChange={field.onChange}
                                  hasError={!!errors.startDatePrimary}
                                  errorMessage={
                                    errors.startDatePrimary?.message
                                  }
                                  disableFuture
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormPlaceholders.END_DATE}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="endDatePrimary"
                              render={({ field }) => (
                                <CustomDatePicker
                                  placeholder={
                                    PatientFormPlaceholders.SELECT_DATE
                                  }
                                  value={field.value || ""}
                                  handleDateChange={field.onChange}
                                  hasError={!!errors.endDatePrimary}
                                  errorMessage={errors.endDatePrimary?.message}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12}>
                            <CustomLabel
                              label={PatientFormLabels.PATIENT_RELATIONSHIP}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="patientRelationship"
                              render={({ field }) => (
                                <RadioGroup
                                  row
                                  {...field}
                                  sx={{ gap: 4, marginTop: 1 }}
                                >
                                  <FormControlLabel
                                    value="SELF"
                                    control={<Radio />}
                                    label={PatientRelationshipOptions.SELF}
                                  />
                                  <FormControlLabel
                                    value="SPOUSE"
                                    control={<Radio />}
                                    label={PatientRelationshipOptions.SPOUSE}
                                  />
                                  <FormControlLabel
                                    value="CHILD"
                                    control={<Radio />}
                                    label={PatientRelationshipOptions.CHILD}
                                  />
                                  <FormControlLabel
                                    value="DEPENDENT"
                                    control={<Radio />}
                                    label={PatientRelationshipOptions.DEPENDENT}
                                  />
                                </RadioGroup>
                              )}
                            />
                            <Grid>
                              {errors.patientRelationship && (
                                <Typography
                                  color="error"
                                  variant="bodyRegular5"
                                >
                                  {errors.patientRelationship.message}
                                </Typography>
                              )}
                            </Grid>
                          </Grid>

                          <Grid
                            display={"flex"}
                            flexDirection={"row"}
                            alignItems={"center"}
                            justifyContent={"space-between"}
                            bgcolor={"#F5F5F5"}
                            p={0.5}
                            borderRadius={2}
                            mt={1}
                            xs={12}
                            ml={1}
                          >
                            <Grid ml={1} mr={2}>
                              <Typography variant="bodyMedium4">
                                {PatientFormSectionTitles.SUBSCRIBER_DETAILS}
                              </Typography>
                            </Grid>
                          </Grid>

                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormLabels.FIRST_NAME}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="subscriberFirstName"
                              render={({ field }) => (
                                <CustomInput
                                  placeholder={
                                    PatientFormPlaceholders.ENTER_FIRST_NAME
                                  }
                                  {...field}
                                  hasError={!!errors.subscriberFirstName}
                                  errorMessage={
                                    errors.subscriberFirstName?.message
                                  }
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormLabels.LAST_NAME}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="subscriberLastName"
                              render={({ field }) => (
                                <CustomInput
                                  placeholder={
                                    PatientFormPlaceholders.ENTER_LAST_NAME
                                  }
                                  {...field}
                                  hasError={!!errors.subscriberLastName}
                                  errorMessage={
                                    errors.subscriberLastName?.message
                                  }
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={2}>
                            <CustomLabel
                              label={PatientFormLabels.DATE_OF_BIRTH}
                              isRequired
                            />
                            <Controller
                              control={control}
                              name="subscriberDateOfBirthPrimary"
                              render={({ field }) => (
                                <CustomDatePicker
                                  placeholder={
                                    PatientFormPlaceholders.SELECT_DATE
                                  }
                                  value={field.value || ""}
                                  handleDateChange={field.onChange}
                                  hasError={
                                    !!errors.subscriberDateOfBirthPrimary
                                  }
                                  errorMessage={
                                    errors.subscriberDateOfBirthPrimary?.message
                                  }
                                />
                              )}
                            />
                          </Grid>

                          <Grid
                            display={"flex"}
                            flexDirection={"row"}
                            alignItems={"center"}
                            justifyContent={"space-between"}
                            bgcolor={"#F5F5F5"}
                            p={0.5}
                            borderRadius={2}
                            mt={2}
                            mb={2}
                            xs={12}
                            ml={1}
                          >
                            <Grid ml={1} mr={2}>
                              <Typography variant="bodyMedium4">
                                {PatientFormSectionTitles.UPLOAD_INSURANCE_CARD}
                              </Typography>
                            </Grid>
                          </Grid>
                          <Grid container spacing={2} width={"50%"} ml={0.1}>
                            <Grid item xs={12} md={6}>
                              <CustomMultipleFilesUpload
                                onUpload={(base64: string) => {
                                  setPrimaryFrontPhoto(base64);
                                }}
                                placeholder={
                                  UploadFileComponentConstants.FRONT_OF_CARD
                                }
                                initialValue={primaryFrontPhoto || undefined}
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <CustomMultipleFilesUpload
                                onUpload={(base64: string) => {
                                  setPrimaryBackPhoto(base64);
                                }}
                                placeholder={
                                  UploadFileComponentConstants.BACK_OF_CARD
                                }
                                initialValue={primaryBackPhoto || undefined}
                              />
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Box>

                  <Box
                    sx={{
                      cursor: "pointer",
                      mb: 2,
                      display: "flex",
                      alignItems: "center",
                      pl: 1,
                      pb: 3,
                    }}
                    onClick={handleToggleSecondaryInsurance}
                  >
                    <Typography
                      variant="bodyMedium4"
                      sx={{
                        color: "#145DA0",
                        textDecoration: "underline",
                      }}
                    >
                      {showSecondaryInsurance
                        ? "Remove Secondary Insurance"
                        : "Add Secondary Insurance"}
                    </Typography>
                  </Box>

                  {showSecondaryInsurance && (
                    <Box mb={1} pb={5}>
                      <Grid
                        display={"flex"}
                        flexDirection={"row"}
                        alignItems={"center"}
                        justifyContent={"space-between"}
                        bgcolor={"#F5F5F5"}
                        p={0.5}
                        borderRadius={2}
                        mb={1}
                        mt={1}
                      >
                        <Grid ml={1}>
                          <Typography variant="bodyMedium4">
                            {PatientFormSectionTitles.SECONDARY_INSURANCE}
                          </Typography>
                        </Grid>
                      </Grid>

                      <Grid
                        bgcolor={"#FFFFFF"}
                        borderRadius={2}
                        sx={{ width: "100%" }}
                      >
                        <Grid container p={1} sx={{ width: "100%" }}>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormLabels.INSURANCE_NAME}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondaryInsuranceName"
                                render={({ field }) => (
                                  <CustomSelect
                                    placeholder={
                                      PatientFormPlaceholders.SELECT_INSURANCE_NAME
                                    }
                                    name={field.name}
                                    value={field.value || ""}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                    }}
                                    items={[
                                      {
                                        value: "Aetna Insurance",
                                        label: PatientInsuranceOptions.AETNA,
                                      },
                                      {
                                        value: "Bluecross Insurance",
                                        label:
                                          PatientInsuranceOptions.BLUE_CROSS,
                                      },
                                      {
                                        value: "United Insurance",
                                        label: PatientInsuranceOptions.UNITED,
                                      },
                                      {
                                        value: "Cigna Insurance",
                                        label: PatientInsuranceOptions.CIGNA,
                                      },
                                    ]}
                                    hasError={!!errors.secondaryInsuranceName}
                                    errorMessage={
                                      errors.secondaryInsuranceName?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormLabels.MEMBER_ID}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondaryMemberId"
                                render={({ field }) => (
                                  <CustomInput
                                    placeholder={
                                      PatientFormPlaceholders.ENTER_MEMBER_ID
                                    }
                                    {...field}
                                    hasError={!!errors.secondaryMemberId}
                                    errorMessage={
                                      errors.secondaryMemberId?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel label={PatientFormLabels.GROUP_ID} />
                              <Controller
                                control={control}
                                name="secondaryGroupId"
                                render={({ field }) => (
                                  <CustomInput
                                    placeholder={
                                      PatientFormPlaceholders.ENTER_GROUP_ID
                                    }
                                    {...field}
                                    hasError={!!errors.secondaryGroupId}
                                    errorMessage={
                                      errors.secondaryGroupId?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormPlaceholders.START_DATE}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="startDateSecondary"
                                render={({ field }) => (
                                  <CustomDatePicker
                                    placeholder={
                                      PatientFormPlaceholders.SELECT_DATE
                                    }
                                    value={field.value || ""}
                                    handleDateChange={field.onChange}
                                    hasError={!!errors.startDateSecondary}
                                    errorMessage={
                                      errors.startDateSecondary?.message
                                    }
                                    disableFuture
                                  />
                                )}
                              />
                            </Grid>
                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormPlaceholders.END_DATE}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="endDateSecondary"
                                render={({ field }) => (
                                  <CustomDatePicker
                                    placeholder={
                                      PatientFormPlaceholders.SELECT_DATE
                                    }
                                    value={field.value || ""}
                                    handleDateChange={field.onChange}
                                    hasError={!!errors.endDateSecondary}
                                    errorMessage={
                                      errors.endDateSecondary?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12}>
                              <CustomLabel
                                label={PatientFormLabels.PATIENT_RELATIONSHIP}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondaryPatientRelationship"
                                render={({ field }) => (
                                  <RadioGroup
                                    row
                                    {...field}
                                    sx={{ gap: 4, marginTop: 1 }}
                                  >
                                    <FormControlLabel
                                      value="SELF"
                                      control={<Radio />}
                                      label={PatientRelationshipOptions.SELF}
                                    />
                                    <FormControlLabel
                                      value="SPOUSE"
                                      control={<Radio />}
                                      label={PatientRelationshipOptions.SPOUSE}
                                    />
                                    <FormControlLabel
                                      value="CHILD"
                                      control={<Radio />}
                                      label={PatientRelationshipOptions.CHILD}
                                    />
                                    <FormControlLabel
                                      value="DEPENDENT"
                                      control={<Radio />}
                                      label={
                                        PatientRelationshipOptions.DEPENDENT
                                      }
                                    />
                                  </RadioGroup>
                                )}
                              />
                              <Grid>
                                {errors.secondaryPatientRelationship && (
                                  <Typography
                                    color="error"
                                    variant="bodyRegular5"
                                  >
                                    {
                                      errors.secondaryPatientRelationship
                                        .message
                                    }
                                  </Typography>
                                )}
                              </Grid>
                            </Grid>

                            <Grid
                              display={"flex"}
                              flexDirection={"row"}
                              alignItems={"center"}
                              justifyContent={"space-between"}
                              bgcolor={"#F5F5F5"}
                              p={0.5}
                              borderRadius={2}
                              mt={1}
                              xs={12}
                              ml={1}
                            >
                              <Grid ml={1} mr={2}>
                                <Typography variant="bodyMedium4">
                                  {PatientFormSectionTitles.SUBSCRIBER_DETAILS}
                                </Typography>
                              </Grid>
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormLabels.FIRST_NAME}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondarySubscriberFirstName"
                                render={({ field }) => (
                                  <CustomInput
                                    placeholder={
                                      PatientFormPlaceholders.ENTER_FIRST_NAME
                                    }
                                    {...field}
                                    hasError={
                                      !!errors.secondarySubscriberFirstName
                                    }
                                    errorMessage={
                                      errors.secondarySubscriberFirstName
                                        ?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormLabels.LAST_NAME}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondarySubscriberLastName"
                                render={({ field }) => (
                                  <CustomInput
                                    placeholder={
                                      PatientFormPlaceholders.ENTER_LAST_NAME
                                    }
                                    {...field}
                                    hasError={
                                      !!errors.secondarySubscriberLastName
                                    }
                                    errorMessage={
                                      errors.secondarySubscriberLastName
                                        ?.message
                                    }
                                  />
                                )}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <CustomLabel
                                label={PatientFormLabels.DATE_OF_BIRTH}
                                isRequired
                              />
                              <Controller
                                control={control}
                                name="secondarySubscriberDateOfBirth"
                                render={({ field }) => (
                                  <CustomDatePicker
                                    placeholder={
                                      PatientFormPlaceholders.SELECT_DATE
                                    }
                                    value={field.value || ""}
                                    handleDateChange={field.onChange}
                                    hasError={
                                      !!errors.secondarySubscriberDateOfBirth
                                    }
                                    errorMessage={
                                      errors.secondarySubscriberDateOfBirth
                                        ?.message
                                    }
                                    disableFuture
                                  />
                                )}
                              />
                            </Grid>

                            <Grid
                              display={"flex"}
                              flexDirection={"row"}
                              alignItems={"center"}
                              justifyContent={"space-between"}
                              bgcolor={"#F5F5F5"}
                              p={0.5}
                              borderRadius={2}
                              mt={2}
                              mb={2}
                              xs={12}
                              ml={1}
                            >
                              <Grid ml={1} mr={2}>
                                <Typography variant="bodyMedium4">
                                  {
                                    PatientFormSectionTitles.UPLOAD_INSURANCE_CARD
                                  }
                                </Typography>
                              </Grid>
                            </Grid>
                            <Grid container spacing={2} width={"50%"} ml={0.1}>
                              <Grid item xs={12} md={6}>
                                <CustomMultipleFilesUpload
                                  onUpload={(base64: string) => {
                                    setSecondaryFrontPhoto(base64);
                                  }}
                                  placeholder={
                                    UploadFileComponentConstants.FRONT_OF_CARD
                                  }
                                  initialValue={
                                    secondaryFrontPhoto || undefined
                                  }
                                />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <CustomMultipleFilesUpload
                                  onUpload={(base64: string) => {
                                    setSecondaryBackPhoto(base64);
                                  }}
                                  placeholder={
                                    UploadFileComponentConstants.BACK_OF_CARD
                                  }
                                  initialValue={secondaryBackphoto || undefined}
                                />
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                </>
              )}
            </Box>

            <Box
              sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                bgcolor: "background.paper",
                borderTop: "1px solid #E0E0E0",
                p: 1.5,
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                zIndex: 1000,
                boxShadow: "0px -4px 10px rgba(0, 0, 0, 0.05)",
              }}
            >
              <CustomButton
                variant="outline"
                label={PatientFormButtons.CANCEL}
                isSubmitButton
                onClick={() => {
                  navigate(-1);
                }}
              />
              <CustomButton
                variant="filled"
                label={PatientFormButtons.SAVE}
                type="submit"
                changePadding={false}
              />
            </Box>
          </Box>
        </Grid>
      </form>
    </Grid>
  );
};

export default DemographicTab;
