import { ValidationMessages } from "../../../../../constants/formConst";
import * as yup from "yup";

export const AddPatientSchema = yup.object().shape({
  firstName: yup
    .string()
    .required("First name is required")
    .min(2, "First name must be at least 2 characters")
    .matches(
      /^[a-zA-Z0-9]+$/,
      "First name cannot contain spaces or special characters"
    ),
  middleName: yup.string().optional(),
  lastName: yup
    .string()
    .required("Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .matches(
      /^[a-zA-Z0-9]+$/,
      "Last name cannot contain spaces or special characters"
    ),
  preferredName: yup.string().optional(),
  dateOfBirth: yup.string().required("Date of birth is required"),
  // .test("valid-date", "Please enter a valid date", (value) => {
  //   if (!value) return false;
  //   const date = new Date(value);
  //   const currentDate = new Date();
  //   const year = date.getFullYear();

  //   return (
  //     date instanceof Date &&
  //     !isNaN(date.getTime()) &&
  //     year >= 1900 &&
  //     date <= currentDate
  //   );
  // }),
  legalSex: yup.string().optional(),
  genderIdentity: yup.string().optional(),
  phoneNumber: yup
    .string()
    .required("Phone Number is required")
    .min(12, "Phone Number must be of minimum 10 digits")
    .max(13, "Phone Number must be at most 10 digits"),
  emailId: yup
    .string()
    .optional()
    .transform((value) => value?.toLowerCase())
    .matches(/(^$)|(^\w+([\.-]?\w+)*(\+\w+)?@\w+([\.-]?\w+)*(\.\w{2,})$)/, {
      message: ValidationMessages.ValidEmailRequired,
    })
    .max(255, ValidationMessages.EmailMaxLength),
  ethnicity: yup.string().optional(),
  race: yup.string().optional(),
  preferredLanguage: yup.string().optional(),

  // Address
  addressLine1: yup.string().optional(),
  addressLine2: yup.string().optional(),
  city: yup.string().optional(),
  state: yup.string().optional(),
  zipcode: yup
    .string()
    .optional()
    .test(
      "zipCode-format",
      "Zipcode should be either 5 or 9 digits",
      (value) => {
        if (!value) return true; // allow empty
        return /^\d{5}(-?\d{4})?$/.test(value);
      }
    ),

  // Emergency Contact
  emergencyName: yup.string().optional(),
  emergencyPhone: yup.string().optional(),
  // .required("Contact Number is required")
  // .min(12, "Contact Number must be of minimum 10 digits")
  // .max(14, "Contact Number must be at most 10 digits"),
  relationship: yup.string().optional(),
  isResponsibleParty: yup.boolean().optional(),

  // Clinician Information
  primaryClinician: yup.string().required("Primary clinician is required"),
  secondaryClinician: yup.string().optional(),
  sameAsEmergencyContact: yup.boolean().optional(),
  isResponsiblePartyClinician: yup.boolean().optional(),

  // Appointment Reminders
  phoneAppointmentReminders: yup.boolean().optional(),
  emailAppointmentReminders: yup.boolean().optional(),

  // Payment Method
  paymentMethod: yup.string().required("Payment method is required"),

  // Primary Insurance - Only required if paymentMethod is "insurance"
  insuranceName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Insurance name is required"),
  }),
  memberId: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Member ID is required"),
  }),
  groupId: yup.string(),
  patientRelationship: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Client relationship is required"),
  }),
  subscriberFirstName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Subscriber first name is required"),
  }),
  subscriberLastName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Subscriber last name is required"),
  }),
  subscriberDateOfBirthPrimary: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Subscriber date of birth is required"),
  }),

  startDatePrimary: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Start date is required"),
  }),
  endDatePrimary: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("End date is required"),
  }),

  // Secondary Insurance - Required fields when secondary insurance is shown
  secondaryInsuranceName: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) => schema.required("Secondary insurance name is required"),
    }),
  secondaryMemberId: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) => schema.required("Secondary member ID is required"),
    }),
  secondaryGroupId: yup.string(),
  secondaryPatientRelationship: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) =>
        schema.required("Secondary client relationship is required"),
    }),
  secondarySubscriberFirstName: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) =>
        schema.required("Secondary subscriber first name is required"),
    }),
  secondarySubscriberLastName: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) =>
        schema.required("Secondary subscriber last name is required"),
    }),
  secondarySubscriberDateOfBirth: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) =>
        schema.required("Secondary subscriber date of birth is required"),
    }),

  startDateSecondary: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) => schema.required("Secondary start date is required"),
    }),
  endDateSecondary: yup
    .string()
    .when(["paymentMethod", "showSecondaryInsurance"], {
      is: (paymentMethod: string, showSecondaryInsurance: boolean) =>
        paymentMethod === "INSURANCE" && showSecondaryInsurance,
      then: (schema) => schema.required("Secondary end date is required"),
    }),

  showSecondaryInsurance: yup.boolean().optional(),
});

export const AddPatientInsuranceSchema = yup.object().shape({
  paymentMethod: yup.string().required("Payment method is required"),
  insuranceName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Insurance name is required"),
  }),
  memberId: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Member ID is required"),
  }),
  groupId: yup.string(),
  patientRelationship: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Client relationship is required"),
  }),
  firstName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("First name is required"),
  }),
  lastName: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Last name is required"),
  }),
  subscriberDateOfBirthPrimary: yup.string().when("paymentMethod", {
    is: "INSURANCE",
    then: (schema) => schema.required("Date of birth is required"),
  }),
});
