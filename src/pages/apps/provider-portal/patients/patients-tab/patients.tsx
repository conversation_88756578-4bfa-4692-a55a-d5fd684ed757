import AddIcon from "@mui/icons-material/Add";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import SearchIcon from "@mui/icons-material/Search";
import { Dialog, Grid, SelectChangeEvent, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "src/redux/store";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import CustomInput from "../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import { patientTableHeaders } from "../../../../../common-components/headers/all-headers";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";
import CustomisedTable from "../../../../../common-components/table/table";
import {
  capitalizeFirstLetter,
  formatDateToMMDDYYYY,
  formatPhoneNumber,
  formatTitle,
} from "../../../../../common-components/utils/stringUtils";
import {
  PatientFormButtons,
  PatientTableLabels,
  PatientTemplateActions,
  SettingsFormConstants,
} from "../../../../../constants/formConst";
import { PatientData } from "../../../../../models/all-const";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import { getAllPatients } from "../../../../../redux/auth/patient/get-all-patients-reducer";
import { getAllSupervisingClinicians } from "../../../../../redux/auth/profile/get-all-supervising-clinicians";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AvailabilityConstants } from "../../availability/model/availabilityModel";
import ImportPatientsDialog from "./import-patients-dialog";
export interface PatientSearchParams {
  size: number;
  page: number;
  searchString?: string;
  status?: string;
  insurance?: string;
}

const Patients = () => {
  const navigate = useNavigate();
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [pageDisplaySize, setPageDisplaySize] = useState("10");
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [, setTotalElements] = useState<number>(0);
  const [searchString, setSearchString] = useState("");
  const [selectedPatientStatus, setSelectedPatientStatus] =
    useState<string>("");
  const [selectedPatientFilter, setSelectedPatientFilter] =
    useState<string>("");
  const [selectedPatientInsurance, setSelectedPatientInsurance] =
    useState<string>("");
  const [tableData, setTableData] = useState<any>();
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();

  const patientSearchOption = [
    { value: "ACTIVE", label: "Active" },
    { value: "NEW", label: "New" },
    { value: "DISCHARGED", label: "Discharged" },
    { value: "", label: "All" },
  ];

  const patientFilterOption = [
    { value: "name", label: "Name" },
    { value: "dob", label: "DOB" },
    { value: "insurance", label: "Insurance" },
  ];

  const patientInsuranceOption = [
    { value: "SELF_PAY", label: "Self Pay" },
    { value: "INSURANCE", label: "Insurance" },
    { value: "", label: "All" },
  ];

  const { data: getAllSupervisingCliniciansData } = useSelector(
    (state: RootState) => state.GetAllSupervisingCliniciansReducer
  );

  const { data: getAllPatientsDataTable, status: getAllPatientsStatus } =
    useSelector((state: RootState) => state.GetAllPatientsReducer);

  const displaySupervisingClinicians = [
    { value: "", label: "All" },
    ...Object.entries(getAllSupervisingCliniciansData || {}).map(
      ([uuid, value]) => ({
        value: uuid,
        label: String(value),
      })
    ),
  ];

  const handleAddPatient = () => {
    navigate("/admin/patients/add-patient");
  };

  const handleNavigate = (row: PatientData) => {
    navigate("/admin/patients/patient-profile", {
      state: row?.uuid,
    });
  };

  // const handleImportClick = () => {
  //   setImportDialogOpen(true);
  // };

  const handlePageChange = (value: number) => {
    const newPage = value - 1;
    setPage(newPage);
  };

  const handlePageSizeChange = (size: number) => {
    const newSize = size.toString();
    setPageDisplaySize(newSize);
    setPage(0);
  };

  useEffect(() => {
    if (getAllPatientsDataTable) {
      const {
        content,
        totalPages: total,
        totalElements: elements,
      } = getAllPatientsDataTable;
      setTotalPages(total);
      setTotalElements(elements);

      if (content?.length === 0) {
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: "No client found with selected filters",
          })
        );
        dispatch(snackbarAction.hideSnackbarAction());
      }

      const modifiedPatientsData = content?.map((patient: any) => {
        return {
          patientName:
            patient?.preferredName ||
            `${patient?.firstName} ${patient?.lastName}`,
          clinician: formatTitle(patient?.clinicianName),
          memberSince: formatDateToMMDDYYYY(patient?.memberSince),
          dob: formatDateToMMDDYYYY(patient?.dateOfBirth),
          emailId: patient?.emailId,
          contactNumber: `${formatPhoneNumber(patient?.phoneNumber)}`,
          paymentMethod: capitalizeFirstLetter(patient?.paymentMethodName),
          status: patient?.clientStatus,

          uuid: patient?.uuid,
          alertNote: patient?.alertNote,
          mrn: patient?.mrn,
        };
      });
      setTableData(modifiedPatientsData);
    }
  }, [getAllPatientsDataTable]);

  useEffect(() => {
    const timerId = setTimeout(() => {
      dispatch(
        getAllPatients({
          size: parseInt(pageDisplaySize),
          page: page,
          searchString: searchString || "",
          status: selectedPatientStatus || "",
          insurance: selectedPatientInsurance || "",
          clinicianId: selectedProvider || "",
          filter: selectedPatientFilter || null,
        })
      );
    }, 500);

    return () => {
      clearTimeout(timerId);
    };
  }, [
    dispatch,
    pageDisplaySize,
    page,
    searchString,
    selectedPatientStatus,
    selectedPatientInsurance,
    selectedProvider,
  ]);

  useEffect(() => {
    switch (getAllPatientsStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getAllPatientsStatus, dispatch]);

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  return (
    <Grid container flexDirection="column" gap={2}>
      <Grid flexDirection="row" display="flex">
        <Grid display="flex" alignItems="center" mt={3}>
          <Typography variant="bodySemiBold2">Clients</Typography>
        </Grid>
        <Grid
          flexDirection="row"
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
          flex={1}
          gap={2}
        >
          <Grid width={"11vw"}>
            <Grid mb={1}>
              <CustomLabel label={PatientTableLabels.FILTER_SEARCH} />
            </Grid>
            <CustomSelect
              crossable
              placeholder={PatientTableLabels.FILTER}
              value={selectedPatientFilter}
              items={patientFilterOption}
              backgroundColor="#FFFFFF"
              onChange={(e: SelectChangeEvent<string>) => {
                setSelectedPatientFilter(e.target.value);
                setPage(0);
              }}
            />
          </Grid>

          <Grid width={"20vw"}>
            <Grid mb={1}>
              <CustomLabel label={PatientTableLabels.SEARCH} />
            </Grid>
            <CustomInput
              showIcon={<SearchIcon />}
              bgWhite
              placeholder={PatientTableLabels.SEARCH}
              value={searchString}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setSearchString(e.target.value);
                setPage(0);
              }}
            />
          </Grid>
          <Grid width={"9vw"}>
            <Grid mb={1}>
              <CustomLabel label={PatientTableLabels.STATUS} />
            </Grid>
            <CustomSelect
              placeholder={PatientTableLabels.STATUS}
              value={selectedPatientStatus}
              items={patientSearchOption}
              backgroundColor="#FFFFFF"
              onChange={(e: SelectChangeEvent<string>) => {
                setSelectedPatientStatus(e.target.value);
                setPage(0);
              }}
            />
          </Grid>

          <Grid width={"12vw"}>
            <Grid mb={1}>
              <CustomLabel label={AvailabilityConstants.FILTER_BY_CLINICIAN} />
            </Grid>
            <CustomSelect
              placeholder={AvailabilityConstants.SELECT_CLINICIAN_NAME}
              name="selectedProvider"
              backgroundColor="#FFFFFF"
              onChange={(event: SelectChangeEvent<string>) => {
                setSelectedProvider(event.target.value);
                setPage(0);
              }}
              value={selectedProvider}
              items={displaySupervisingClinicians}
            />
          </Grid>
          <Grid width={"9vw"}>
            <Grid mb={1}>
              <CustomLabel label={PatientTableLabels.INSURANCE} />
            </Grid>
            <CustomSelect
              placeholder={PatientTableLabels.NAME}
              value={selectedPatientInsurance}
              items={patientInsuranceOption}
              backgroundColor="#FFFFFF"
              onChange={(e: SelectChangeEvent<string>) => {
                setSelectedPatientInsurance(e.target.value);
                setPage(0);
              }}
            />
          </Grid>

          <Grid
            flexDirection="row"
            display="flex"
            gap={1}
            bgcolor={"#FFFFFF"}
            p={0.9}
            pl={2.5}
            pr={2.5}
            borderRadius={1}
            border={"1px solid #E0E0E0"}
            alignItems="center"
            justifyContent="center"
            // onClick={handleImportClick}
            sx={{ cursor: "pointer" }}
            mt={2.5}
          >
            <Grid>
              <FileUploadOutlinedIcon />
            </Grid>
            <Grid>
              <Typography variant="bodyMedium4">
                {PatientFormButtons.IMPORT_CLIENTS}
              </Typography>
            </Grid>
          </Grid>
          <Grid mt={2.5}>
            <CustomButton
              variant="filled"
              label={SettingsFormConstants.ADD_NEW_PATIENT}
              startIcon={<AddIcon />}
              changePadding={true}
              isSubmitButtonTwo
              onClick={handleAddPatient}
            />
          </Grid>
        </Grid>
      </Grid>
      <Grid>
        <CustomisedTable
          headCells={patientTableHeaders}
          tableData={tableData}
          showCPTAndICDPagination
          setHeight="75vh"
          removeRadius={false}
          setPage={handlePageChange}
          pageSize={totalPages}
          setPageDisplaySize={handlePageSizeChange}
          pageDisplaySize={pageDisplaySize}
          page={page}
          handleNavigate={handleNavigate}
        />
      </Grid>

      <Dialog
        title={PatientTemplateActions.IMPORT_CLIENTS}
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
      >
        <ImportPatientsDialog
          open={importDialogOpen}
          handleClose={() => setImportDialogOpen(false)}
        />
      </Dialog>
    </Grid>
  );
};

export default Patients;
