import {
  Box,
  Dialog,
  DialogActions,
  DialogTitle,
  Grid,
  SelectChangeEvent,
  Switch,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
  useTheme,
  IconButton,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import CustomButton from "../../../../../common-components/custom-button/custom-button";
import {
  PatientAssignedGroupsLabels,
  PatientClinicianLabels,
  PatientDemographicsLabels,
  PatientEmergencyContactLabels,
  PatientFormLabels,
  PatientGuardianLabels,
  PatientPrivacyLabels,
  PatientProfileOutletLabels,
  PatientProfileToggleRoutes,
} from "../../../../../constants/formConst";
import { tabLabel, tabSx } from "../../../../../constants/tabs-widget";

import { useDispatch, useSelector } from "react-redux";
import Chip from "../../../../../common-components/chip/chip";
import CustomSelect from "../../../../../common-components/custom-select/customSelect";
import CustomLabel from "../../../../../common-components/customLabel/customLabel";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";
import {
  capitalizeFirstLetter,
  formatISOToMMDDYYYYLocal,
  formatPhoneNumber,
} from "../../../../../common-components/utils/stringUtils";
import { ProfileTypographyVariants } from "../../../../../constants/typography-variants";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  changeTwoFactorAuthentication,
  changeTwoFactorAuthenticationReducerAction,
} from "../../../../../redux/auth/patient/allow-two-factor-authentication-patient-reducer";
import {
  changePortalAccess,
  changePortalAccessReducerAction,
} from "../../../../../redux/auth/patient/change-patient-portal-access";
import {
  changePrimaryClinician,
  changePrimaryClinicianReducerAction,
} from "../../../../../redux/auth/patient/change-primary-clinician";
import { getPatientById } from "../../../../../redux/auth/patient/get-patient-by-id";
import { getAllSupervisingClinicians } from "../../../../../redux/auth/profile/get-all-supervising-clinicians";
import {
  resendInviteClientClinician,
  resendInviteClientClinicianReducerAction,
} from "../../../../../redux/auth/profile/resend-invitation-client-clinician-reducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { AvailabilityConstants } from "../../availability/model/availabilityModel";
import { User } from "../../settings/Users/<USER>";
import CloseIcon from "@mui/icons-material/Close";

interface FieldData {
  label: string;
  value: string;
}

const PatientProfileNext = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  // const [editProfile] = useState(false);

  const { data: getPatientByIdData, status: getPatientByIdStatus } =
    useSelector((state: RootState) => state.GetPatientByIdReducer) as {
      data: any;
      status: string;
    };

  const {
    data: changeTwoFactorAuthenticationData,
    status: changeTwoFactorAuthenticationStatus,
    error: changeTwoFactorAuthenticationError,
  } = useSelector(
    (state: RootState) => state.ChangeTwoFactorAuthenticationReducer
  );

  const {
    data: changePortalAccessData,
    status: changePortalAccessStatus,
    error: changePortalAccessError,
  } = useSelector((state: RootState) => state.ChangePortalAccessReducer);

  const [leftDemographicsColumnFields, setLeftDemographicsColumnFields] =
    useState<FieldData[]>([]);
  const [rightDemographicsColumnFields, setRightDemographicsColumnFields] =
    useState<FieldData[]>([]);
  const [privacyColumnFields, setPrivacyColumnFields] = useState<FieldData[]>(
    []
  );
  const [changePrimaryClinicianDialog, setChangePrimaryClinicianDialog] =
    useState(false);
  const [guardianFirstColumnFields, setGuardianFirstColumnFields] = useState<
    FieldData[]
  >([]);
  const [guardianSecondColumnFields, setGuardianSecondColumnFields] = useState<
    FieldData[]
  >([]);
  const [guardianThirdColumnFields, setGuardianThirdColumnFields] = useState<
    FieldData[]
  >([]);

  const handleEditProfile = () => {
    navigate(`/admin/patients/edit-patient/${location.state}`, {
      state: {
        patientUuid: location.state,
        isEdit: true,
      },
    });
  };

  const [
    emergencyContactFirstColumnFields,
    setEmergencyContactFirstColumnFields,
  ] = useState<FieldData[]>([]);

  const [
    emergencyContactThirdColumnFields,
    setEmergencyContactThirdColumnFields,
  ] = useState<FieldData[]>([]);
  const [
    emergencyContactFourthColumnFields,
    setEmergencyContactFourthColumnFields,
  ] = useState<FieldData[]>([]);
  const [assignedGroupsColumnFields, setAssignedGroupsColumnFields] = useState<
    FieldData[]
  >([]);

  const tabRoutes = Object.values(PatientProfileToggleRoutes);
  const [value, setValue] = React.useState(0);
  const { data: getAllSupervisingCliniciansData } = useSelector(
    (state: RootState) => state.GetAllSupervisingCliniciansReducer
  );

  const {
    data: changePrimaryClinicianData,
    status: changePrimaryClinicianStatus,
    error: changePrimaryClinicianError,
  } = useSelector((state: RootState) => state.ChangePrimaryClinicianReducer);

  const {
    status: resendInviteClientClinicianStatus,
    data: resendInviteClientClinicianData,
  } = useSelector(
    (state: RootState) => state.ResendInviteClientClinicianReducer
  );

  const displaySupervisingClinicians = [
    ...Object.entries(getAllSupervisingCliniciansData || {}).map(
      ([uuid, value]) => ({
        value: uuid,
        label: String(value),
      })
    ),
  ];

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    if (newValue === 0 || newValue === 1 || newValue === 2) return;

    setValue(newValue);
    navigate(tabRoutes[newValue].toLowerCase());
  };

  const a11yProps = (index: number) => {
    return {
      id: `simple-tab-${index}`,
      "aria-controls": `simple-tabpanel-${index}`,
    };
  };

  const handleChangePortalAccess = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(
      changePortalAccess({
        clientUuid: location.state,
        portalAccess: event.target.checked,
      })
    );
  };

  const handleTwoFactorAuthenticationChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(
      changeTwoFactorAuthentication({
        clientUuid: location.state,
        flag: event.target.checked,
      })
    );
  };

  useEffect(() => {
    if (!getPatientByIdData) return;

    setLeftDemographicsColumnFields([
      {
        label: PatientDemographicsLabels.LANGUAGES,
        value:
          capitalizeFirstLetter(getPatientByIdData.preferredLanguage) || "-",
      },
      {
        label: PatientDemographicsLabels.RACE,
        value: capitalizeFirstLetter(getPatientByIdData.race) || "-",
      },
      {
        label: PatientDemographicsLabels.ETHNICITY,
        value: capitalizeFirstLetter(getPatientByIdData.ethnicity) || "-",
      },
    ]);
    setRightDemographicsColumnFields([
      {
        label: PatientDemographicsLabels.CREATED_ON,
        value:
          formatISOToMMDDYYYYLocal(getPatientByIdData.accountCreatedOn) || "-",
      },
      {
        label: PatientDemographicsLabels.CREATED_BY,
        value: getPatientByIdData?.accountCreator || "-",
      },
      {
        label: PatientDemographicsLabels.LEGAL_SEX,
        value: capitalizeFirstLetter(getPatientByIdData.legalSex) || "-",
      },
    ]);

    setPrivacyColumnFields([
      {
        label: PatientPrivacyLabels.PHONE_APPOINTMENT_REMINDERS,
        value: getPatientByIdData.phoneAppointmentReminder ? "Yes" : "No",
      },
      {
        label: PatientPrivacyLabels.EMAIL_APPOINTMENT_REMINDERS,
        value: getPatientByIdData.emailAppointmentRemainder ? "Yes" : "No",
      },
    ]);

    setGuardianFirstColumnFields([
      {
        label: PatientGuardianLabels.NAME,
        value: getPatientByIdData.guardianContact?.name || "-",
      },
    ]);

    setGuardianSecondColumnFields([
      {
        label: PatientGuardianLabels.RELATIONSHIP_WITH_PATIENT,
        value:
          capitalizeFirstLetter(
            getPatientByIdData.guardianContact?.relationship
          ) || "-",
      },
    ]);

    setGuardianThirdColumnFields([
      {
        label: PatientGuardianLabels.PHONE_NO,
        value: getPatientByIdData.guardianContact?.phoneNumber
          ? `${formatPhoneNumber(getPatientByIdData.guardianContact?.phoneNumber)}`
          : "-",
      },
    ]);

    setEmergencyContactFirstColumnFields([
      {
        label: PatientEmergencyContactLabels.NAME,
        value: getPatientByIdData.emergencyContact?.name || "-",
      },
    ]);

    setEmergencyContactThirdColumnFields([
      {
        label: PatientEmergencyContactLabels.RELATIONSHIP_WITH_PATIENT,
        value:
          capitalizeFirstLetter(
            getPatientByIdData.emergencyContact?.relationship
          ) || "-",
      },
    ]);

    setEmergencyContactFourthColumnFields([
      {
        label: PatientEmergencyContactLabels.PHONE_NO,
        value: getPatientByIdData.emergencyContact?.phoneNumber
          ? `${formatPhoneNumber(getPatientByIdData.emergencyContact?.phoneNumber)}`
          : "-",
      },
    ]);

    setAssignedGroupsColumnFields([
      {
        label: PatientAssignedGroupsLabels.GROUP_NAME,
        value: "-",
      },
    ]);
  }, [getPatientByIdData]);

  const handleTransferToClinician = () => {
    setChangePrimaryClinicianDialog(true);
  };

  const handleChangePrimaryClinician = () => {
    dispatch(
      changePrimaryClinician({
        clientUuid: location.state,
        existingClinicianId: getPatientByIdData?.primaryClinicianId,
        newClinicianId: selectedProvider,
      })
    ).then((response) => {
      if (response.meta.requestStatus === "fulfilled") {
        setChangePrimaryClinicianDialog(false);
      }
    });
  };

  const handleInvite = (rowData: User) => {
    if (rowData?.uuid) {
      dispatch(
        resendInviteClientClinician({
          uuid: rowData.uuid,
          isClient: true,
        })
      );
    }
  };

  useEffect(() => {
    switch (getPatientByIdStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        break;
    }
  }, [getPatientByIdStatus, dispatch]);

  useEffect(() => {
    if (!changePrimaryClinicianStatus) return;
    switch (changePrimaryClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              changePrimaryClinicianData || "Clinician changed successfully",
          })
        );
        dispatch(
          changePrimaryClinicianReducerAction.resetChangePrimaryClinicianReducer()
        );
        dispatch(getPatientById(location.state as string));
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: changePrimaryClinicianError || "",
          })
        );
        dispatch(
          changePrimaryClinicianReducerAction.resetChangePrimaryClinicianReducer()
        );
        break;
    }
  }, [
    changePrimaryClinicianStatus,
    changePrimaryClinicianError,
    changePrimaryClinicianData,
  ]);

  useEffect(() => {
    switch (resendInviteClientClinicianStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: "success",
            message:
              resendInviteClientClinicianData || "Invite sent successfully",
          })
        );
        dispatch(
          resendInviteClientClinicianReducerAction.resetResendInviteClientClinicianReducer()
        );
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          resendInviteClientClinicianReducerAction.resetResendInviteClientClinicianReducer()
        );
        break;
    }
  }, [resendInviteClientClinicianStatus, resendInviteClientClinicianData]);

  useEffect(() => {
    if (!changePortalAccessStatus) return;
    switch (changePortalAccessStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              changePortalAccessData || "Portal Access updated successfully",
          })
        );
        dispatch(
          changePortalAccessReducerAction.resetChangePortalAccessReducer()
        );
        dispatch(getPatientById(location.state as string));

        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              changePortalAccessError || "Failed to update Portal Access",
          })
        );
        dispatch(
          changePortalAccessReducerAction.resetChangePortalAccessReducer()
        );
        break;
    }
  }, [
    changePortalAccessStatus,
    changePortalAccessData,
    changePortalAccessError,
  ]);

  useEffect(() => {
    if (!changeTwoFactorAuthenticationStatus) return;
    switch (changeTwoFactorAuthenticationStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message:
              changeTwoFactorAuthenticationData ||
              "Two-Factor Authentication updated successfully",
          })
        );
        dispatch(
          changeTwoFactorAuthenticationReducerAction.resetChangeTwoFactorAuthenticationReducer()
        );
        dispatch(getPatientById(location.state as string));
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message:
              changeTwoFactorAuthenticationError ||
              "Failed to update Two-Factor Authentication",
          })
        );
        dispatch(
          changeTwoFactorAuthenticationReducerAction.resetChangeTwoFactorAuthenticationReducer()
        );
        break;
    }
  }, [
    changeTwoFactorAuthenticationStatus,
    changeTwoFactorAuthenticationError,
    changeTwoFactorAuthenticationData,
  ]);

  useEffect(() => {
    dispatch(getAllSupervisingClinicians());
  }, [dispatch]);

  useEffect(() => {
    if (location?.state) {
      dispatch(getPatientById(location.state as string));
    }
  }, [dispatch, location?.state]);

  return (
    <Box>
      {/* Header Section */}
      <Box sx={{ p: 1.5, borderBottom: "1px solid #E0E0E0" }}>
        <Box
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          justifyContent="space-between"
          width="100%"
          alignItems={{ xs: "flex-start", sm: "center" }}
          gap={2}
        >
          <Box sx={{ ...tabSx, ml: 0.5, width: { xs: "100%", md: "auto" } }}>
            <Tabs
              value={value}
              onChange={handleChange}
              variant={isMobile ? "scrollable" : "standard"}
              scrollButtons="auto"
            >
              {tabRoutes.map((item, index) => (
                <Tab
                  key={index}
                  label={item}
                  {...a11yProps(index)}
                  sx={tabLabel}
                />
              ))}
            </Tabs>
          </Box>

          <Grid
            container
            spacing={2}
            justifyContent={{ xs: "flex-start", sm: "flex-end" }}
            sx={{ width: { xs: "100%", sm: "auto" } }}
          >
            <Grid item>
              <CustomButton variant="smallButton" label="Reset Password" />
            </Grid>
            <Grid item>
              <CustomButton
                variant="smallButton"
                label="Edit Profile"
                onClick={handleEditProfile}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          p: 2,
          maxHeight: "calc(100vh - 200px)",
          overflowY: "auto",
          paddingBottom: 3,
        }}
      >
        <Grid container spacing={1.5}>
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                border: "1px solid #E7E7E7",
                borderRadius: 2,
                p: 1,
                height: "100%",
              }}
            >
              <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                {PatientProfileOutletLabels.DEMOGRAPHICS}
              </Typography>
              <Grid container spacing={1} mt={0.5} gap={2}>
                <Grid item xs={12} lg={6}>
                  {rightDemographicsColumnFields.map((field, index) => (
                    <Grid container key={index} sx={{ mb: 1.5 }}>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
                <Grid item xs={12} lg={4}>
                  {leftDemographicsColumnFields.map((field, index) => (
                    <Grid container key={index} sx={{ mb: 1.5 }}>
                      <Grid item xs={5}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={7}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                border: "1px solid #E7E7E7",
                borderRadius: 2,
                p: 1,
                height: "100%",
              }}
            >
              <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                {PatientProfileOutletLabels.PRIVACY}
              </Typography>
              <Box mt={1.5}>
                {privacyColumnFields.map((field, index) => (
                  <Grid container key={index} sx={{ mb: 1.5 }}>
                    <Grid item xs={9} sm={5}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                        }
                      >
                        {field.label}
                      </Typography>
                    </Grid>
                    <Grid item xs={3} sm={4}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                        }
                      >
                        : {field.value}
                      </Typography>
                    </Grid>
                  </Grid>
                ))}
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ border: "1px solid #E7E7E7", borderRadius: 2, p: 1 }}>
              <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                {PatientProfileOutletLabels.GUARDIAN_INFORMATION}
              </Typography>
              <Grid container spacing={2} mt={0.5}>
                <Grid item xs={12} md={4.5}>
                  {guardianFirstColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={3}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={9}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
                <Grid item xs={12} md={3}>
                  {guardianSecondColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
                <Grid item xs={12} md={4.5}>
                  {guardianThirdColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={4}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={8}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Box>
          </Grid>

          {/* Emergency Contact Details Section */}
          <Grid item xs={12} md={6}>
            <Box sx={{ border: "1px solid #E7E7E7", borderRadius: 2, p: 1 }}>
              <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                {PatientProfileOutletLabels.EMERGENCY_CONTACT_DETAILS}
              </Typography>
              <Grid container spacing={2} mt={0.5}>
                <Grid item xs={12} md={4.5}>
                  {emergencyContactFirstColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={4}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={8}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>

                <Grid item xs={12} md={3}>
                  {emergencyContactThirdColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
                <Grid item xs={12} md={4.5}>
                  {emergencyContactFourthColumnFields.map((field, index) => (
                    <Grid container key={index}>
                      <Grid item xs={4}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                          }
                        >
                          {field.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={8}>
                        <Typography
                          variant={
                            ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                          }
                        >
                          : {field.value}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                border: "1px solid #E7E7E7",
                borderRadius: 2,
                p: 1,
                height: "100%",
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                  {PatientProfileOutletLabels.CLINICIANS}
                </Typography>
                <CustomButton
                  variant="smallButton"
                  label="Transfer to Clinician"
                  onClick={handleTransferToClinician}
                />
              </Box>
              <Box mt={1.5}>
                <Grid container sx={{ mb: 1.5 }} alignItems="center">
                  <Grid item xs={3}>
                    <Typography
                      variant={
                        ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                      }
                    >
                      {PatientClinicianLabels.PRIMARY_CLINICIAN}
                    </Typography>
                  </Grid>
                  <Grid item xs={9} display="flex" flexDirection="row">
                    <Box display="flex" alignItems="center" width="9vw">
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                        }
                        sx={{ mr: 1 }}
                      >
                        : {getPatientByIdData?.clinicianName || "-"}
                      </Typography>
                    </Box>
                    <Box ml={6}>
                      <Chip type={"ACTIVE"} />
                    </Box>
                  </Grid>
                </Grid>

                <Grid display="flex" flexDirection="row">
                  <Grid item xs={3}>
                    <Typography
                      variant={
                        ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                      }
                    >
                      {PatientClinicianLabels.OTHER_CLINICIANS}
                    </Typography>
                  </Grid>
                  :
                  <Box sx={{ width: "3px", display: "inline-block" }} />
                  <Box>
                    <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                      {getPatientByIdData?.otherClinicians &&
                      getPatientByIdData.otherClinicians.length > 0 &&
                      getPatientByIdData.otherClinicians[0] !== null
                        ? getPatientByIdData.otherClinicians.map(
                            (clinician: string, index: number) => (
                              <Grid>
                                <Grid container key={index} sx={{ mb: 1.5 }}>
                                  <Box
                                    display="flex"
                                    alignItems="center"
                                    width="10vw"
                                  >
                                    <Typography
                                      variant={
                                        ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                                      }
                                      sx={{ mr: 1 }}
                                    >
                                      {clinician || "-"}
                                    </Typography>
                                  </Box>
                                  <Chip type={"DISCHARGED"} />
                                </Grid>
                              </Grid>
                            )
                          )
                        : "-"}
                    </Typography>
                  </Box>
                </Grid>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                border: "1px solid #E7E7E7",
                borderRadius: 2,
                p: 1,
                height: "100%",
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="bodyMedium3" sx={{ color: "#373D41" }}>
                  {PatientProfileOutletLabels.ASSIGN_GROUPS}
                </Typography>
                <CustomButton
                  variant="smallButton"
                  label="Edit"
                  // onClick={handleTransferToClinician}
                />
              </Box>
              <Box mt={1.5}>
                {assignedGroupsColumnFields.map((field, index) => (
                  <Grid container key={index} sx={{ mb: 1.5 }}>
                    <Grid item xs={3}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY
                        }
                      >
                        {field.label}
                      </Typography>
                    </Grid>
                    <Grid item xs={7}>
                      <Typography
                        variant={
                          ProfileTypographyVariants.TITLE_SMALL_PROFILE_GREY_LIGHT
                        }
                      >
                        : {field.value}
                      </Typography>
                    </Grid>
                  </Grid>
                ))}
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} sx={{ mb: 3 }}>
            <Box sx={{ border: "1px solid #E7E7E7", borderRadius: 2, p: 1 }}>
              <Grid container spacing={1}>
                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={8}>
                    <Grid item xs={12} sm="auto">
                      <Typography
                        variant="bodyMedium3"
                        sx={{ color: "#373D41" }}
                      >
                        {PatientDemographicsLabels.PATIENT_PORTAL_ACCESS}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm="auto">
                      <Switch
                        checked={getPatientByIdData?.portalAccess}
                        onChange={handleChangePortalAccess}
                      />
                    </Grid>
                    {!getPatientByIdData?.invited &&
                      getPatientByIdData?.portalAccess && (
                        <Grid item xs={12} sm="auto">
                          <CustomButton
                            variant="outline"
                            label="Invite Client"
                            changePadding={false}
                            isSubmitButton
                            onClick={() => handleInvite(getPatientByIdData)}
                          />
                        </Grid>
                      )}
                  </Grid>
                </Grid>
                <Grid item xs={12}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} sm="auto">
                      <Typography
                        variant="bodyMedium3"
                        sx={{ color: "#373D41" }}
                      >
                        {PatientDemographicsLabels.TWO_FACTOR_AUTHENTICATION}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm="auto">
                      <Switch
                        checked={!!getPatientByIdData?.twoFactorAuthentication}
                        onChange={handleTwoFactorAuthenticationChange}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Dialog
        open={changePrimaryClinicianDialog}
        PaperProps={{ sx: { p: 1, width: "500px" } }}
      >
        <DialogTitle>
          {PatientDemographicsLabels.CHANGE_PRIMARY_CLINICIAN}
          <IconButton
            aria-label="close"
            onClick={() => setChangePrimaryClinicianDialog(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <Grid ml={3} mt={1}>
          <CustomLabel label={PatientFormLabels.CLINICIAN_NAME} />
        </Grid>
        <Grid width={"90%"} alignSelf={"center"} mb={2}>
          <CustomSelect
            placeholder={AvailabilityConstants.SELECT_CLINICIAN_NAME}
            name="selectedProvider"
            backgroundColor="#FFFFFF"
            onChange={(event: SelectChangeEvent<string>) => {
              setSelectedProvider(event.target.value);
            }}
            value={selectedProvider}
            items={displaySupervisingClinicians}
          />
        </Grid>
        <Grid mt={2} mr={2}>
          <DialogActions>
            <Grid
              display={"flex"}
              justifyContent={"space-between"}
              flexDirection={"row"}
              gap={3}
            >
              <Grid>
                <CustomButton
                  variant="outline"
                  label="Cancel"
                  isSubmitButton
                  onClick={() => setChangePrimaryClinicianDialog(false)}
                />
              </Grid>
              <Grid>
                <CustomButton
                  variant="filled"
                  label="Save"
                  changePadding={false}
                  isSubmitButton
                  onClick={handleChangePrimaryClinician}
                />
              </Grid>
            </Grid>
          </DialogActions>
        </Grid>
      </Dialog>
    </Box>
  );
};

export default PatientProfileNext;
