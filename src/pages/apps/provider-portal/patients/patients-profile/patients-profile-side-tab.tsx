import AccountBalanceOutlinedIcon from "@mui/icons-material/AccountBalanceOutlined";
import AttachMoneyOutlinedIcon from "@mui/icons-material/AttachMoneyOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import EventNoteIcon from "@mui/icons-material/EventNote";
import PersonOutlineOutlinedIcon from "@mui/icons-material/PersonOutlineOutlined";
import WatchLaterOutlinedIcon from "@mui/icons-material/WatchLaterOutlined";
import { Box, Grid, List, ListItem, Typography } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";

export const patientProfileSideBarMenu = [
  {
    id: "Appointments",
    name: "Appointments",
    // label: "appointments",
    icon: <WatchLaterOutlinedIcon />,
    route: "appointments",
  },
  {
    id: "Notes/Records",
    name: "Notes/Records",
    // label: "notes",
    icon: <EventNoteIcon />,
    route: "notes-records",
  },
  {
    id: "Insurance",
    name: "Insurance",
    // label: "insurance",
    icon: <AccountBalanceOutlinedIcon />,
    route: "insurance",
  },
  {
    id: "Billing",
    name: "Billing",
    // label: "billing",
    icon: <AttachMoneyOutlinedIcon />,
    route: "billing",
  },
  {
    id: "Documents",
    name: "Documents",
    // label: "documents",
    icon: <DescriptionOutlinedIcon />,
    route: "documents",
  },
  {
    id: "Profile",
    name: "Profile",
    label: "",
    icon: <PersonOutlineOutlinedIcon />,
    route: "patient-profile",
  },
];

function PatientProfileSidebar({
  onTabChange,
  selectedTab,
}: {
  onTabChange?: (tabId: string) => void;
  selectedTab?: string;
}) {
  const navigate = useNavigate();
  const location = useLocation();

  const [openSideListComponent, setOpenSideListComponent] = useState(
    patientProfileSideBarMenu[5].id
  );

  const navigateSidebar = (route: string, id: string) => {
    setOpenSideListComponent(id);
    if (onTabChange) {
      onTabChange(id);
    }
    const basePath = location.pathname.split("/").slice(0, -1).join("/");
    console.log(route);
    navigate(`${basePath}/${route}`);
  };

  return (
    <Box
      sx={{
        width: "10vw",
        backgroundColor: "#FFFFFF",
        border: "1px solid #E7E7E7",
      }}
    >
      <Grid sx={{ flex: 1 }}>
        <List>
          {patientProfileSideBarMenu.map((tab) => {
            const isSelected = openSideListComponent === tab.id;
            return (
              <ListItem
                sx={{
                  display: "flex",
                  alignItems: "center",
                  padding: "16px",
                  cursor: "pointer",
                  backgroundColor: isSelected ? "#F0F8FF" : "transparent",
                  borderLeft: isSelected
                    ? "3px solid #007FFF"
                    : "3px solid transparent",
                  "&:hover": {
                    backgroundColor: "#F0F8FF",
                  },
                }}
                key={tab.id}
                onClick={() => navigateSidebar(tab.route, tab.id)}
              >
                <Typography
                  variant="subtitleSemiBold"
                  noWrap
                  title={tab.name}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-start",
                  }}
                >
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      color: isSelected ? "#007FFF" : "#595F63",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {tab.icon}
                  </Box>
                  <Typography
                    variant="titleSmallProfileGrey"
                    component="span"
                    sx={{
                      ml: 1,
                      fontWeight: isSelected ? 600 : 400,
                      color: isSelected ? "#007FFF" : "inherit",
                    }}
                  >
                    {tab.name}
                  </Typography>
                </Typography>
              </ListItem>
            );
          })}
        </List>
      </Grid>
    </Box>
  );
}

export default PatientProfileSidebar;
