import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import EditIcon from "@mui/icons-material/Edit";
import {
  Backdrop,
  Box,
  Fade,
  Grid2,
  IconButton,
  Modal,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AlertSeverity } from "../../../../../common-components/snackbar-alert/snackbar-alert";
import { apiStatus } from "../../../../../models/apiStatus";
import { loaderAction } from "../../../../../redux/auth/loaderReducer";
import {
  addStickyNotes,
  addStickyNotesReducerAction,
} from "../../../../../redux/auth/patient/add-new-sticky-note-reducer";
import { getStickyNotes } from "../../../../../redux/auth/patient/get-sticky-notes-reducer";
import { snackbarAction } from "../../../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../../../redux/store";

interface StickyNotesProps {
  uuid: string;
  isAlertNote?: boolean;
}
const StickyNotes = ({ uuid, isAlertNote }: StickyNotesProps) => {
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [opens, setOpens] = useState(false);
  const handleOpens = () => {
    setStickyNotes(stickyNotesData?.note || "");
    setAlertNote(stickyNotesData?.alertNote || "");
    setOpens(true);
  };

  const style = {
    position: "absolute" as "absolute",
    top: "44%",
    left: "85%",
    transform: "translate(-50%, -50%)",
    width: 435,
    height: 250,
    bgcolor: isAlertNote ? "#FFF2F3" : "#FFFBF2",
    border: isAlertNote ? "1.5px solid #FFB6BC" : "1.5px solid #FFAA00",
    boxShadow: "0px 0px 8px #00000029",
    borderRadius: "5px",
    p: 1,
    Opacity: 1,
  };

  const [stickyNotes, setStickyNotes] = useState("");
  const [alertNote, setAlertNote] = useState("");

  const dispatch = useDispatch<AppDispatch>();
  const { data: stickyNotesData } = useSelector(
    (state: RootState) => state.getStickyNotesReducer
  );

  const {
    data: addStickyNotesData,
    status: addStickyNotesStatus,
    error: addStickyNotesError,
  } = useSelector((state: RootState) => state.addStickyNotesReducer);

  const handleCloses = () => {
    setOpens(false);
    handleSaveNote();
  };

  const handleSaveNote = () => {
    if (isAlertNote) {
      dispatch(
        addStickyNotes({
          uuid: "",
          clientId: uuid,
          alertNote: alertNote,
        })
      );
    } else {
      dispatch(
        addStickyNotes({
          uuid: "",
          note: stickyNotes,
          clientId: uuid,
        })
      );
    }
  };

  useEffect(() => {
    if (uuid) {
      dispatch(getStickyNotes(uuid));
      dispatch(addStickyNotesReducerAction.resetAddStickyNotesReducer());
    }
  }, [uuid, dispatch]);

  useEffect(() => {
    switch (addStickyNotesStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.SUCCESS,
            message: addStickyNotesData || "",
          })
        );
        if (uuid) {
          dispatch(getStickyNotes(uuid));
        }
        setStickyNotes("");
        setAlertNote("");
        setOpens(false);
        dispatch(addStickyNotesReducerAction.resetAddStickyNotesReducer());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            isSnackbarOpen: true,
            severity: AlertSeverity.ERROR,
            message: addStickyNotesError || "",
          })
        );
        dispatch(addStickyNotesReducerAction.resetAddStickyNotesReducer());
        break;
    }
  }, [
    addStickyNotesStatus,
    dispatch,
    uuid,
    addStickyNotesData,
    addStickyNotesError,
  ]);

  return (
    <Grid2 display={"flex"} justifyContent={"flex-end"} gap={2} height={"8vh"}>
      <Grid2>
        <Grid2
          p={0.5}
          sx={{
            border: "1px solid #1B598426",
            borderRadius: "5px",
            background: isAlertNote ? "#FFF2F3" : "#FFFBF2",
            display: "flex",
            flexDirection: "column",
            opacity: 1,
          }}
        >
          <Grid2>
            <Grid2>
              <Grid2 display={"flex"} justifyContent={"space-between"}>
                <Grid2>
                  <Typography variant="bodyMedium4">
                    {isAlertNote ? "Alert Note" : "Note"}
                  </Typography>
                </Grid2>
                <Grid2>
                  <Typography>
                    <IconButton onClick={handleOpens}>
                      <EditIcon
                        fontSize="small"
                        sx={{ marginTop: "-6.5px", color: "black" }}
                      ></EditIcon>
                    </IconButton>
                  </Typography>
                </Grid2>
                <Modal
                  aria-labelledby="transition-modal-title"
                  aria-describedby="transition-modal-description"
                  open={opens}
                  onClose={(reason) => {
                    if (reason !== "backdropClick") {
                      handleCloses();
                    }
                  }}
                  closeAfterTransition
                  slots={{ backdrop: Backdrop }}
                  slotProps={{
                    backdrop: {
                      timeout: 500,
                    },
                  }}
                >
                  <Fade in={opens}>
                    <Box sx={style}>
                      <Grid2
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Typography
                          id="transition-modal-title"
                          variant="bodyMedium4"
                        >
                          {isAlertNote ? "ALERT NOTE" : "NOTE"}
                        </Typography>
                        <Typography>
                          <IconButton onClick={handleCloses}>
                            <CloseOutlinedIcon />
                          </IconButton>
                        </Typography>
                      </Grid2>
                      <TextField
                        id="note-textarea"
                        // label="Add your note"
                        multiline
                        rows={6}
                        variant="outlined"
                        fullWidth
                        placeholder="Add your note here..."
                        sx={{
                          mt: 2,
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              border: "none",
                            },
                            "&:hover fieldset": {
                              border: "none",
                            },
                            "&.Mui-focused fieldset": {
                              border: "none",
                            },
                          },
                        }}
                        value={isAlertNote ? alertNote : stickyNotes}
                        onChange={(e) =>
                          isAlertNote
                            ? setAlertNote(e.target.value)
                            : setStickyNotes(e.target.value)
                        }
                      />
                    </Box>
                  </Fade>
                </Modal>
              </Grid2>
            </Grid2>
          </Grid2>
          <Grid2 width={"10vw"} height={"6vh"} container>
            <Typography variant="bodyRegular5" color={"black"}>
              {stickyNotesData?.note &&
                !isAlertNote &&
                (() => {
                  const originalText = stickyNotesData?.note;
                  const alphabetOnly = originalText.replace(/[^A-Za-z]/g, "");

                  if (alphabetOnly.length > 20) {
                    let count = 0;
                    let truncatedText = "";

                    for (let char of originalText) {
                      if (/[A-Za-z]/.test(char)) count++;
                      truncatedText += char;
                      if (count === 20) break;
                    }

                    return (
                      <>
                        {truncatedText}...
                        <span
                          style={{
                            color: "#1B5984",
                            fontWeight: "bold",
                            cursor: "pointer",
                            marginLeft: 4,
                          }}
                          onClick={handleOpen}
                        >
                          More
                        </span>
                      </>
                    );
                  } else {
                    return (
                      <>
                        {originalText}
                        <span
                          style={{
                            color: "#1B5984",
                            fontWeight: "bold",
                            cursor: "pointer",
                            marginLeft: 4,
                          }}
                          onClick={handleOpen}
                        >
                          View
                        </span>
                      </>
                    );
                  }
                })()}
              {stickyNotesData?.alertNote &&
                isAlertNote &&
                (() => {
                  const originalText = stickyNotesData?.alertNote;
                  const alphabetOnly = originalText.replace(/[^A-Za-z]/g, "");

                  if (alphabetOnly.length > 20) {
                    let count = 0;
                    let truncatedText = "";

                    for (let char of originalText) {
                      if (/[A-Za-z]/.test(char)) count++;
                      truncatedText += char;
                      if (count === 20) break;
                    }

                    return (
                      <>
                        {truncatedText}...
                        <span
                          style={{
                            color: "#1B5984",
                            fontWeight: "bold",
                            cursor: "pointer",
                            marginLeft: 4,
                          }}
                          onClick={handleOpen}
                        >
                          More
                        </span>
                      </>
                    );
                  } else {
                    return (
                      <>
                        {originalText}
                        <span
                          style={{
                            color: "#1B5984",
                            fontWeight: "bold",
                            cursor: "pointer",
                            marginLeft: 4,
                          }}
                          onClick={handleOpen}
                        >
                          View
                        </span>
                      </>
                    );
                  }
                })()}
            </Typography>
          </Grid2>

          <Modal
            aria-labelledby="transition-modal-title"
            aria-describedby="transition-modal-description"
            open={open}
            onClose={(reason) => {
              if (reason !== "backdropClick") {
                handleClose();
              }
            }}
            closeAfterTransition
            slots={{ backdrop: Backdrop }}
            slotProps={{
              backdrop: {
                timeout: 500,
              },
            }}
          >
            <Fade in={open}>
              <Box sx={style}>
                <Grid2
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography id="transition-modal-title" variant="bodyMedium3">
                    {isAlertNote ? "ALERT NOTE" : "NOTE"}
                  </Typography>
                  <Typography>
                    <IconButton onClick={handleClose}>
                      <CloseOutlinedIcon />
                    </IconButton>
                  </Typography>
                </Grid2>

                <Box
                  sx={{
                    mt: 2,
                    maxHeight: "150px",
                    overflowY: "auto",
                  }}
                >
                  <Typography
                    id="modal-modal-description"
                    variant="bodyMedium3"
                    sx={{
                      wordBreak: "break-word",
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    {isAlertNote && stickyNotesData?.alertNote}
                    {!isAlertNote && stickyNotesData?.note}
                  </Typography>
                </Box>
              </Box>
            </Fade>
          </Modal>
        </Grid2>
      </Grid2>
    </Grid2>
  );
};

export default StickyNotes;
