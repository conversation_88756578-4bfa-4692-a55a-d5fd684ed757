import { Box, Typography, Paper } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { useState } from "react";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import Chip from "../../../../../../common-components/chip/chip";
import ActionButton from "../../../../../../common-components/action-button/action-button";
import CustomisedTable from "../../../../../../common-components/table/table";

const DOCUMENT_TYPE_OPTIONS = [
  { value: "all", label: "All Types" },
  { value: "online", label: "Online" },
  { value: "pdf", label: "PDF" },
  { value: "attachments", label: "Message Attachments" },
];

const DOCUMENTS = [
  {
    name: "Good Faith Estimate",
    type: "Online",
    uploaded: "8/16/13",
    description: "Please fill as early as possible",
    status: "COMPLETED",
    action: [
      { label: "View", route: "view" },
      { label: "Download", route: "download" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "Patient Health Questionnaire",
    type: "PDF",
    uploaded: "8/2/19",
    description: "Please fill as early as possible",
    status: "INCOMPLETE",
    action: [
      { label: "View", route: "view" },
      { label: "Download", route: "download" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "Patient Health Questionnaire",
    type: "Message Attachments",
    uploaded: "8/2/19",
    description: "Please fill as early as possible",
    status: "COMPLETED",
    action: [
      { label: "View", route: "view" },
      { label: "Download", route: "download" },
      { label: "Delete", route: "delete" },
    ],
  },
];

const headCells = [
  { id: "name", label: "Document Name" },
  { id: "type", label: "Type" },
  { id: "uploaded", label: "Uploaded Date" },
  { id: "description", label: "Description" },
  { id: "status", label: "Status", type: "chip" },
  { id: "action", label: "Action", type: "action" },
];

const statusMap = {
  COMPLETED: "COMPLETED",
  INCOMPLETE: "IN_ACTIVE",
};

const mappedDocuments = DOCUMENTS.map((doc) => ({
  ...doc,
  status: statusMap[doc.status as keyof typeof statusMap],
}));

const PatientDocuments = () => {
  const [docType, setDocType] = useState("");

  return (
    <Box sx={{ p: 0, background: "#fff", borderRadius: 2 }}>
      {/* Header Section */}
      <Box
        sx={{
          borderBottom: "1px solid #E7E7E7",
          px: 3,
          py: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "#21262B",
          }}
        >
          Documents
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <CustomSelect
            placeholder="Document Type "
            value={docType}
            items={DOCUMENT_TYPE_OPTIONS}
            onChange={(e) => setDocType(e.target.value)}
            bgWhite
            crossable
          />
          <Box sx={{ minWidth: 185 }}>
            <CustomButton
              label="Assign Form"
              variant="filled"
              startIcon={<AddIcon sx={{ fontSize: 18 }} />}
            />
          </Box>
          <Box sx={{ minWidth: 220 }}>
            <CustomButton
              label="Upload Document"
              variant="filled"
              startIcon={<AddIcon sx={{ fontSize: 18 }} />}
            />
          </Box>
        </Box>
      </Box>

      {/* Table Section */}
      <Box sx={{ p: 2 }}>
        <Paper
          elevation={0}
          sx={{
            borderRadius: 2,
            border: "1px solid #E7E7E7",
            overflow: "hidden",
            background: "#fff",
          }}
        >
          <CustomisedTable
            headCells={headCells}
            tableData={mappedDocuments}
            removeRadius
            setHeight="auto"
          />
        </Paper>
      </Box>
    </Box>
  );
};

export default PatientDocuments;
