import { Box, Typography, Paper, Stack } from "@mui/material";
import { useLocation } from "react-router-dom";

const PatientDocuments = () => {
  const location = useLocation();
  const patientUuid = location.state;

  return (
    <Box sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Typography variant="h6">Patient Documents</Typography>

        <Paper sx={{ p: 3, textAlign: "center" }}>
          <Typography variant="body1" color="text.secondary">
            Document management component for patient: {patientUuid}
          </Typography>
          <Typography variant="body2" sx={{ mt: 2 }}>
            This is a test component. You can add your document functionality
            here.
          </Typography>
        </Paper>

        {/* Add your document-related components here */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            Document Features to Implement:
          </Typography>
          <ul>
            <li>Document upload functionality</li>
            <li>Document list/grid view</li>
            <li>Document preview</li>
            <li>Document categorization</li>
            <li>Search and filter documents</li>
          </ul>
        </Paper>
      </Stack>
    </Box>
  );
};

export default PatientDocuments;
