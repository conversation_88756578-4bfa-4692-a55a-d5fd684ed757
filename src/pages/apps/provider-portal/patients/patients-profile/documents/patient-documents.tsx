import { Box, Typography, Paper, Stack, Grid } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useState } from "react";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import Chip from "../../../../../../common-components/chip/chip";
import ActionButton from "../../../../../../common-components/action-button/action-button";

const DOCUMENT_TYPE_OPTIONS = [
  { value: "all", label: "All Types" },
  { value: "online", label: "Online" },
  { value: "pdf", label: "PDF" },
  { value: "attachments", label: "Message Attachments" },
];

const DOCUMENTS = [
  {
    name: "Good Faith Estimate",
    type: "Online",
    uploaded: "8/16/13",
    description: "Please fill as early as possible",
    status: "COMPLETED",
  },
  {
    name: "Patient Health Questionnaire",
    type: "PDF",
    uploaded: "8/2/19",
    description: "Please fill as early as possible",
    status: "INCOMPLETE",
  },
  {
    name: "Patient Health Questionnaire",
    type: "Message Attachments",
    uploaded: "8/2/19",
    description: "Please fill as early as possible",
    status: "COMPLETED",
  },
];

const statusMap = {
  COMPLETED: "COMPLETED",
  INCOMPLETE: "IN_ACTIVE", // Use custom chip status for 'Incomplete'
};

const PatientDocuments = () => {
  const [docType, setDocType] = useState("");

  return (
    <Box sx={{ p: 0, background: "#fff", borderRadius: 2 }}>
      {/* Header Section */}
      <Box
        sx={{
          borderBottom: "1px solid #E7E7E7",
          px: 3,
          py: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "#21262B",
          }}
        >
          Documents
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <CustomSelect
            placeholder="Document Type"
            value={docType}
            items={DOCUMENT_TYPE_OPTIONS}
            onChange={(e) => setDocType(e.target.value)}
            bgWhite
            crossable
          />
          <CustomButton
            label="Assign Form"
            variant="filled"
            startIcon={<AddIcon sx={{ fontSize: 18 }} />}
            sx={{ minWidth: 150 }}
          />
          <CustomButton
            label="Upload Document"
            variant="filled"
            startIcon={<AddIcon sx={{ fontSize: 18 }} />}
            sx={{ minWidth: 170 }}
          />
        </Box>
      </Box>

      {/* Table Section */}
      <Box sx={{ p: 0 }}>
        <Paper elevation={0} sx={{ boxShadow: "none", borderRadius: 0, p: 0 }}>
          <Box
            sx={{
              width: "100%",
              overflowX: "auto",
              borderRadius: 2,
              border: "1px solid #E7E7E7",
            }}
          >
            <Box
              component="table"
              sx={{ width: "100%", borderCollapse: "collapse" }}
            >
              <Box component="thead" sx={{ background: "#F5F5F5" }}>
                <Box component="tr">
                  <Box component="th" sx={tableHeadCellStyle}>
                    Document Name
                  </Box>
                  <Box component="th" sx={tableHeadCellStyle}>
                    Type
                  </Box>
                  <Box component="th" sx={tableHeadCellStyle}>
                    Uploaded Date
                  </Box>
                  <Box component="th" sx={tableHeadCellStyle}>
                    Description
                  </Box>
                  <Box component="th" sx={tableHeadCellStyle}>
                    Status
                  </Box>
                  <Box component="th" sx={tableHeadCellStyle}>
                    Action
                  </Box>
                </Box>
              </Box>
              <Box component="tbody">
                {DOCUMENTS.map((doc, idx) => (
                  <Box
                    component="tr"
                    key={idx}
                    sx={{
                      borderBottom: "1px solid #E7E7E7",
                      background: idx % 2 === 0 ? "#fff" : "#FAFAFA",
                    }}
                  >
                    <Box component="td" sx={tableBodyCellStyle}>
                      {doc.name}
                    </Box>
                    <Box component="td" sx={tableBodyCellStyle}>
                      {doc.type}
                    </Box>
                    <Box component="td" sx={tableBodyCellStyle}>
                      {doc.uploaded}
                    </Box>
                    <Box component="td" sx={tableBodyCellStyle}>
                      {doc.description}
                    </Box>
                    <Box component="td" sx={tableBodyCellStyle}>
                      <Chip type={statusMap[doc.status]} />
                    </Box>
                    <Box component="td" sx={tableBodyCellStyle}>
                      <ActionButton
                        list={[
                          { label: "View", route: "view" },
                          { label: "Download", route: "download" },
                          { label: "Delete", route: "delete" },
                        ]}
                        onItemSelected={() => {}}
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

const tableHeadCellStyle = {
  fontFamily: "Figtree",
  fontWeight: 600,
  fontSize: 14,
  color: "#74797B",
  padding: "16px",
  borderBottom: "1px solid #E7E7E7",
  textAlign: "left",
};

const tableBodyCellStyle = {
  fontFamily: "Figtree",
  fontWeight: 400,
  fontSize: 14,
  color: "#21262B",
  padding: "8px 16px",
  textAlign: "left",
};

export default PatientDocuments;
