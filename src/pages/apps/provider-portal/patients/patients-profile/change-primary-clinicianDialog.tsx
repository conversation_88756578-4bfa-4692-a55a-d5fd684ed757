import { Grid, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import CustomSelect from "src/common-components/custom-select/customSelect";
import { RootState } from "src/redux/store";

const ChangePrimaryClinicianDialog = () => {
  const { data: getAllSupervisingCliniciansData }: any =
    useSelector(
      (state: RootState) => state.GetAllSupervisingCliniciansReducer
    ) || {};

  const displaySupervisingClinicians = Object.entries(
    getAllSupervisingCliniciansData || {}
  ).map(([uuid, value]) => ({
    value: uuid,
    label: String(value),
  }));

  return (
    <Grid>
      <Grid>
        <Typography>Transfer Client</Typography>
      </Grid>
      <CustomSelect
        placeholder="Select Clinician Name"
        value=""
        items={displaySupervisingClinicians}
        backgroundColor="#FFFFFF"
      />
      <Grid>
        <Typography>Transfer Client</Typography>
      </Grid>
    </Grid>
  );
};

export default ChangePrimaryClinicianDialog;
