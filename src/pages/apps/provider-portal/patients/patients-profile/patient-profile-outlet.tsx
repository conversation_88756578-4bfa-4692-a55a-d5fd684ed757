import { Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import { useEffect, useState } from "react";
import PatientProfileSidebar from "./patients-profile-side-tab";
import PatientDocuments from "./documents/patient-documents";
import Insurance from "./Insurance/Insurance";

// Tab component mapping - easily extensible for future tabs
const TAB_COMPONENTS: Record<string, React.ComponentType> = {
  Documents: PatientDocuments,
  // Add future components here:
  // Profile: PatientProfile,
  // Billing: PatientBilling,
  // Insurance: PatientInsurance,
  Insurance: Insurance,
  // "Notes/Records": PatientNotes,
  // Appointments: PatientAppointments,
};

function PatientProfileOutlet({ customSetTab }: { string }) {
  const [selectedTab, setSelectedTab] = useState("Profile");

  const handleTabChange = (tabId: string) => {
    setSelectedTab(tabId);
  };

  // Smart component renderer
  const renderTabContent = () => {
    
    const TabComponent = TAB_COMPONENTS[selectedTab];
    return TabComponent ? <TabComponent /> : <Outlet />;
  };
  console.log("customSetTab", customSetTab, selectedTab);

  useEffect(() => {
    setSelectedTab(customSetTab);
  }, [customSetTab]);

  return (
    <Box display="flex" width="100%" height="100%">
      <PatientProfileSidebar onTabChange={handleTabChange} selectedTab={selectedTab}/>
      <Box
        sx={{ flex: 1, background: "#FFFFFF", borderTop: "1px solid #E7E7E7" }}
      >
        {renderTabContent()}
      </Box>
    </Box>
  );
}

export default PatientProfileOutlet;
