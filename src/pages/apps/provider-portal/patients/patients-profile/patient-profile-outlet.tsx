import { Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import { useState } from "react";
import PatientProfileSidebar from "./patients-profile-side-tab";
import PatientDocuments from "./documents/patient-documents";

function PatientProfileOutlet() {
  const [selectedTab, setSelectedTab] = useState("Profile");

  const handleTabChange = (tabId: string) => {
    setSelectedTab(tabId);
  };

  return (
    <Box display="flex" width="100%" height="100%">
      <PatientProfileSidebar onTabChange={handleTabChange} />
      <Box
        sx={{ flex: 1, background: "#FFFFFF", borderTop: "1px solid #E7E7E7" }}
      >
        {selectedTab === "Documents" ? <PatientDocuments /> : <Outlet />}
      </Box>
    </Box>
  );
}

export default PatientProfileOutlet;
