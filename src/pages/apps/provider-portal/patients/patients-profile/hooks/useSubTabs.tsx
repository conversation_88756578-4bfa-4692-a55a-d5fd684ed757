import React, { useState } from "react";
import { Box, Tab, Tabs } from "@mui/material";

// Reusable sub-tab configuration interface
export interface SubTabConfig {
  id: string;
  label: string;
  component: React.ComponentType;
}

// Reusable sub-tabs hook
export const useSubTabs = (subTabs: SubTabConfig[], defaultTabIndex: number = 0) => {
  const [selectedSubTab, setSelectedSubTab] = useState(defaultTabIndex);

  const handleSubTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedSubTab(newValue);
  };

  const renderSubTabContent = () => {
    const SubTabComponent = subTabs[selectedSubTab]?.component;
    return SubTabComponent ? <SubTabComponent /> : null;
  };

  const SubTabsHeader = ({ ariaLabel }: { ariaLabel: string }) => (
    <Box sx={{ borderBottom: 1, borderColor: "divider", px: 2, pt: 2 }}>
      <Tabs
        value={selectedSubTab}
        onChange={handleSubTabChange}
        aria-label={ariaLabel}
      >
        {subTabs.map((tab, index) => (
          <Tab key={tab.id} label={tab.label} id={`${ariaLabel}-tab-${index}`} />
        ))}
      </Tabs>
    </Box>
  );

  return {
    selectedSubTab,
    handleSubTabChange,
    renderSubTabContent,
    SubTabsHeader,
  };
};

// Reusable SubTabContainer component
export const SubTabContainer: React.FC<{
  subTabs: SubTabConfig[];
  ariaLabel: string;
  defaultTabIndex?: number;
}> = ({ subTabs, ariaLabel, defaultTabIndex = 0 }) => {
  const { renderSubTabContent, SubTabsHeader } = useSubTabs(subTabs, defaultTabIndex);

  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      <SubTabsHeader ariaLabel={ariaLabel} />
      <Box sx={{ p: 2, height: "calc(100% - 64px)", overflow: "auto" }}>
        {renderSubTabContent()}
      </Box>
    </Box>
  );
};
