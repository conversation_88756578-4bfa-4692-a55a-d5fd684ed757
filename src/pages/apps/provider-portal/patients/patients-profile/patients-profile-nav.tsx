import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import LocalPhoneOutlinedIcon from "@mui/icons-material/LocalPhoneOutlined";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import TagOutlinedIcon from "@mui/icons-material/TagOutlined";
import { Avatar, Box, Chip, Grid, Paper, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "src/redux/store";
import {
  capitalizeFirstLetter,
  formatDateToMMDDYYYY,
  formatPhoneNumber,
} from "../../../../../common-components/utils/stringUtils";
import {
  PatientDemographicsLabels,
  PatientNavCardLabels,
} from "../../../../../constants/formConst";
import StickyNotes from "./sticky-notes";
import { getDataFromLocalStorage } from "../../../../../utils/localStorage";
import { getPatientById } from "../../../../../redux/auth/patient/get-patient-by-id-reducer";

const calculateAge = (dob: string) => {
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age.toString();
};

interface PatientData {
  clinician: string;
  contactNumber: string;
  dob: string;
  emailId: string;
  memberSince: string;
  mrn: string;
  patientName: string;
  paymentMethod: string;
  status: string;
  gender: string;
  age: string;
  phone: string;
  address: string;
  language: string;
  balance: string;
  lastVisit: string;
  alertNote: string;
  notes: string;
  profileImageUrl: string;
  uuid: string;
}

const PatientProfileNav = () => {
  // const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();

  const [patientData, setPatientData] = useState<PatientData>({
    clinician: "",
    contactNumber: "",
    dob: "",
    emailId: "",
    memberSince: "",
    mrn: "",
    patientName: "",
    paymentMethod: "",
    status: "",
    gender: "",
    age: "",
    phone: "",
    address: "",
    language: "",
    balance: "",
    lastVisit: "",
    alertNote: "",
    notes: "",
    profileImageUrl: "",
    uuid: "",
  });

  const { data: getPatientByIdData, status: getPatientByIdStatus } =
    useSelector((state: RootState) => state.GetPatientByIdReducer) as {
      data: any;
      status: string;
    };

  console.log("qwerty", getDataFromLocalStorage("patientUuid"));

  console.log("getPatientByIdData",getPatientByIdData)

  useEffect(() => {
    if (
      getPatientByIdStatus === "succeeded" &&
      getPatientByIdData?.dateOfBirth
    ) {
      const fullAddress = [
        getPatientByIdData.address?.line1,
        getPatientByIdData.address?.line2,
        getPatientByIdData.address?.city,
        getPatientByIdData.address?.state,
        getPatientByIdData.address?.zipcode,
      ]
        .filter(Boolean)
        .join(", ");

      setPatientData({
        ...patientData,
        patientName:
          getPatientByIdData.preferredName ||
          `${getPatientByIdData.firstName} ${getPatientByIdData.lastName}`,
        emailId: getPatientByIdData.emailId || "-",
        dob: getPatientByIdData.dateOfBirth,
        phone: `${formatPhoneNumber(getPatientByIdData.phoneNumber)}`,
        address: fullAddress || "-",
        paymentMethod: getPatientByIdData.paymentMethod,
        clinician: getPatientByIdData.clinicianName || "N/A",
        gender: getPatientByIdData.genderIdentity,
        status: getPatientByIdData.active ? "Active" : "Inactive",
        age: calculateAge(getPatientByIdData.dateOfBirth),
        language: capitalizeFirstLetter(getPatientByIdData?.preferredLanguage),
        profileImageUrl: getPatientByIdData.profileImageUrl || "",
        uuid: getPatientByIdData?.uuid,
        mrn: getPatientByIdData?.mrn,
      });
    }
  }, [getPatientByIdData, getPatientByIdStatus]);


  useEffect(() => {
    const rawUuid = getDataFromLocalStorage("patientUuid");
    // Remove quotes if present
    const uuid = rawUuid ? rawUuid.replace(/^"|"$/g, "") : "";
    if (uuid) {
      dispatch(getPatientById(uuid));
    }
  }, [dispatch, getDataFromLocalStorage("patientUuid")]);

  return (
    <Paper
      sx={{
        borderRadius: 2,
        p: 2,
        borderBottomLeftRadius: "0",
        borderBottomRightRadius: "0",
      }}
    >
      <Grid container alignItems="center" spacing={2}>
        {/* Profile Section */}
        <Grid item ml={2}>
          <Avatar
            src={patientData?.profileImageUrl}
            sx={{ width: "78px", height: "78px" }}
          />
        </Grid>
        <Grid item xs ml={2}>
          <Box display="flex" alignItems="center" gap={"8px"}>
            <Typography variant="bodySemiBold3">
              {patientData?.patientName || ""}
            </Typography>
            {patientData?.gender && (
              <Chip
                label={capitalizeFirstLetter(patientData?.gender)}
                variant="outlined"
                sx={{ color: "#0068FF", backgroundColor: "#EEF7FE" }}
                size="small"
              />
            )}
          </Box>

          <Box
            display="flex"
            flexDirection="row"
            gap="24px"
            mt={1}
            color="#595F63"
          >
            <Box display="flex" flexDirection="column" gap={1}>
              <Box display="flex" alignItems="center" gap={"8px"}>
                <CalendarTodayIcon sx={{ width: "16px", height: "16px" }} />
                <Typography variant="bodyRegular4">
                  {patientData?.dob && patientData?.age
                    ? `${formatDateToMMDDYYYY(patientData.dob)} (${patientData.age} yrs)`
                    : ""}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={"8px"}>
                <TagOutlinedIcon sx={{ width: "16px", height: "16px" }} />
                <Typography variant="bodyRegular4">
                  {patientData?.mrn || ""}
                </Typography>
              </Box>
            </Box>
            <Box display="flex" flexDirection="column" gap={1}>
              <Box display="flex" alignItems="center" gap={"8px"}>
                <LocalPhoneOutlinedIcon
                  sx={{ width: "16px", height: "16px" }}
                />
                <Typography variant="bodyRegular4">
                  {patientData?.phone || ""}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={"8px"}>
                <MailOutlineIcon sx={{ width: "16px", height: "16px" }} />
                <Typography variant="bodyRegular4">
                  {patientData?.emailId || ""}
                </Typography>
              </Box>
            </Box>
            <Box display="flex" flexDirection="column" gap={1}>
              <Box display="flex" alignItems="center" gap={"8px"}>
                <LocationOnIcon sx={{ width: "16px", height: "16px" }} />
                <Typography variant="bodyRegular4">
                  {patientData?.address}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" gap={"8px"} ml={0.5}>
                {/* <AttachMoneyOutlinedIcon /> */}$
                <Grid ml={0.5}>
                  <Typography variant="bodyRegular4">
                    {PatientDemographicsLabels.CLIENT_BALANCE}
                  </Typography>
                </Grid>
                <Typography variant="bodyRegular4">0</Typography>
              </Box>
            </Box>
          </Box>
        </Grid>
        <Grid item>
          <Box display="flex" flexDirection="row" gap={2}>
            <Paper
              sx={{
                width: "10vw",
                height: "10.5vh",
                borderRadius: "4px",
                padding: "8px",
                display: "flex",
                flexDirection: "column",
                gap: "10px",
                background: "#f5f8ff",
              }}
            >
              <Box display="flex" flexDirection="column">
                <Typography variant="bodyMedium4">
                  {PatientNavCardLabels.PRIMARY_CLINICIAN}:
                </Typography>
                <Typography variant="bodyRegular5">
                  {patientData?.clinician}
                </Typography>
              </Box>
            </Paper>

            <Grid>
              <StickyNotes uuid={patientData?.uuid} isAlertNote={true} />
            </Grid>
            <Grid>
              <StickyNotes uuid={patientData?.uuid} />
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PatientProfileNav;
