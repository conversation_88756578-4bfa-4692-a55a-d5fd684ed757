import {
  <PERSON>,
  <PERSON>rid,
  Typo<PERSON>,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import CustomInput from "../../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";
import { useEffect, useState } from "react";
import CustomDatePicker from "../../../../../../common-components/custom-date-picker/custom-date-picker";
import CustomMultipleFilesUpload from "../../../../../../common-components/multiple-files-upload copy/custom-multiple-files-upload";
import { relationshipOptions } from "../../../../../../constants/dropdown-values";
import { insurances } from "../../../../../../constants/insurances-array";
import { yupResolver } from "@hookform/resolvers/yup";
import { addInsuranceValidationSchema } from "./insurance-form-schema";
import { outlinedButton } from "./custom-button-style";
import {
  FileUploadActions,
  PatientFormLabels,
  PatientFormPlaceholders,
  PatientFormSectionTitles,
  SUBSCRIBER_DETAILS_ENUM,
  SUBSCRIBER_DETAILS_PLACEHOLDER_ENUM,
} from "../../../../../../constants/formConst";

// Updated interface to match the screenshot
export interface AddInsuranceFormValues {
  insuranceType: string;
  insuranceName: string;
  memberId: string;
  groupId?: string | null;
  relationship: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  insuranceCardFront?: string | null;
  insuranceCardBack?: string | null;
}

export default function AddInsuranceForm({
  defaultValues,
  onSubmit,
  isEdit = false,
}: {
  defaultValues?: Partial<AddInsuranceFormValues>;
  onSubmit?: (values: AddInsuranceFormValues) => void;
  isEdit?: boolean;
}) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<AddInsuranceFormValues>({
    resolver: yupResolver(addInsuranceValidationSchema),
    defaultValues: {
      insuranceType: "primary",
      insuranceName: "",
      memberId: "",
      groupId: "",
      relationship: "",
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      insuranceCardFront: null,
      insuranceCardBack: null,
      ...defaultValues,
    },
    mode: "onTouched",
  });

  // For file upload preview
  const [primaryFrontPhoto, setPrimaryFrontPhoto] = useState<string | null>(
    null
  );
  const [primaryBackPhoto, setPrimaryBackPhoto] = useState<string | null>(null);

  useEffect(() => {
    setValue("insuranceCardFront", primaryFrontPhoto);
    setValue("insuranceCardBack", primaryBackPhoto);
  }, [primaryFrontPhoto, primaryBackPhoto]);
  return (
    <Box
      sx={{
        height: "100%",
        bgcolor: "#fff",
        display: "flex",
        flexDirection: "column",
        overflow: "auto",
      }}
    >
      <form
        onSubmit={handleSubmit((values) => onSubmit && onSubmit(values))}
        style={{ display: "flex", flexDirection: "column", height: "100%" }}
      >
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={3}>
            {/* Primary Insurance Header */}
            <Grid item xs={12}>
              <Typography
                variant="bodyMedium2"
                sx={{
                  color: "#1F2937",
                  mb: 2,
                }}
              >
                Primary Insurance
              </Typography>
            </Grid>
            <Grid container spacing={2} pl={3}>
              {/* Insurance Name */}
              <Grid item xs={4}>
                <CustomLabel
                  label={PatientFormLabels.INSURANCE_NAME}
                  isRequired
                />
                <Controller
                  name="insuranceName"
                  control={control}
                  render={({ field }) => (
                    <CustomSelect
                      {...field}
                      items={[...insurances]}
                      placeholder={
                        PatientFormPlaceholders.SELECT_INSURANCE_NAME
                      }
                      hasError={!!errors.insuranceName}
                      errorMessage={errors.insuranceName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Member ID */}
              <Grid item xs={4}>
                <CustomLabel label={PatientFormLabels.MEMBER_ID} isRequired />
                <Controller
                  name="memberId"
                  control={control}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder={PatientFormPlaceholders.ENTER_MEMBER_ID}
                      hasError={!!errors.memberId}
                      errorMessage={errors.memberId?.message}
                    />
                  )}
                />
              </Grid>

              {/* Group ID */}
              <Grid item xs={4}>
                <CustomLabel label={PatientFormLabels.GROUP_ID} />
                <Controller
                  name="groupId"
                  control={control}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder={PatientFormPlaceholders.ENTER_GROUP_ID}
                      hasError={!!errors.groupId}
                      errorMessage={errors.groupId?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>

            {/* Patient Relationship with Subscriber */}
            <Grid item xs={12}>
              <CustomLabel
                label={PatientFormLabels.PATIENT_RELATIONSHIP}
                isRequired
              />
              <Controller
                name="relationship"
                control={control}
                render={({ field }) => (
                  <RadioGroup row {...field} sx={{ mt: 1 }}>
                    {relationshipOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio sx={{ color: "#3B82F6" }} />}
                        label={option.label}
                        sx={{
                          mr: 4,
                          ".MuiFormControlLabel-label": {
                            fontFamily: "Figtree",
                            fontWeight: 500,
                            fontSize: 14,
                            color: "#374151",
                          },
                        }}
                      />
                    ))}
                  </RadioGroup>
                )}
              />
            </Grid>

            {/* Subscriber Details Header */}
            <Grid item xs={12}>
              <Typography
                variant="bodySemiBold3"
                sx={{
                  color: "#1F2937",
                  mb: 1,
                  mt: 2,
                }}
              >
                {PatientFormSectionTitles.SUBSCRIBER_DETAILS}
              </Typography>
            </Grid>

            {/* First Name and Last Name */}
            <Grid container spacing={2} pl={3}>
              {/* First Name */}
              <Grid item xs={4}>
                <CustomLabel
                  label={SUBSCRIBER_DETAILS_ENUM.FIRST_NAME}
                  isRequired
                />
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder={
                        SUBSCRIBER_DETAILS_PLACEHOLDER_ENUM.ENTER_FIRST_NAME
                      }
                      hasError={!!errors.firstName}
                      errorMessage={errors.firstName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Last Name */}
              <Grid item xs={4}>
                <CustomLabel
                  label={SUBSCRIBER_DETAILS_ENUM.LAST_NAME}
                  isRequired
                />
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder={
                        SUBSCRIBER_DETAILS_PLACEHOLDER_ENUM.ENTER_LAST_NAME
                      }
                      hasError={!!errors.lastName}
                      errorMessage={errors.lastName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Date of Birth */}
              <Grid item xs={4}>
                <CustomLabel
                  label={SUBSCRIBER_DETAILS_ENUM.DATE_OF_BIRTH}
                  isRequired
                />
                <Controller
                  name="dateOfBirth"
                  control={control}
                  render={({ field }) => (
                    <CustomDatePicker
                      value={field.value}
                      handleDateChange={field.onChange}
                      hasError={!!errors.dateOfBirth}
                      errorMessage={errors.dateOfBirth?.message}
                      placeholder="Select Date"
                    />
                  )}
                />
              </Grid>
            </Grid>

            {/* Upload Insurance Card Header */}
            <Grid item xs={12}>
              <Typography
                variant="bodySemiBold3"
                sx={{
                  color: "#1F2937",
                  mb: 2,
                  mt: 2,
                }}
              >
                {PatientFormSectionTitles.UPLOAD_INSURANCE_CARD}
              </Typography>
            </Grid>

            {/* Upload Insurance Card Front and Back */}
            <Grid item xs={6}>
              <Controller
                name="insuranceCardFront"
                control={control}
                render={({}) => (
                  <CustomMultipleFilesUpload
                    placeholder={
                      FileUploadActions.CLICK_TO_UPLOAD +
                      " " +
                      FileUploadActions.OR_DRAG_AND_DROP
                    }
                    onUpload={(base64: string) => {
                      setPrimaryFrontPhoto(base64);
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="insuranceCardBack"
                control={control}
                render={({}) => (
                  <CustomMultipleFilesUpload
                    placeholder={
                      FileUploadActions.CLICK_TO_UPLOAD +
                      " " +
                      FileUploadActions.OR_DRAG_AND_DROP
                    }
                    onUpload={(file: string) => {
                      setPrimaryBackPhoto(file);
                    }}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        {/* Action Buttons always at the bottom */}
        <Box
          sx={{ mt: 4, display: "flex", justifyContent: "flex-end", gap: 2 }}
        >
          <Button variant="outlined" sx={outlinedButton}>
            Cancel
          </Button>
          <CustomButton
            label={isEdit ? "Update" : "Add"}
            variant="filled"
            type="submit"
          />
        </Box>
      </form>
    </Box>
  );
}
