import {
  <PERSON>,
  <PERSON>rid,
  <PERSON>po<PERSON>,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
} from "@mui/material";
import { useF<PERSON>, Controller } from "react-hook-form";
import CustomInput from "../../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";
import { useState } from "react";
import CustomDatePicker from "../../../../../../common-components/custom-date-picker/custom-date-picker";
import CustomMultipleFilesUpload from "../../../../../../common-components/multiple-files-upload copy/custom-multiple-files-upload";

const insuranceTypeOptions = [
  { value: "primary", label: "Primary" },
  { value: "secondary", label: "Secondary" },
  { value: "tertiary", label: "Tertiary" },
];

const planTypeOptions = [
  { value: "individual", label: "Individual" },
  { value: "family", label: "Family" },
  { value: "group", label: "Group" },
];

const relationshipOptions = [
  { value: "SELF", label: "Self" },
  { value: "SPOUSE", label: "Spouse" },
  { value: "CHILD", label: "Child" },
  { value: "DEPENDENT", label: "Dependent" },
];

const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

// Updated interface to match the screenshot
export interface AddInsuranceFormValues {
  insuranceType: string;
  insuranceName: string;
  memberId: string;
  groupId: string;
  relationship: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  insuranceCardFront?: File | null;
  insuranceCardBack?: File | null;
}

export default function AddInsuranceForm({
  defaultValues,
  onSubmit,
  isEdit = false,
}: {
  defaultValues?: Partial<AddInsuranceFormValues>;
  onSubmit?: (values: AddInsuranceFormValues) => void;
  isEdit?: boolean;
}) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    watch,
  } = useForm<AddInsuranceFormValues>({
    defaultValues: {
      insuranceType: "primary",
      insuranceName: "",
      memberId: "",
      groupId: "",
      relationship: "SELF",
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      insuranceCardFront: null,
      insuranceCardBack: null,
      ...defaultValues,
    },
    mode: "onTouched",
  });

  // For file upload preview
  const [frontFileName, setFrontFileName] = useState("");
  const [backFileName, setBackFileName] = useState("");
  const [primaryFrontPhoto, setPrimaryFrontPhoto] = useState<string | null>(
    null
  );
  const [primaryBackPhoto, setPrimaryBackPhoto] = useState<string | null>(null);
  return (
    <Box
      sx={{
        height: "100%",
        bgcolor: "#fff",
        display: "flex",
        flexDirection: "column",
        overflow: "auto",
      }}
    >
      <form
        onSubmit={handleSubmit((values) => onSubmit && onSubmit(values))}
        style={{ display: "flex", flexDirection: "column", height: "100%" }}
      >
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={3}>
            {/* Primary Insurance Header */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: "Figtree",
                  fontWeight: 600,
                  fontSize: 18,
                  color: "#1F2937",
                  mb: 2,
                }}
              >
                Primary Insurance
              </Typography>
            </Grid>
            <Grid container spacing={2} pl={3}>
              {/* Insurance Name */}
              <Grid item xs={4}>
                <CustomLabel label="Insurance Name" isRequired />
                <Controller
                  name="insuranceName"
                  control={control}
                  rules={{ required: "Insurance name is required" }}
                  render={({ field }) => (
                    <CustomSelect
                      {...field}
                      items={[
                        {
                          value: "blue-cross",
                          label: "Blue Cross Blue Shield",
                        },
                        { value: "aetna", label: "Aetna" },
                        { value: "cigna", label: "Cigna" },
                        { value: "humana", label: "Humana" },
                        { value: "other", label: "Other" },
                      ]}
                      placeholder="Select Insurance Name"
                      hasError={!!errors.insuranceName}
                      errorMessage={errors.insuranceName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Member ID */}
              <Grid item xs={4}>
                <CustomLabel label="Member ID" isRequired />
                <Controller
                  name="memberId"
                  control={control}
                  rules={{ required: "Member ID is required" }}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder="Enter Member ID"
                      hasError={!!errors.memberId}
                      errorMessage={errors.memberId?.message}
                    />
                  )}
                />
              </Grid>

              {/* Group ID */}
              <Grid item xs={4}>
                <CustomLabel label="Group ID" />
                <Controller
                  name="groupId"
                  control={control}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder="Enter Group ID"
                      hasError={!!errors.groupId}
                      errorMessage={errors.groupId?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>

            {/* Patient Relationship with Subscriber */}
            <Grid item xs={12}>
              <CustomLabel
                label="Patient Relationship with Subscriber"
                isRequired
              />
              <Controller
                name="relationship"
                control={control}
                rules={{ required: "Relationship is required" }}
                render={({ field }) => (
                  <RadioGroup row {...field} sx={{ mt: 1 }}>
                    {relationshipOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio sx={{ color: "#3B82F6" }} />}
                        label={option.label}
                        sx={{
                          mr: 4,
                          ".MuiFormControlLabel-label": {
                            fontFamily: "Figtree",
                            fontWeight: 500,
                            fontSize: 14,
                            color: "#374151",
                          },
                        }}
                      />
                    ))}
                  </RadioGroup>
                )}
              />
            </Grid>

            {/* Subscriber Details Header */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: "Figtree",
                  fontWeight: 600,
                  fontSize: 16,
                  color: "#1F2937",
                  mb: 1,
                  mt: 2,
                }}
              >
                Subscriber Details
              </Typography>
            </Grid>

            {/* First Name and Last Name */}
            <Grid container spacing={2} pl={3}>
              {/* First Name */}
              <Grid item xs={4}>
                <CustomLabel label="First Name" isRequired />
                <Controller
                  name="firstName"
                  control={control}
                  rules={{ required: "First name is required" }}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder="Enter First Name"
                      hasError={!!errors.firstName}
                      errorMessage={errors.firstName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Last Name */}
              <Grid item xs={4}>
                <CustomLabel label="Last Name" isRequired />
                <Controller
                  name="lastName"
                  control={control}
                  rules={{ required: "Last name is required" }}
                  render={({ field }) => (
                    <CustomInput
                      {...field}
                      placeholder="Enter Last Name"
                      hasError={!!errors.lastName}
                      errorMessage={errors.lastName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Date of Birth */}
              <Grid item xs={4}>
                <CustomLabel label="Date Of Birth" isRequired />
                <Controller
                  name="dateOfBirth"
                  control={control}
                  rules={{ required: "Date of birth is required" }}
                  render={({ field }) => (
                    <CustomDatePicker
                      value={field.value}
                      handleDateChange={field.onChange}
                      hasError={!!errors.dateOfBirth}
                      errorMessage={errors.dateOfBirth?.message}
                      placeholder="Select Date"
                    />
                  )}
                />
              </Grid>
            </Grid>

            {/* Upload Insurance Card Header */}
            <Grid item xs={12}>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: "Figtree",
                  fontWeight: 600,
                  fontSize: 16,
                  color: "#1F2937",
                  mb: 2,
                  mt: 2,
                }}
              >
                Upload Insurance Card
              </Typography>
            </Grid>

            {/* Upload Insurance Card Front and Back */}
            <Grid item xs={6}>
              <Controller
                name="insuranceCardFront"
                control={control}
                render={({ field }) => (
                  <CustomMultipleFilesUpload
                    placeholder="Click here to add file, drag & drop 
                    or browse back side of card"
                    onUpload={(base64: string) => {
                      setPrimaryFrontPhoto(base64);
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="insuranceCardBack"
                control={control}
                render={({ field }) => (
                  <CustomMultipleFilesUpload
                    placeholder="Click here to add file, drag & drop 
                    or browse back side of card"
                    onUpload={(base64: string) => {
                      setPrimaryBackPhoto(base64);
                    }}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        {/* Action Buttons always at the bottom */}
        <Box
          sx={{ mt: 4, display: "flex", justifyContent: "flex-end", gap: 2 }}
        >
          <Button
            variant="outlined"
            sx={{
              borderColor: "#D1D5DB",
              color: "#6B7280",
              fontFamily: "Figtree",
              fontWeight: 500,
              fontSize: 14,
              textTransform: "none",
              px: 3,
            }}
          >
            Cancel
          </Button>
          <CustomButton
            label={isEdit ? "Update" : "Add"}
            variant="filled"
            type="submit"
          />
        </Box>
      </form>
    </Box>
  );
}
