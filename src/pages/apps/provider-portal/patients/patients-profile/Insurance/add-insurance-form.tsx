import { <PERSON>, <PERSON>rid, <PERSON>po<PERSON>, RadioGroup, FormControlLabel, Radio, Button } from "@mui/material";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import CustomInput from "../../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../../common-components/custom-select/customSelect";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { useState } from "react";
import CustomDatePicker from "../../../../../../common-components/custom-date-picker/custom-date-picker";

const insuranceTypeOptions = [
  { value: "primary", label: "Primary" },
  { value: "secondary", label: "Secondary" },
  { value: "tertiary", label: "Tertiary" },
];

const planTypeOptions = [
  { value: "individual", label: "Individual" },
  { value: "family", label: "Family" },
  { value: "group", label: "Group" },
];

const relationshipOptions = [
  { value: "self", label: "Self" },
  { value: "spouse", label: "Spouse" },
  { value: "child", label: "Child" },
  { value: "other", label: "Other" },
];

const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

export interface AddInsuranceFormValues {
  insuranceType: string;
  planName: string;
  planType: string;
  relationship: string;
  name: string;
  dob: string;
  gender: string;
  memberId: string;
  groupNumber: string;
  payerId: string;
  insurancePhone: string;
  address: string;
  insuranceCardFront?: File | null;
  insuranceCardBack?: File | null;
}

export default function AddInsuranceForm({
  defaultValues,
  onSubmit,
  isEdit = false,
}: {
  defaultValues?: Partial<AddInsuranceFormValues>;
  onSubmit?: (values: AddInsuranceFormValues) => void;
  isEdit?: boolean;
}) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    watch,
  } = useForm<AddInsuranceFormValues>({
    defaultValues: {
      insuranceType: "primary",
      planName: "",
      planType: "",
      relationship: "",
      name: "",
      dob: "",
      gender: "",
      memberId: "",
      groupNumber: "",
      payerId: "",
      insurancePhone: "",
      address: "",
      insuranceCardFront: null,
      insuranceCardBack: null,
      ...defaultValues,
    },
    mode: "onTouched",
  });

  // For file upload preview
  const [frontFileName, setFrontFileName] = useState("");
  const [backFileName, setBackFileName] = useState("");

  return (
    <Box
      sx={{
        p: 0,
        width: "60vw",
        height: "100vh",
        maxWidth: 700,
        bgcolor: "#fff",
        display: "flex",
        flexDirection: "column",
        borderRadius: "16px 0 0 16px",
        overflow: "auto",
      }}
    >
      <Box sx={{ p: 4, pb: 0 }}>
        <Typography
          sx={{ fontFamily: "Figtree", fontWeight: 600, fontSize: 20, mb: 3 }}
        >
          {isEdit ? "Edit Insurance" : "Add Insurance"}
        </Typography>
        <form onSubmit={handleSubmit((values) => onSubmit && onSubmit(values))}>
          <Grid container spacing={3}>
            {/* Insurance Type as RadioGroup */}
            <Grid item xs={12}>
              <CustomLabel label="Insurance Type" isRequired />
              <Controller
                name="insuranceType"
                control={control}
                rules={{ required: "Insurance type is required" }}
                render={({ field }) => (
                  <RadioGroup row {...field}>
                    {insuranceTypeOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio color="primary" />}
                        label={option.label}
                        sx={{
                          mr: 4,
                          fontFamily: "Figtree",
                          fontWeight: 500,
                          fontSize: 14,
                        }}
                      />
                    ))}
                  </RadioGroup>
                )}
              />
              {errors.insuranceType && (
                <Typography color="error" fontSize={12} mt={0.5}>
                  {errors.insuranceType.message}
                </Typography>
              )}
            </Grid>
            {/* Plan Name */}
            <Grid item xs={12}>
              <CustomLabel label="Plan Name" isRequired />
              <Controller
                name="planName"
                control={control}
                rules={{ required: "Plan name is required" }}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Plan Name"
                    hasError={!!errors.planName}
                    errorMessage={errors.planName?.message}
                  />
                )}
              />
            </Grid>
            {/* Plan Type */}
            <Grid item xs={12}>
              <CustomLabel label="Plan Type" isRequired />
              <Controller
                name="planType"
                control={control}
                rules={{ required: "Plan type is required" }}
                render={({ field }) => (
                  <CustomSelect
                    {...field}
                    items={planTypeOptions}
                    placeholder="Select Plan Type"
                    hasError={!!errors.planType}
                    errorMessage={errors.planType?.message}
                  />
                )}
              />
            </Grid>
            {/* Relationship */}
            <Grid item xs={12}>
              <CustomLabel label="Patient Relationship with Insurance" isRequired />
              <Controller
                name="relationship"
                control={control}
                rules={{ required: "Relationship is required" }}
                render={({ field }) => (
                  <CustomSelect
                    {...field}
                    items={relationshipOptions}
                    placeholder="Select Relationship"
                    hasError={!!errors.relationship}
                    errorMessage={errors.relationship?.message}
                  />
                )}
              />
            </Grid>
            {/* Name */}
            <Grid item xs={12}>
              <CustomLabel label="Name" isRequired />
              <Controller
                name="name"
                control={control}
                rules={{ required: "Name is required" }}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Name"
                    hasError={!!errors.name}
                    errorMessage={errors.name?.message}
                  />
                )}
              />
            </Grid>
            {/* Date of Birth and Gender */}
            <Grid item xs={6}>
              <CustomLabel label="Date of Birth" isRequired />
              <Controller
                name="dob"
                control={control}
                rules={{ required: "Date of birth is required" }}
                render={({ field }) => (
                  <CustomDatePicker
                    value={field.value}
                    handleDateChange={field.onChange}
                    hasError={!!errors.dob}
                    errorMessage={errors.dob?.message}
                    placeholder="MM/DD/YYYY"
                  />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <CustomLabel label="Gender" isRequired />
              <Controller
                name="gender"
                control={control}
                rules={{ required: "Gender is required" }}
                render={({ field }) => (
                  <CustomSelect
                    {...field}
                    items={genderOptions}
                    placeholder="Select Gender"
                    hasError={!!errors.gender}
                    errorMessage={errors.gender?.message}
                  />
                )}
              />
            </Grid>
            {/* Member ID */}
            <Grid item xs={12}>
              <CustomLabel label="Member ID" isRequired />
              <Controller
                name="memberId"
                control={control}
                rules={{ required: "Member ID is required" }}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Member ID"
                    hasError={!!errors.memberId}
                    errorMessage={errors.memberId?.message}
                  />
                )}
              />
            </Grid>
            {/* Group Number */}
            <Grid item xs={12}>
              <CustomLabel label="Group Number" />
              <Controller
                name="groupNumber"
                control={control}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Group Number"
                    hasError={!!errors.groupNumber}
                    errorMessage={errors.groupNumber?.message}
                  />
                )}
              />
            </Grid>
            {/* Payer ID */}
            <Grid item xs={12}>
              <CustomLabel label="Payer ID" />
              <Controller
                name="payerId"
                control={control}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Payer ID"
                    hasError={!!errors.payerId}
                    errorMessage={errors.payerId?.message}
                  />
                )}
              />
            </Grid>
            {/* Insurance Phone Number */}
            <Grid item xs={12}>
              <CustomLabel label="Insurance Phone Number" />
              <Controller
                name="insurancePhone"
                control={control}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Phone Number"
                    hasError={!!errors.insurancePhone}
                    errorMessage={errors.insurancePhone?.message}
                  />
                )}
              />
            </Grid>
            {/* Address */}
            <Grid item xs={12}>
              <CustomLabel label="Address" />
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <CustomInput
                    {...field}
                    placeholder="Enter Address"
                    hasError={!!errors.address}
                    errorMessage={errors.address?.message}
                  />
                )}
              />
            </Grid>
            {/* Upload Insurance Card Front */}
            <Grid item xs={12}>
              <CustomLabel label="Insurance Card Front" />
              <Controller
                name="insuranceCardFront"
                control={control}
                render={({ field }) => (
                  <Button
                    variant="outlined"
                    component="label"
                    sx={{
                      borderRadius: 2,
                      border: "1px dashed #0068FF",
                      color: "#0068FF",
                      fontFamily: "Figtree",
                      fontWeight: 500,
                      fontSize: 14,
                      textTransform: "none",
                      width: "100%",
                      py: 2,
                      mb: 1,
                    }}
                  >
                    {frontFileName || "Upload Front of Insurance Card"}
                    <input
                      type="file"
                      hidden
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        setFrontFileName(file ? file.name : "");
                        field.onChange(file);
                      }}
                    />
                  </Button>
                )}
              />
            </Grid>
            {/* Upload Insurance Card Back */}
            <Grid item xs={12}>
              <CustomLabel label="Insurance Card Back" />
              <Controller
                name="insuranceCardBack"
                control={control}
                render={({ field }) => (
                  <Button
                    variant="outlined"
                    component="label"
                    sx={{
                      borderRadius: 2,
                      border: "1px dashed #0068FF",
                      color: "#0068FF",
                      fontFamily: "Figtree",
                      fontWeight: 500,
                      fontSize: 14,
                      textTransform: "none",
                      width: "100%",
                      py: 2,
                      mb: 1,
                    }}
                  >
                    {backFileName || "Upload Back of Insurance Card"}
                    <input
                      type="file"
                      hidden
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        setBackFileName(file ? file.name : "");
                        field.onChange(file);
                      }}
                    />
                  </Button>
                )}
              />
            </Grid>
            {/* Submit Button */}
            <Grid item xs={12} mt={2}>
              <CustomButton
                label={isEdit ? "Update Insurance" : "Add Insurance"}
                variant="filled"
                type="submit"
                fullWidth
              />
            </Grid>
          </Grid>
        </form>
      </Box>
    </Box>
  );
}
