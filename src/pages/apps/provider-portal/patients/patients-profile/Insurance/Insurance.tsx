import InsurancePlan from "./insurance-plan";
import InsuranceVerification from "./insurance-verification";
import InsuranceAuthorization from "./insurance-authorization/insurance-authorization";
import { Box, Tabs, Tab, Button } from "@mui/material";
import { useState } from "react";
import { tabLabel, tabSx } from "../../../../../../constants/tabs-widget";
import { Add } from "@mui/icons-material";
import CustomButton from "../../../../../../common-components/custom-button/custom-button";
import { INSURANCE_SUB_TABS } from "../../../../../../constants/formConst";
import DrawerBS from "../../../../../../common-components/drawer-bs/custom-drawer";
import PreAuthForm from "./insurance-authorization/pre-auth-form";

const INSURANCE_SUB_TABS_CONFIG = [
  { id: "plans", label: INSURANCE_SUB_TABS.PLANS, component: InsurancePlan },
  {
    id: "verifications",
    label: INSURANCE_SUB_TABS.VERIFICATIONS,
    component: InsuranceVerification,
  },
  {
    id: "authorization",
    label: INSURANCE_SUB_TABS.AUTHORIZATION,
    component: InsuranceAuthorization,
  },
];

export default function Insurance() {
  const [selectedSubTab, setSelectedSubTab] = useState(0);
  const [preAuthDrawerOpen, setPreAuthDrawerOpen] = useState(false);
  const handleSubTabChange = (
    _event: React.SyntheticEvent,
    newValue: number
  ) => {
    setSelectedSubTab(newValue);
  };
  const SubTabComponent = INSURANCE_SUB_TABS_CONFIG[selectedSubTab]?.component;

  return (
    <Box sx={{ flex: 1, minWidth: 0 }}>
      {/* Figma-accurate Tabs header */}
      <Box
        sx={{ borderBottom: 1, borderColor: "divider", p: 1.5 }}
        display="flex"
        flexDirection={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        width="100%"
        alignItems={{ xs: "flex-start", sm: "center" }}
        gap={2}
      >
        <Tabs
          value={selectedSubTab}
          onChange={handleSubTabChange}
          aria-label="insurance sub tabs"
          sx={{ ...tabSx, display: "flex" }}
        >
          {INSURANCE_SUB_TABS_CONFIG.map((tab, _idx) => (
            <Tab
              key={tab.id}
              label={tab.label}
              sx={{ ...tabLabel, padding: "0 10px", minWidth: "auto" }}
            />
          ))}
        </Tabs>
        {/* <CustomButton label="something" variant="contained" /> */}
        {INSURANCE_SUB_TABS_CONFIG[selectedSubTab]?.id === "authorization" ? (
          <CustomButton
            variant="filled"
            label="Add Pre-Authorization"
            startIcon={<Add />}
            onClick={() => setPreAuthDrawerOpen(true)}
          />
        ) : (
          <CustomButton
            variant="filled"
            label={INSURANCE_SUB_TABS.ADD_INSURANCE_ENUM}
            startIcon={<Add />}
          />
        )}
      </Box>
      {/* Subtab content */}
      <Box sx={{ p: 2, minHeight: 200 }}>
        {SubTabComponent ? <SubTabComponent /> : null}
      </Box>
      {/* Drawer for Pre-Authorization */}
      <DrawerBS
        anchor="right"
        open={preAuthDrawerOpen}
        onClose={() => setPreAuthDrawerOpen(false)}
        title="Add Pre-Authorization"
        drawerWidth="35vw"
      >
        <PreAuthForm onSubmit={() => setPreAuthDrawerOpen(false)} setPreAuthDrawerOpen={setPreAuthDrawerOpen} />
      </DrawerBS>
    </Box>
  );
}
