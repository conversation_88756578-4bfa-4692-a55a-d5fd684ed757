import InsurancePlan from "./insurance-plan";
import InsuranceVerification from "./insurance-verification";
import InsuranceAuthorization from "./insurance-authorization";
import { Box, Tabs, Tab, Button } from "@mui/material";
import { useState } from "react";
import { tabLabel, tabSx } from "../../../../../../constants/tabs-widget";
import { Add } from "@mui/icons-material";

const INSURANCE_SUB_TABS = [
  { id: "plans", label: "Plans", component: InsurancePlan },
  {
    id: "verifications",
    label: "Verifications",
    component: InsuranceVerification,
  },
  {
    id: "authorization",
    label: "Authorization",
    component: InsuranceAuthorization,
  },
];

export default function Insurance() {
  const [selectedSubTab, setSelectedSubTab] = useState(0);
  const handleSubTabChange = (
    _event: React.SyntheticEvent,
    newValue: number
  ) => {
    setSelectedSubTab(newValue);
  };
  const SubTabComponent = INSURANCE_SUB_TABS[selectedSubTab]?.component;

  return (
    <Box sx={{ flex: 1, minWidth: 0 }}>
      {/* Figma-accurate Tabs header */}
      <Box
        sx={{ borderBottom: 1, borderColor: "divider", p: 1.5 }}
        display="flex"
        flexDirection={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        width="100%"
        alignItems={{ xs: "flex-start", sm: "center" }}
        gap={2}
      >
        <Tabs
          value={selectedSubTab}
          onChange={handleSubTabChange}
          aria-label="insurance sub tabs"
          sx={{ ...tabSx, display: "flex" }}
        >
          {INSURANCE_SUB_TABS.map((tab, _idx) => (
            <Tab
              key={tab.id}
              label={tab.label}
              sx={{ ...tabLabel, padding: "0 10px", minWidth: "auto" }}
            />
          ))}
        </Tabs>
        {/* <CustomButton label="something" variant="contained" /> */}
        <Button size="large" variant="contained" startIcon={<Add />}>
          Add Insurance
        </Button>
      </Box>
      {/* Subtab content */}
      <Box sx={{ p: 2, minHeight: 200 }}>
        {SubTabComponent ? <SubTabComponent /> : null}
      </Box>
    </Box>
  );
}
