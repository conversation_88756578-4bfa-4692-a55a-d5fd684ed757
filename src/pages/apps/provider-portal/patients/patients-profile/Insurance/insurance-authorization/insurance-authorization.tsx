import { Box, Paper, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import CustomisedTable from "../../../../../../../common-components/table/table";
import { AppDispatch, RootState } from "../../../../../../../redux/store";
import {
  getAllPreAuthorizations,
  PreAuthorizationItem,
} from "../../../../../../../redux/auth/pre-authorization/get-all-pre-authorization-reducer";
import { getPatientByIdState } from "../../../../../../../redux/auth/patient/get-patient-by-id-reducer";
import { apiStatus } from "../../../../../../../models/apiStatus";

// Table header configuration
const headCells = [
  { id: "authNumber", label: "Auth Number" },
  { id: "insurance", label: "Insurance" },
  { id: "cptCode", label: "CPT Code" },
  { id: "verifiedDate", label: "Verified Date" },
  { id: "startDate", label: "Start Date" },
  { id: "endDate", label: "End Date" },
  { id: "clinicians", label: "Clinicians" },
  { id: "action", label: "Action", type: "link" },
  { id: "actionMenu", label: "", type: "action" },
];

export default function InsuranceAuthorization() {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Get clientId from location state (same pattern as other components)
  const locationClientId = location.state;

  // Get patient data from Redux store
  const { data: getPatientByIdData }: getPatientByIdState =
    useSelector((state: RootState) => state.GetPatientByIdReducer) || {};

  // Get the actual clientId for API calls
  const actualClientId = getPatientByIdData?.uuid || locationClientId;

  // Get pre-authorization data from Redux store
  const {
    data: preAuthData,
    status: preAuthStatus,
    error: preAuthError,
  } = useSelector((state: RootState) => state.GetAllPreAuthorizationReducer);

  // Fetch pre-authorization data when component mounts or clientId changes
  useEffect(() => {
    if (actualClientId) {
      dispatch(
        getAllPreAuthorizations({
          clientId: actualClientId,
          page: page,
          pageSize: pageSize,
        })
      );
    }
  }, [dispatch, actualClientId, page, pageSize]);

  // Transform API data to table format
  const transformDataToTableFormat = (items: PreAuthorizationItem[]) => {
    return items.map((item) => ({
      authNumber: item.authNumber,
      insurance: item.insuranceName || "N/A", // Will need to be populated from API
      cptCode: item.allCptCodes
        ? "All"
        : item.procedureCodes?.join(", ") || "N/A",
      verifiedDate: new Date(item.preAuthorizationDate).toLocaleDateString(),
      startDate: new Date(item.startDate).toLocaleDateString(),
      endDate: new Date(item.endDate).toLocaleDateString(),
      clinicians: item.allClinician
        ? "All"
        : item.clinicianNames?.join(", ") || "N/A",
      action: "View Auth",
      actionMenu: [
        { label: "Edit", route: "edit" },
        { label: "Delete", route: "delete" },
      ],
    }));
  };

  // Get table data
  const tableData = preAuthData?.content
    ? transformDataToTableFormat(preAuthData.content)
    : [];

  // Debug logging
  console.log("Actual Client ID:", actualClientId);
  console.log("Pre-Auth Data:", preAuthData);
  console.log("Pre-Auth Status:", preAuthStatus);
  console.log("Table Data:", tableData);

  // Show loading state
  if (preAuthStatus === apiStatus.LOADING) {
    return (
      <Box sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <Typography>Loading pre-authorizations...</Typography>
      </Box>
    );
  }

  // Show error state
  if (preAuthError) {
    return (
      <Box sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <Typography color="error">Error: {preAuthError}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%", p: 0 }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: "1px solid #E7E7E7",
          overflow: "hidden",
          background: "#fff",
        }}
      >
        <CustomisedTable
          headCells={headCells}
          tableData={tableData}
          removeRadius
          setHeight="auto"
        />
      </Paper>
    </Box>
  );
}
