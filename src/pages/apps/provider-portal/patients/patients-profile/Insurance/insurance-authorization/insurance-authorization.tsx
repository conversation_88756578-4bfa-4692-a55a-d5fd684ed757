import { Box, Paper } from "@mui/material";
import CustomisedTable from "../../../../../../../common-components/table/table";

const rows = [
  {
    authNumber: "#09876",
    insurance: "United Healthcare",
    cptCode: "All",
    verifiedDate: "8/16/13",
    startDate: "8/16/13",
    endDate: "8/16/14",
    clinicians: "All",
    action: "View Auth",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    authNumber: "#09876",
    insurance: "United Healthcare",
    cptCode: "All",
    verifiedDate: "8/16/13",
    startDate: "8/16/13",
    endDate: "8/16/14",
    clinicians: "All",
    action: "View Auth",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    authNumber: "#09876",
    insurance: "United Healthcare",
    cptCode: "All",
    verifiedDate: "8/16/13",
    startDate: "8/16/13",
    endDate: "8/16/14",
    clinicians: "All",
    action: "View Auth",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    authNumber: "#09876",
    insurance: "United Healthcare",
    cptCode: "All",
    verifiedDate: "8/16/13",
    startDate: "8/16/13",
    endDate: "8/16/14",
    clinicians: "All",
    action: "View Auth",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

const headCells = [
  { id: "authNumber", label: "Auth Number" },
  { id: "insurance", label: "Insurance" },
  { id: "cptCode", label: "CPT Code" },
  { id: "verifiedDate", label: "Verified Date" },
  { id: "startDate", label: "Start Date" },
  { id: "endDate", label: "End Date" },
  { id: "clinicians", label: "Clinicians" },
  { id: "action", label: "Action", type: "link" },
  { id: "actionMenu", label: "", type: "action" },
];

export default function InsuranceAuthorization() {
  return (
    <Box sx={{ width: "100%", p: 0 }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: "1px solid #E7E7E7",
          overflow: "hidden",
          background: "#fff",
        }}
      >
        <CustomisedTable
          headCells={headCells}
          tableData={rows}
          removeRadius
          setHeight="auto"
        />
      </Paper>
    </Box>
  );
}
