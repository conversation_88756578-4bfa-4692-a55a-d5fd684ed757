import { Box, Paper, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import CustomisedTable from "../../../../../../../common-components/table/table";
import { AppDispatch, RootState } from "../../../../../../../redux/store";
import {
  getAllPreAuthorizations,
  PreAuthorizationItem,
} from "../../../../../../../redux/auth/pre-authorization/get-all-pre-authorization-reducer";
import { getPatientByIdState } from "../../../../../../../redux/auth/patient/get-patient-by-id-reducer";
import { apiStatus } from "../../../../../../../models/apiStatus";
import DrawerBS from "../../../../../../../common-components/drawer-bs/custom-drawer";
import PreAuthForm from "./pre-auth-form";

// Table header configuration
const headCells = [
  { id: "authNumber", label: "Auth Number" },
  { id: "insurance", label: "Insurance" },
  { id: "cptCode", label: "CPT Code" },
  { id: "verifiedDate", label: "Verified Date" },
  { id: "startDate", label: "Start Date" },
  { id: "endDate", label: "End Date" },
  { id: "clinicians", label: "Clinicians" },
  { id: "action", label: "Action", type: "link" },
  { id: "actionMenu", label: "", type: "action" },
];

export default function InsuranceAuthorization() {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [editDrawerOpen, setEditDrawerOpen] = useState(false);
  const [editData, setEditData] = useState(null);

  // Get clientId from location state (same pattern as other components)
  const locationClientId = location.state;

  // Get patient data from Redux store
  const { data: getPatientByIdData }: getPatientByIdState =
    useSelector((state: RootState) => state.GetPatientByIdReducer) || {};

  // Get the actual clientId for API calls
  const actualClientId = getPatientByIdData?.uuid || locationClientId;

  // Get pre-authorization data from Redux store
  const {
    data: preAuthData,
    status: preAuthStatus,
    error: preAuthError,
  } = useSelector((state: RootState) => state.GetAllPreAuthorizationReducer);

  // Fetch pre-authorization data when component mounts or clientId changes
  useEffect(() => {
    if (actualClientId) {
      dispatch(
        getAllPreAuthorizations({
          clientId: actualClientId,
          page: page,
          pageSize: pageSize,
        })
      );
    }
  }, [dispatch, actualClientId, page, pageSize]);

  // Helper to format array for table cell
  const formatArrayForTableCell = (arr: string[], allFlag: boolean) => {
    if (allFlag) return "All";
    if (!arr || arr.length === 0) return "N/A";
    if (arr.length <= 3) {
      return arr.map((name, idx) => <div key={idx}>{name}</div>);
    }
    return (
      <>
        {arr.slice(0, 3).map((name, idx) => (
          <div key={idx}>{name}</div>
        ))}
        <div>...</div>
      </>
    );
  };
  // Transform API data to table format
  const transformDataToTableFormat = (items: PreAuthorizationItem[]) => {
    return items.map((item: any) => {
      // Handle CPT Codes display
      let cptCodeDisplay = "N/A";
      if (item.allCptCodes) {
        cptCodeDisplay = "All";
      } else if (item.cptCodes && item.cptCodes.length > 0) {
        if (item.cptCodes.length <= 2) {
          cptCodeDisplay = item.cptCodes.join(", ");
        } else {
          cptCodeDisplay = `${item.cptCodes.slice(0, 2).join(", ")}...`;
        }
      }

      return {
        authNumber: item.authNumber,
        insurance: item.clientInsuranceName || "N/A",
        cptCode: formatArrayForTableCell(item.cptCodes, item.allCptCodes),
        verifiedDate: new Date(item.preAuthorizationDate).toLocaleDateString(),
        startDate: new Date(item.startDate).toLocaleDateString(),
        endDate: new Date(item.endDate).toLocaleDateString(),
        clinicians: formatArrayForTableCell(
          item.clinicianNames,
          item.allClinician
        ),
        action: "View Auth",
        actionMenu: [
          { label: "Edit", route: "edit" },
          { label: "Delete", route: "delete" },
        ],
      };
    });
  };

  // Get table data
  const tableData = preAuthData?.content
    ? transformDataToTableFormat(preAuthData.content)
    : [];

  const handleEditDrawerClose = () => {
    setEditDrawerOpen(false);
    setEditData(null);
  };

  // Show loading state
  if (preAuthStatus === apiStatus.LOADING) {
    return (
      <Box sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <Typography>Loading pre-authorizations...</Typography>
      </Box>
    );
  }

  // Show error state
  if (preAuthError) {
    return (
      <Box sx={{ width: "100%", p: 2, textAlign: "center" }}>
        <Typography color="error">Error: {preAuthError}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%", p: 0 }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: "1px solid #E7E7E7",
          overflow: "hidden",
          background: "#fff",
        }}
      >
        <CustomisedTable
          headCells={headCells}
          tableData={tableData}
          removeRadius
          setHeight="auto"
          handleOpenDrawer={(rowData) => {
            setEditData(rowData);
            setEditDrawerOpen(true);
          }}
        />
      </Paper>
      <DrawerBS
        open={editDrawerOpen}
        onClose={handleEditDrawerClose}
        title="Edit Pre-Authorization"
        drawerWidth="35vw"
        anchor="right"
      >
        {editData && (
          <PreAuthForm
            defaultValues={editData}
            isEdit
            onSubmit={() => handleEditDrawerClose()}
            onCancel={handleEditDrawerClose}
          />
        )}
      </DrawerBS>
    </Box>
  );
}
