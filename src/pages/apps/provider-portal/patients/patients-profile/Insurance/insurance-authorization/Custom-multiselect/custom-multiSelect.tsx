import {
  Clear as ClearIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import {
  Box,
  Checkbox,
  FormControl,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  TextField,
  Typography,
  FormHelperText,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";

// Assuming CustomLabel is a valid component in your project structure
const CustomLabel = ({
  label,
  isRequired,
}: {
  label: string;
  isRequired?: boolean;
}) => (
  <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 500 }}>
    {label} {isRequired && <span style={{ color: "red" }}>*</span>}
  </Typography>
);

interface Option {
  value: string;
  key: string;
}

interface MultiSelectDropdownProps {
  options: Option[];
  selectedValues: { key: string; value: string }[];
  setSelectedValues: (values: { key: string; value: string }[]) => void;
  isRequired?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  label?: string;
  name: string;
  placeholder?: string;
  handleInputVal?: (inputValue: string) => void;
  allLoadedOptions?: Option[];
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  options = [],
  selectedValues = [],
  setSelectedValues,
  hasError,
  isRequired,
  label,
  handleInputVal,
  placeholder = "Select Group Members",
  allLoadedOptions = [],
  errorMessage,
  // name,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [dropdownStyles, setDropdownStyles] = useState({
    top: 0,
    left: 0,
    width: 0,
  });

  // Use allLoadedOptions if provided, otherwise fall back to options
  const optionsToUse = allLoadedOptions.length > 0 ? allLoadedOptions : options;

  // --- START: Added for 'Select All' functionality ---
  /**
   * Toggles the selection of all available options.
   * If all are currently selected, it deselects all.
   * Otherwise, it selects all available options.
   */
  const handleToggleSelectAll = () => {
    if (selectedValues.length === optionsToUse.length) {
      setSelectedValues([]); // Deselect all
    } else {
      // Select all by mapping over the full list of options
      const allOptionsToSelect = optionsToUse.map((option) => ({
        key: option.key,
        value: option.value,
      }));
      setSelectedValues(allOptionsToSelect);
    }
  };

  // Determines the state of the "Select All" checkbox
  const isAllSelected =
    optionsToUse.length > 0 && selectedValues.length === optionsToUse.length;
  const isIndeterminate =
    selectedValues.length > 0 && selectedValues.length < optionsToUse.length;
  // --- END: Added for 'Select All' functionality ---

  // Get selected options to display at top - use allLoadedOptions to ensure we can find them
  const selectedOptions = optionsToUse.filter((option) =>
    selectedValues.some((selected) => selected.key === option.key)
  );

  // Filter unselected options based on search - only from current options (search results)
  const unselectedOptions = options.filter(
    (option) =>
      !selectedValues.some((selected) => selected.key === option.key) &&
      option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Combine selected options (at top) with filtered unselected options
  const filteredOptions = [...selectedOptions, ...unselectedOptions];

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchTerm(inputValue);
    handleInputVal && handleInputVal(inputValue);
  };

  const handleSelect = (key: string, value: string) => {
    let newSelectedValues;
    if (selectedValues.some((selected) => selected.key === key)) {
      newSelectedValues = selectedValues.filter(
        (selected) => selected.key !== key
      );
    } else {
      newSelectedValues = [...selectedValues, { key, value }];
    }
    setSelectedValues(newSelectedValues);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node) &&
      dropdownListRef.current &&
      !dropdownListRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const handleClearAll = () => {
    setSelectedValues([]);
  };

  const updateDropdownPosition = () => {
    if (dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setDropdownStyles({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  };

  // Function to get display text for selected values
  const getDisplayText = () => {
    if (selectedValues.length === 0) return placeholder;

    const selectedOptionsList = optionsToUse.filter((option) =>
      selectedValues.some((selected) => selected.key === option.key)
    );

    // If all options are selected, show a specific message
    if (selectedValues.length === optionsToUse.length) {
      return `All (${optionsToUse.length}) selected`;
    }

    // Show first few and "..." if too many are selected
    if (selectedOptionsList.length > 2) {
      const firstFew = selectedOptionsList
        .slice(0, 2)
        .map((option) => option.value);
      return `${firstFew.join(", ")} +${selectedOptionsList.length - 2} more`;
    }

    // Otherwise, show all selected options
    return selectedOptionsList.map((option) => option.value).join(", ");
  };

  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();
      window.addEventListener("scroll", updateDropdownPosition, true);
      window.addEventListener("resize", updateDropdownPosition);
      if (searchInputRef.current) {
        // A small delay ensures the element is focusable after rendering
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
    return () => {
      window.removeEventListener("scroll", updateDropdownPosition, true);
      window.removeEventListener("resize", updateDropdownPosition);
    };
  }, [isOpen]);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <Box sx={{ position: "relative" }} ref={dropdownRef}>
      {label && <CustomLabel label={label || ""} isRequired={isRequired} />}

      {/* Dropdown Trigger */}
      <FormControl fullWidth error={hasError}>
        <Box
          onClick={() => setIsOpen(!isOpen)}
          sx={{
            border: hasError ? "1px solid #f87171" : "1px solid #d1d5db",
            borderRadius: 1,
            minHeight: "42px",
            padding: "8px 12px",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            backgroundColor: "white",
            "&:hover": {
              borderColor: hasError ? "#f87171" : "#9ca3af",
            },
            "&:focus-within": {
              borderColor: "#2563eb",
              boxShadow: "0 0 0 1px #2563eb",
            },
          }}
        >
          <Box sx={{ flex: 1, overflow: "hidden" }}>
            <Typography
              variant="body2"
              sx={{
                color: selectedValues.length > 0 ? "#000" : "#9ca3af",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {getDisplayText()}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            {selectedValues.length > 0 && (
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearAll();
                }}
                sx={{
                  mr: 0.5,
                  color: "#9ca3af",
                  "&:hover": { color: "#6b7280" },
                }}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            )}
            <ExpandMoreIcon
              sx={{
                color: "#334155",
                transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
                transition: "transform 0.2s ease-in-out",
              }}
            />
          </Box>
        </Box>

        {/* Error Message Display */}
        {hasError && errorMessage && (
          <FormHelperText error sx={{ mt: 0.5, mx: 1.75 }}>
            {errorMessage}
          </FormHelperText>
        )}
      </FormControl>

      {/* Dropdown Menu */}
      {isOpen &&
        ReactDOM.createPortal(
          <Paper
            ref={dropdownListRef}
            elevation={3}
            sx={{
              position: "fixed",
              top: dropdownStyles.top,
              left: dropdownStyles.left,
              width: dropdownStyles.width,
              zIndex: 9999,
              mt: 0.5,
              maxHeight: "300px",
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
            }}
          >
            {/* Search input */}
            <Box sx={{ p: 1, borderBottom: "1px solid #e5e7eb" }}>
              <TextField
                inputRef={searchInputRef}
                size="small"
                fullWidth
                variant="outlined"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ fontSize: "18px", color: "#9ca3af" }} />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton size="small" onClick={handleClearSearch}>
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "6px",
                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                  },
                }}
              />
            </Box>

            {/* Options list */}
            <Box sx={{ overflow: "auto", flex: 1 }}>
              <List dense disablePadding>
                {/* --- START: Added 'Select All' ListItem --- */}
                {optionsToUse.length > 0 && (
                  <ListItem
                    disablePadding
                    sx={{ borderBottom: "1px solid #e2e8f0" }}
                  >
                    <ListItemButton
                      onClick={handleToggleSelectAll}
                      sx={{ py: 0.5 }}
                    >
                      <Checkbox
                        edge="start"
                        checked={isAllSelected}
                        indeterminate={isIndeterminate}
                        tabIndex={-1}
                        disableRipple
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      <ListItemText
                        primary="Select All"
                        primaryTypographyProps={{
                          variant: "body2",
                          fontWeight: 500,
                        }}
                      />
                    </ListItemButton>
                  </ListItem>
                )}
                {/* --- END: Added 'Select All' ListItem --- */}

                {filteredOptions.length > 0 ? (
                  <>
                    {/* Render the rest of the options */}
                    {filteredOptions.map((option) => (
                      <ListItem
                        key={option.key}
                        disablePadding
                        sx={{
                          ...(selectedValues.some(
                            (sv) => sv.key === option.key
                          ) && {
                            backgroundColor: "#f0f9ff",
                          }),
                        }}
                      >
                        <ListItemButton
                          onClick={() => handleSelect(option.key, option.value)}
                          sx={{ py: 0.5 }}
                        >
                          <Checkbox
                            edge="start"
                            checked={selectedValues.some(
                              (sv) => sv.key === option.key
                            )}
                            tabIndex={-1}
                            disableRipple
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          <ListItemText
                            primary={option.value}
                            primaryTypographyProps={{
                              variant: "body2",
                              fontWeight: selectedValues.some(
                                (sv) => sv.key === option.key
                              )
                                ? 500
                                : 400,
                              color: selectedValues.some(
                                (sv) => sv.key === option.key
                              )
                                ? "#0369a1"
                                : "inherit",
                            }}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </>
                ) : (
                  <Box sx={{ px: 2, py: 4, textAlign: "center" }}>
                    <Typography variant="body2" sx={{ color: "#6b7280" }}>
                      {searchTerm
                        ? "No matching options found"
                        : "No options available"}
                    </Typography>
                  </Box>
                )}
              </List>
            </Box>
          </Paper>,
          document.body
        )}
    </Box>
  );
};

export default MultiSelectDropdown;
