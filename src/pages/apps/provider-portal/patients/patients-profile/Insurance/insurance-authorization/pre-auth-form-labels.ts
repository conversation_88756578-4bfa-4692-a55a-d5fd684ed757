export enum PreAuthFormLabels {
  AUTHORIZATION_NUMBER = "Auth Number",
  DATE = "Date",
  INSURANCE="Insurance",
  CPT_CODE="CPT code",
  CLINICIAN="Clinician",
  VISITS_USED="Visits Used",
  VISITS_ALLOWED="Visits Allowed",
  START_DATE = "Start Date",
  END_DATE = "End Date",
  SPOKE_TO = "Spoke To",
  DASHBOARD_WARNING= "Dashboard Warnings",
  NOTES = "Notes",
}

export enum PreAuthFormPlaceholders {
  ENTER_AUTHORIZATION_NUMBER = "Enter Authorization Number",
  SELECT_DATE = "Select Date",
  SELECT_INSURANCE="Select Insurance",
  SELECT_CPT_CODES="Select CPT Code",
  SELECT_CLINICIAN="Select Clinician",
  VISITS_USED="Select",
  VISITS_ALLOWED="Select",
  SELECT_START_DATE = "Select Start Date",
  SELECT_END_DATE = "Select End Date",
  ENTER_NOTES = "Enter Description",
} 