import { Box, Checkbox, Divider, Grid, Typography } from "@mui/material";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { useLocation } from "react-router-dom";
import CustomInput from "../../../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../../../common-components/custom-select/customSelect";
import CustomButton from "../../../../../../../common-components/custom-button/custom-button";
import CustomDatePicker from "../../../../../../../common-components/custom-date-picker/custom-date-picker";
import CustomLabel from "../../../../../../../common-components/customLabel/customLabel";
import {
  PreAuthFormLabels,
  PreAuthFormPlaceholders,
} from "./pre-auth-form-labels";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAllClinicians } from "../../../../../../../redux/auth/profile/get-all-clinicians";
import { ClinicianPayload } from "src/models/all-const";
import { getAllFeeSchedule } from "../../../../../../../redux/auth/fee-schedule/get-all-fee-schedule-reducer";
import MultiSelectDropdown from "./Custom-multiselect/custom-multiSelect";
import { RootState, AppDispatch } from "../../../../../../../redux/store";
import {
  createPreAuthorization,
  PreAuthorizationData,
} from "../../../../../../../redux/auth/pre-authorization/create-pre-authorization-reducer";
import { apiStatus } from "../../../../../../../models/apiStatus";
import { getClientInsurance } from "../../../../../../../redux/auth/patient/get-client-insurance-reducer";
import { getPatientByIdState } from "../../../../../../../redux/auth/patient/get-patient-by-id-reducer";

export interface PreAuthFormValues {
  authNumber: string;
  date: string;
  insurance: string;
  clinicianIds: { key: string; value: string }[];
  allClinician: boolean;
  feeScheduleIds: { key: string; value: string }[];
  allCptCodes: boolean;
  CPTCode: string[];
  clinician: string;
  visitUsed: string;
  visitAllowed: string;
  startDate: string;
  endDate: string;
  spokeTo: string;
  dashboardWarning: boolean;
  providerNpi: string;
  notes: string;
}

export default function PreAuthForm({
  defaultValues,
  onSubmit,
  isEdit = false,
  setPreAuthDrawerOpen,
  clientId,
  clientInsuranceId,
}: {
  defaultValues?: Partial<PreAuthFormValues>;
  onSubmit?: (values: PreAuthFormValues) => void;
  isEdit?: boolean;
  setPreAuthDrawerOpen?: (val: boolean) => void;
  clientId?: string;
  clientInsuranceId?: string;
}) {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();

  // Get clientId from location state (same pattern as insurance-plan.tsx)
  const locationClientId = location.state;

  // Get data from Redux store
  const { data: cliniciansData } = useSelector(
    (state: RootState) => state.GetAllCliniciansReducer
  );
  const { data: feeScheduleData } = useSelector(
    (state: RootState) => state.GetAllFeeScheduleReducer
  );
  const { data: insuranceData } = useSelector(
    (state: RootState) => state.GetClientInsuranceReducer
  );
  const { status: createPreAuthStatus, error: createPreAuthError } =
    useSelector((state: RootState) => state.CreatePreAuthorizationReducer);

  // Get patient data from Redux store (same pattern as insurance-plan.tsx)
  const { data: getPatientByIdData }: getPatientByIdState =
    useSelector((state: RootState) => state.GetPatientByIdReducer) || {};

  // Get the actual clientId for API calls (same pattern as insurance-plan.tsx)
  const actualClientId =
    getPatientByIdData?.uuid || locationClientId || clientId;

  useEffect(() => {
    dispatch(
      getAllClinicians({
        size: 100, // Get more clinicians
        page: 0,
        searchString: "",
      } as ClinicianPayload)
    );

    dispatch(getAllFeeSchedule({ page: 0, pageSize: 100 })); // Get more fee schedules

    // Fetch client insurance data if actualClientId is available
    if (actualClientId) {
      dispatch(getClientInsurance(actualClientId));
    }
  }, [dispatch, actualClientId]);

  // Transform clinicians data for dropdown
  const clinicianOptions =
    cliniciansData?.content?.map((clinician: any) => ({
      key: clinician.uuid, // UUID for sending to API
      value: `${clinician.firstName} ${clinician.lastName}`, // Display name in dropdown
    })) || [];

  // Transform fee schedule data for dropdown
  const feeScheduleOptions =
    feeScheduleData?.content?.map((feeSchedule: any) => ({
      key: feeSchedule.uuid, // UUID for sending to API
      value: feeSchedule.procedureCode, // Display procedure code in dropdown
    })) || [];

  // Transform insurance data for dropdown
  const insuranceOptions =
    insuranceData?.map((insurance: any) => ({
      value: insurance.uuid, // UUID for sending to API
      label: `${insurance.insuranceName} (${insurance.insuranceType})`, // Display insurance name and type
    })) || [];

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PreAuthFormValues>({
    defaultValues: {
      authNumber: "",
      date: "",
      insurance: "",
      clinicianIds: [],
      allClinician: false,
      feeScheduleIds: [],
      allCptCodes: false,
      CPTCode: [],
      clinician: "",
      visitUsed: "",
      visitAllowed: "",
      startDate: "",
      endDate: "",
      spokeTo: "",
      dashboardWarning: false,
      providerNpi: "",
      notes: "",
      ...defaultValues,
    },
    mode: "onTouched",
  });

  // Handle form submission and extract arrays of IDs
  const handleFormSubmit = async (values: PreAuthFormValues) => {
    // Extract clinician IDs array
    const clinicianIdsArray = values.clinicianIds.map((item) => item.key);

    // Extract fee schedule IDs array
    const feeScheduleIdsArray = values.feeScheduleIds.map((item) => item.key);

    // Create the payload for the pre-auth API
    const preAuthPayload: PreAuthorizationData = {
      authNumber: values.authNumber,
      preAuthorizationDate: values.date,
      clientInsuranceId: values.insurance, // Selected insurance UUID from dropdown
      clinicianIds: clinicianIdsArray,
      feeScheduleIds: feeScheduleIdsArray,
      visitsUsed: parseInt(values.visitUsed) || 0,
      visitsAllowed: parseInt(values.visitAllowed) || 0,
      startDate: values.startDate,
      endDate: values.endDate,
      notes: values.notes,
      dashboardWarnings: values.dashboardWarning,
      spokeTo: values.spokeTo,
      allClinician: values.allClinician,
      allCptCodes: values.allCptCodes,
      clientId: actualClientId || "", // From location/patient data
    };

    console.log("Clinician IDs Array:", clinicianIdsArray);
    console.log("Fee Schedule IDs Array:", feeScheduleIdsArray);
    console.log("Pre-Auth Payload:", preAuthPayload);

    try {
      // Dispatch the API call
      await dispatch(createPreAuthorization(preAuthPayload));

      // Close the drawer on success
      if (setPreAuthDrawerOpen) {
        setPreAuthDrawerOpen(false);
      }

      // Call the onSubmit callback if provided
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error("Failed to create pre-authorization:", error);
    }
  };
  return (
    <form onSubmit={handleSubmit(handleFormSubmit)}>
      <Box
        sx={{
          bgcolor: "#fff",
          p: 2,
          borderRadius: 2,
          maxWidth: 600,
          mx: "auto",
          position: "relative",
          minHeight: "fit-content",
        }}
      >
        {/* Error Message */}
        {createPreAuthError && (
          <Box sx={{ mb: 2, p: 2, bgcolor: "#ffebee", borderRadius: 1 }}>
            <Typography color="error" variant="body2">
              {createPreAuthError}
            </Typography>
          </Box>
        )}
        <Grid container spacing={2} sx={{ pb: 10 }}>
          {/* Authorization Number & Requested Date in same row */}
          <Grid item xs={6}>
            <CustomLabel
              label={PreAuthFormLabels.AUTHORIZATION_NUMBER}
              isRequired
            />
            <Controller
              name="authNumber"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={
                    PreAuthFormPlaceholders.ENTER_AUTHORIZATION_NUMBER
                  }
                  hasError={!!errors.authNumber}
                  errorMessage={errors.authNumber?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.DATE} isRequired />
            <Controller
              name="date"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_DATE}
                  hasError={!!errors.date}
                  errorMessage={errors.date?.message}
                />
              )}
            />
          </Grid>
          {/* Insurance */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.INSURANCE} isRequired />
            <Controller
              name="insurance"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomSelect
                  {...field}
                  items={insuranceOptions}
                  placeholder={PreAuthFormPlaceholders.SELECT_INSURANCE}
                  hasError={!!errors.insurance}
                  errorMessage={errors.insurance?.message}
                />
              )}
            />
          </Grid>

          {/* CPT Codes Multi-Select */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.CPT_CODE} isRequired />
            <Controller
              name="feeScheduleIds"
              control={control}
              rules={{ required: "Please select at least one CPT code" }}
              render={({ field }) => (
                <MultiSelectDropdown
                  name="feeScheduleIds"
                  options={feeScheduleOptions}
                  selectedValues={field.value || []}
                  setSelectedValues={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_CPT_CODES}
                  hasError={!!errors.feeScheduleIds}
                  errorMessage={errors.feeScheduleIds?.message}
                />
              )}
            />
          </Grid>
          {/* Clinicians Multi-Select */}
          <Grid item xs={12}>
            <CustomLabel label="Select Clinicians" isRequired />
            <Controller
              name="clinicianIds"
              control={control}
              rules={{ required: "Please select at least one clinician" }}
              render={({ field }) => (
                <MultiSelectDropdown
                  name="clinicianIds"
                  options={clinicianOptions}
                  selectedValues={field.value || []}
                  setSelectedValues={field.onChange}
                  placeholder="Select Clinicians"
                  hasError={!!errors.clinicianIds}
                  errorMessage={errors.clinicianIds?.message}
                />
              )}
            />
          </Grid>
          {/* visit used */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.VISITS_USED} isRequired />
            <Controller
              name="visitUsed"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.VISITS_USED}
                  hasError={!!errors.visitUsed}
                  errorMessage={errors.visitUsed?.message}
                />
              )}
            />
          </Grid>
          {/* Visits allowed */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.VISITS_ALLOWED} isRequired />
            <Controller
              name="visitAllowed"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.VISITS_ALLOWED}
                  hasError={!!errors.visitAllowed}
                  errorMessage={errors.visitAllowed?.message}
                />
              )}
            />
          </Grid>
          {/* Start Date */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.START_DATE} isRequired />
            <Controller
              name="startDate"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_START_DATE}
                  hasError={!!errors.startDate}
                  errorMessage={errors.startDate?.message}
                />
              )}
            />
          </Grid>
          {/* End Date */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.END_DATE} isRequired />
            <Controller
              name="endDate"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_END_DATE}
                  hasError={!!errors.endDate}
                  errorMessage={errors.endDate?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.SPOKE_TO} isRequired />
            <Controller
              name="clinician"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.SELECT_CLINICIAN}
                  hasError={!!errors.clinician}
                  errorMessage={errors.clinician?.message}
                />
              )}
            />
          </Grid>
          <Grid pt={6} pl={2}>
            <Controller
              name="dashboardWarning"
              control={control}
              // rules={{ required: "Required" }}
              render={({ field }) => <Checkbox size="large" {...field} />}
            />
            <Typography variant="bodyRegular4">
              {PreAuthFormLabels.DASHBOARD_WARNING}
            </Typography>
          </Grid>

          {/* Notes */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.NOTES} />
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.ENTER_NOTES}
                />
              )}
            />
          </Grid>
          <Divider />
        </Grid>
      </Box>
      <Box
        sx={{
          position: "absolute",
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: "#fff",
          p: 2,
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
        }}
      >
        <CustomButton
          label="Cancel"
          variant="outline"
          onClick={() => setPreAuthDrawerOpen && setPreAuthDrawerOpen(false)}
        />
        <CustomButton
          label={
            createPreAuthStatus === apiStatus.LOADING
              ? "Creating..."
              : isEdit
                ? "Update Pre-Authorization"
                : "Add Pre-Authorization"
          }
          variant="filled"
          type="submit"
          disabled={createPreAuthStatus === apiStatus.LOADING}
        />
      </Box>
    </form>
  );
}
