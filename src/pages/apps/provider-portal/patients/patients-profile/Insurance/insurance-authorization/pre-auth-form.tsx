import { Box, Checkbox, Divider, Grid, Typography } from "@mui/material";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import CustomInput from "../../../../../../../common-components/custom-input/customInput";
import CustomSelect from "../../../../../../../common-components/custom-select/customSelect";
import CustomButton from "../../../../../../../common-components/custom-button/custom-button";
import CustomDatePicker from "../../../../../../../common-components/custom-date-picker/custom-date-picker";
import CustomLabel from "../../../../../../../common-components/customLabel/customLabel";
import {
  PreAuthFormLabels,
  PreAuthFormPlaceholders,
} from "./pre-auth-form-labels";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAllClinicians } from "../../../../../../../redux/auth/profile/get-all-clinicians";
import { ClinicianPayload } from "src/models/all-const";
import { getAllFeeSchedule } from "../../../../../../../redux/auth/fee-schedule/get-all-fee-schedule-reducer";
import MultiSelectDropdown from "./Custom-multiselect/custom-multiSelect";
import { RootState } from "../../../../../../../redux/store";

const STATUS_OPTIONS = [
  { value: "pending", label: "Pending" },
  { value: "approved", label: "Approved" },
  { value: "denied", label: "Denied" },
];
const SERVICE_TYPE_OPTIONS = [
  { value: "inpatient", label: "Inpatient" },
  { value: "outpatient", label: "Outpatient" },
  { value: "other", label: "Other" },
];

export interface PreAuthFormValues {
  authNumber: string;
  date: string;
  insurance: string;
  clinicianIds: { key: string; value: string }[];
  allClinician: boolean;
  feeScheduleIds: { key: string; value: string }[];
  allCptCodes: boolean;
  CPTCode: string[];
  clinician: string;
  visitUsed: string;
  visitAllowed: string;
  startDate: string;
  endDate: string;
  spokeTo: string;
  dashboardWarning: boolean;
  providerNpi: string;
  notes: string;
}

export default function PreAuthForm({
  defaultValues,
  onSubmit,
  isEdit = false,
  setPreAuthDrawerOpen,
}: {
  defaultValues?: Partial<PreAuthFormValues>;
  onSubmit?: (values: PreAuthFormValues) => void;
  isEdit?: boolean;
  setPreAuthDrawerOpen?: (val: boolean) => void;
}) {
  const dispatch = useDispatch();

  // Get data from Redux store
  const { data: cliniciansData } = useSelector((state: RootState) => state.GetAllCliniciansReducer);
  const { data: feeScheduleData } = useSelector((state: RootState) => state.GetAllFeeScheduleReducer);

  useEffect(() => {
    dispatch(
      getAllClinicians({
        size: 100, // Get more clinicians
        page: 0,
        searchString: "",
      } as ClinicianPayload)
    );

    dispatch(getAllFeeSchedule({ page: 0, pageSize: 100 })); // Get more fee schedules
  }, [dispatch]);

  // Transform clinicians data for dropdown
  const clinicianOptions = cliniciansData?.content?.map((clinician: any) => ({
    key: clinician.uuid,
    value: clinician.uuid,
    label: `${clinician.firstName} ${clinician.lastName}`,
  })) || [];

  // Transform fee schedule data for dropdown
  const feeScheduleOptions = feeScheduleData?.content?.map((feeSchedule: any) => ({
    key: feeSchedule.uuid,
    value: feeSchedule.uuid,
    label: feeSchedule.procedureCode,
  })) || [];

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PreAuthFormValues>({
    defaultValues: {
      authNumber: "",
      date: "",
      insurance: "",
      clinicianIds: [],
      allClinician: false,
      feeScheduleIds: [],
      allCptCodes: false,
      CPTCode: [],
      clinician: "",
      visitUsed: "",
      visitAllowed: "",
      startDate: "",
      endDate: "",
      spokeTo: "",
      dashboardWarning: false,
      providerNpi: "",
      notes: "",
      ...defaultValues,
    },
    mode: "onTouched",
  });
  return (
    <form onSubmit={handleSubmit((values) => onSubmit && onSubmit(values))}>
      <Box
        sx={{
          bgcolor: "#fff",
          p: 2,
          borderRadius: 2,
          maxWidth: 600,
          mx: "auto",
          position: "relative",
          minHeight: "fit-content",
        }}
      >
        <Grid container spacing={2} sx={{ pb: 10 }}>
          {/* Authorization Number & Requested Date in same row */}
          <Grid item xs={6}>
            <CustomLabel
              label={PreAuthFormLabels.AUTHORIZATION_NUMBER}
              isRequired
            />
            <Controller
              name="authNumber"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={
                    PreAuthFormPlaceholders.ENTER_AUTHORIZATION_NUMBER
                  }
                  hasError={!!errors.authNumber}
                  errorMessage={errors.authNumber?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.DATE} isRequired />
            <Controller
              name="date"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_DATE}
                  hasError={!!errors.date}
                  errorMessage={errors.date?.message}
                />
              )}
            />
          </Grid>
          {/* Insurance */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.INSURANCE} isRequired />
            <Controller
              name="insurance"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomSelect
                  {...field}
                  items={STATUS_OPTIONS}
                  placeholder={PreAuthFormPlaceholders.SELECT_INSURANCE}
                  hasError={!!errors.insurance}
                  errorMessage={errors.insurance?.message}
                />
              )}
            />
          </Grid>
          {/* Clinicians Multi-Select */}
          <Grid item xs={12}>
            <CustomLabel label="Select Clinicians" isRequired />
            <Controller
              name="clinicianIds"
              control={control}
              rules={{ required: "Please select at least one clinician" }}
              render={({ field }) => (
                <MultiSelectDropdown
                  name="clinicianIds"
                  options={clinicianOptions}
                  selectedValues={field.value || []}
                  setSelectedValues={field.onChange}
                  placeholder="Select Clinicians"
                  hasError={!!errors.clinicianIds}
                  errorMessage={errors.clinicianIds?.message}
                />
              )}
            />
          </Grid>

          {/* CPT Codes Multi-Select */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.CPT_CODE} isRequired />
            <Controller
              name="feeScheduleIds"
              control={control}
              rules={{ required: "Please select at least one CPT code" }}
              render={({ field }) => (
                <MultiSelectDropdown
                  name="feeScheduleIds"
                  options={feeScheduleOptions}
                  selectedValues={field.value || []}
                  setSelectedValues={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_CPT_CODES}
                  hasError={!!errors.feeScheduleIds}
                  errorMessage={errors.feeScheduleIds?.message}
                />
              )}
            />
          </Grid>
          {/* Clinician */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.CLINICIAN} isRequired />
            <Controller
              name="clinician"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomSelect
                  {...field}
                  items={SERVICE_TYPE_OPTIONS}
                  placeholder={PreAuthFormPlaceholders.SELECT_CLINICIAN}
                  hasError={!!errors.clinician}
                  errorMessage={errors.clinician?.message}
                />
              )}
            />
          </Grid>
          {/* visit used */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.VISITS_USED} isRequired />
            <Controller
              name="visitUsed"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.VISITS_USED}
                  hasError={!!errors.visitUsed}
                  errorMessage={errors.visitUsed?.message}
                />
              )}
            />
          </Grid>
          {/* Visits allowed */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.VISITS_ALLOWED} isRequired />
            <Controller
              name="visitAllowed"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.VISITS_ALLOWED}
                  hasError={!!errors.visitAllowed}
                  errorMessage={errors.visitAllowed?.message}
                />
              )}
            />
          </Grid>
          {/* Start Date */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.START_DATE} isRequired />
            <Controller
              name="startDate"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_START_DATE}
                  hasError={!!errors.startDate}
                  errorMessage={errors.startDate?.message}
                />
              )}
            />
          </Grid>
          {/* End Date */}
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.END_DATE} isRequired />
            <Controller
              name="endDate"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomDatePicker
                  value={field.value}
                  handleDateChange={field.onChange}
                  placeholder={PreAuthFormPlaceholders.SELECT_END_DATE}
                  hasError={!!errors.endDate}
                  errorMessage={errors.endDate?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <CustomLabel label={PreAuthFormLabels.SPOKE_TO} isRequired />
            <Controller
              name="clinician"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.SELECT_CLINICIAN}
                  hasError={!!errors.clinician}
                  errorMessage={errors.clinician?.message}
                />
              )}
            />
          </Grid>
          <Grid pt={6} pl={2}>
            <Controller
              name="dashboardWarning"
              control={control}
              rules={{ required: "Required" }}
              render={({ field }) => <Checkbox size="large" {...field} />}
            />
            <Typography variant="bodyRegular4">
              {PreAuthFormLabels.DASHBOARD_WARNING}
            </Typography>
          </Grid>

          {/* Notes */}
          <Grid item xs={12}>
            <CustomLabel label={PreAuthFormLabels.NOTES} />
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <CustomInput
                  {...field}
                  placeholder={PreAuthFormPlaceholders.ENTER_NOTES}
                />
              )}
            />
          </Grid>
          <Divider />
        </Grid>
      </Box>
      <Box
        sx={{
          position: "absolute",
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: "#fff",
          p: 2,
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
        }}
      >
        <CustomButton
          label="Cancel"
          variant="outline"
          onClick={() => setPreAuthDrawerOpen && setPreAuthDrawerOpen(false)}
        />
        <CustomButton
          label={isEdit ? "Update Pre-Authorization" : "Add Pre-Authorization"}
          variant="filled"
          type="submit"
        />
      </Box>
    </form>
  );
}
