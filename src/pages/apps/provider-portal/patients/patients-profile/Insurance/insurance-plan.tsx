import {
  Box,
  Typography,
  I<PERSON><PERSON><PERSON>on,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  But<PERSON>,
} from "@mui/material";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";

interface InsurancePlanCardProps {
  title: string;
  insuranceType: string;
  planName: string;
  planType: string;
  relationship: string;
  name: string;
  dob: string;
  gender: string;
  isPrimary?: boolean;
  onEdit?: () => void;
  onArchive?: () => void;
  onSetPrimary?: () => void;
}

const InsurancePlanCard = ({
  title,
  insuranceType,
  planName,
  planType,
  relationship,
  name,
  dob,
  gender,
  isPrimary = false,
  onEdit,
  onArchive,
  onSetPrimary,
}: InsurancePlanCardProps) => (
  <Box
    sx={{
      background: "#EEF7FE",
      borderRadius: 2,
      p: 0,
      mb: 2,
      display: "flex",
      flexDirection: "column",
      width: "100%",
      boxShadow: "none",
      border: "1px solid #E7E7E7",
    }}
  >
    {/* Header */}
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        px: 2,
        py: 1.5,
        borderBottom: "1px solid #E7E7E7",
      }}
    >
      {/* Set as Primary Button - only show for non-primary insurance */}
      {!isPrimary && onSetPrimary && (
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 1 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={onSetPrimary}
            sx={{
              fontSize: 12,
              fontWeight: 500,
              textTransform: "none",
              borderColor: "#007FFF",
              color: "#007FFF",
              "&:hover": {
                borderColor: "#0056CC",
                backgroundColor: "#F0F8FF",
              },
            }}
          >
            Set as Primary
          </Button>
        </Box>
      )}

      {/* Title and Action Buttons */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "#21262B",
          }}
        >
          {title}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <IconButton
            size="small"
            sx={{ color: "#373D41" }}
            onClick={onArchive}
          >
            <ArchiveOutlinedIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" sx={{ color: "#373D41" }} onClick={onEdit}>
            <EditOutlinedIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>
    </Box>
    {/* Body */}
    <Box sx={{ bgcolor: "white" }}>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60} mb={2}>
          <Box>
            <CustomLabel label="Insurance Type" />
            <Typography sx={valueStyle}>{insuranceType}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Name" />
            <Typography sx={valueStyle}>{planName}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Type" />
            <Typography sx={valueStyle}>{planType}</Typography>
          </Box>
        </Stack>
      </Box>
      <Stack direction="row" spacing={2} mb={2} sx={{ p: 2 }}>
        <Box
          sx={{
            width: 140,
            height: 90,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#BDBDBD",
            fontFamily: "Figtree",
            fontWeight: 400,
            fontSize: 14,
          }}
        >
          Insurance Card Front
        </Box>
        <Box
          sx={{
            width: 140,
            height: 90,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#BDBDBD",
            fontFamily: "Figtree",
            fontWeight: 400,
            fontSize: 14,
          }}
        >
          Insurance Card Back
        </Box>
      </Stack>
      {/* Footer */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 2,
          alignItems: "center",
          borderTop: "1px solid #E7E7E7",
          p: 2,
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "primary.main",
          }}
        >
          Patient Relationship with Insurance (Self)
        </Typography>
        <Divider orientation="vertical" flexItem />
      </Box>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60}>
          <Box>
            <CustomLabel label="Name" />
            <Typography sx={valueStyle}>{name}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Date of Birth" />
            <Typography sx={valueStyle}>{dob}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Gender" />
            <Typography sx={valueStyle}>{gender}</Typography>
          </Box>
        </Stack>
      </Box>
    </Box>
  </Box>
);

const valueStyle = {
  fontFamily: "Figtree",
  fontWeight: 400,
  fontSize: 14,
  color: "#21262B",
  mt: 0.5,
};

export default function InsurancePlan() {
  const handleSetPrimary = (insuranceId: string) => {
    console.log(`Setting insurance ${insuranceId} as primary`);
    // Add your logic here to set the insurance as primary
  };

  const handleEdit = (insuranceId: string) => {
    console.log(`Editing insurance ${insuranceId}`);
    // Add your edit logic here
  };

  const handleArchive = (insuranceId: string) => {
    console.log(`Archiving insurance ${insuranceId}`);
    // Add your archive logic here
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        width: "100%",
        height: "100%",
        p: 2,
        overflowY: "auto", // Make it scrollable
        "&::-webkit-scrollbar": {
          width: "6px",
        },
        "&::-webkit-scrollbar-thumb": {
          backgroundColor: "#D2D2D2",
          borderRadius: "3px",
        },
        "&::-webkit-scrollbar-track": {
          backgroundColor: "transparent",
        },
      }}
    >
      <InsurancePlanCard
        title="Primary Insurance"
        insuranceType="Primary"
        planName="Ins1234"
        planType="Individual"
        relationship="Self"
        name="Henna West"
        dob="8/30/24"
        gender="Male"
        isPrimary={true}
        onEdit={() => handleEdit("primary")}
        onArchive={() => handleArchive("primary")}
      />
      <InsurancePlanCard
        title="Secondary Insurance"
        insuranceType="Secondary"
        planName="Ins5678"
        planType="Family"
        relationship="Spouse"
        name="John West"
        dob="7/15/80"
        gender="Female"
        isPrimary={false}
        onEdit={() => handleEdit("secondary")}
        onArchive={() => handleArchive("secondary")}
        onSetPrimary={() => handleSetPrimary("secondary")}
      />
    </Box>
  );
}
