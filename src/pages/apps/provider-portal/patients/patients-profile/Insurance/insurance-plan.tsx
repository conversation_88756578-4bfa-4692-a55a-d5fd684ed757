import { Box, Typography, IconButton, Stack, Divider } from "@mui/material";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";

export default function InsurancePlan() {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {/* Primary Insurance Card */}
      <Box
        sx={{
          background: "#EEF7FE",
          borderRadius: 2,
          p: 2,
          mb: 2,
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography
            sx={{
              fontFamily: "Figtree",
              fontWeight: 500,
              fontSize: 16,
              color: "#21262B",
            }}
          >
            Primary Insurance
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <IconButton size="small" sx={{ color: "#373D41" }}>
            <ArchiveOutlinedIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" sx={{ color: "#373D41" }}>
            <EditOutlinedIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* Insurance Details */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          gap: 4,
          mb: 2,
        }}
      >
        <Stack spacing={3} flex={1}>
          <Box>
            <CustomLabel label="Insurance Type" />
            <Typography sx={valueStyle}>Primary</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Name" />
            <Typography sx={valueStyle}>Ins1234</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Type" />
            <Typography sx={valueStyle}>Individual</Typography>
          </Box>
        </Stack>
        <Stack spacing={3} flex={1}>
          <Box>
            <CustomLabel label="Patient Relationship with Insurance" />
            <Typography sx={valueStyle}>Self</Typography>
          </Box>
          <Box>
            <CustomLabel label="Name" />
            <Typography sx={valueStyle}>Henna West</Typography>
          </Box>
          <Box>
            <CustomLabel label="Date of Birth" />
            <Typography sx={valueStyle}>8/30/24</Typography>
          </Box>
          <Box>
            <CustomLabel label="Gender" />
            <Typography sx={valueStyle}>Male</Typography>
          </Box>
        </Stack>
        {/* Insurance Card Images (Figma: 2 images side by side) */}
        <Stack spacing={2} flex={1} alignItems="center" justifyContent="center">
          <Box
            sx={{
              width: 298,
              height: 188,
              background: "#F5F5F5",
              borderRadius: 2,
              mb: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#BDBDBD",
              fontFamily: "Figtree",
              fontWeight: 400,
              fontSize: 14,
            }}
          >
            Insurance Card Front
          </Box>
          <Box
            sx={{
              width: 298,
              height: 188,
              background: "#F5F5F5",
              borderRadius: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#BDBDBD",
              fontFamily: "Figtree",
              fontWeight: 400,
              fontSize: 14,
            }}
          >
            Insurance Card Back
          </Box>
        </Stack>
      </Box>
      <Divider sx={{ my: 2, borderColor: "#E7E7E7" }} />
      {/* Secondary Insurance Card (Figma: similar structure, static) */}
    </Box>
  );
}

const valueStyle = {
  fontFamily: "Figtree",
  fontWeight: 400,
  fontSize: 14,
  color: "#21262B",
  mt: 0.5,
};
