import { useState } from "react";
import {
  Box,
  Typography,
  IconButton,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  But<PERSON>,
} from "@mui/material";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import AddIcon from "@mui/icons-material/Add";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";
import DrawerBS from "../../../../../../common-components/drawer-bs/custom-drawer";
import AddInsuranceForm, { AddInsuranceFormValues } from "./add-insurance-form";

interface InsurancePlanCardProps {
  title: string;
  insuranceType: string;
  planName: string;
  planType: string;
  relationship: string;
  name: string;
  dob: string;
  gender: string;
  isPrimary?: boolean;
  onEdit?: () => void;
  onArchive?: () => void;
  onSetPrimary?: () => void;
}

const InsurancePlanCard = ({
  title,
  insuranceType,
  planName,
  planType,
  relationship,
  name,
  dob,
  gender,
  isPrimary = false,
  onEdit,
  onArchive,
  onSetPrimary,
}: InsurancePlanCardProps) => (
  <Box
    sx={{
      background: "#EEF7FE",
      borderRadius: 2,
      p: 0,
      mb: 2,
      display: "flex",
      flexDirection: "column",
      width: "100%",
      boxShadow: "none",
      border: "1px solid #E7E7E7",
    }}
  >
    {/* Header */}
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        px: 2,
        py: 1.5,
        borderBottom: "1px solid #E7E7E7",
      }}
    >
      <Typography
        sx={{
          fontFamily: "Figtree",
          fontWeight: 500,
          fontSize: 16,
          color: "#21262B",
        }}
      >
        {title}
      </Typography>
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* Set as Primary Button - only show for non-primary insurance */}
        {!isPrimary && onSetPrimary && (
          <Button
            variant="outlined"
            size="small"
            onClick={onSetPrimary}
            sx={{
              fontSize: 12,
              fontWeight: 500,
              textTransform: "none",
              borderColor: "#007FFF",
              color: "#007FFF",
              "&:hover": {
                borderColor: "#0056CC",
                backgroundColor: "#F0F8FF",
              },
            }}
          >
            Set as Primary
          </Button>
        )}
        <IconButton size="small" sx={{ color: "#373D41" }} onClick={onArchive}>
          <ArchiveOutlinedIcon fontSize="small" />
        </IconButton>
        <IconButton size="small" sx={{ color: "#373D41" }} onClick={onEdit}>
          <EditOutlinedIcon fontSize="small" />
        </IconButton>
      </Box>
    </Box>
    {/* Body */}
    <Box sx={{ bgcolor: "white" }}>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60} mb={2}>
          <Box>
            <CustomLabel label="Insurance Type" />
            <Typography sx={valueStyle}>{insuranceType}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Name" />
            <Typography sx={valueStyle}>{planName}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Type" />
            <Typography sx={valueStyle}>{planType}</Typography>
          </Box>
        </Stack>
      </Box>
      <Stack direction="row" spacing={2} mb={2} sx={{ p: 1 }}>
        <Box
          sx={{
            width: 300,
            height: 188,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#BDBDBD",
            fontFamily: "Figtree",
            fontWeight: 400,
            fontSize: 14,
          }}
        >
          Insurance Card Front
        </Box>
        <Box
          sx={{
            width: 300,
            height: 188,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#BDBDBD",
            fontFamily: "Figtree",
            fontWeight: 400,
            fontSize: 14,
          }}
        >
          Insurance Card Back
        </Box>
      </Stack>
      {/* Footer */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 2,
          alignItems: "center",
          borderTop: "1px solid #E7E7E7",
          p: 2,
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "primary.main",
          }}
        >
          Patient Relationship with Insurance (Self)
        </Typography>
        <Divider orientation="vertical" flexItem />
      </Box>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60}>
          <Box>
            <CustomLabel label="Name" />
            <Typography sx={valueStyle}>{name}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Date of Birth" />
            <Typography sx={valueStyle}>{dob}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Gender" />
            <Typography sx={valueStyle}>{gender}</Typography>
          </Box>
        </Stack>
      </Box>
    </Box>
  </Box>
);

const valueStyle = {
  fontFamily: "Figtree",
  fontWeight: 400,
  fontSize: 14,
  color: "#21262B",
  mt: 0.5,
};

export default function InsurancePlan() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerMode, setDrawerMode] = useState<"add" | "edit">("add");
  const [selectedInsurance, setSelectedInsurance] = useState<any>(null);

  const handleSetPrimary = (insuranceId: string) => {
    console.log(`Setting insurance ${insuranceId} as primary`);
    // Add your logic here to set the insurance as primary
  };

  const handleEdit = (insuranceId: string, insuranceData?: any) => {
    console.log(`Editing insurance ${insuranceId}`);
    setSelectedInsurance(insuranceData);
    setDrawerMode("edit");
    setDrawerOpen(true);
  };

  const handleArchive = (insuranceId: string) => {
    console.log(`Archiving insurance ${insuranceId}`);
    // Add your archive logic here
  };

  const handleAddInsurance = () => {
    setSelectedInsurance(null);
    setDrawerMode("add");
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedInsurance(null);
  };

  const handleFormSubmit = (values: AddInsuranceFormValues) => {
    console.log("Form submitted:", values);
    // Add your form submission logic here
    handleCloseDrawer();
  };

  return (
    <>
      {/* Insurance Cards Container */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 3,
          width: "100%",
          maxHeight: "calc(100vh - 300px)", // Set a specific max height
          p: 2,
          overflowY: "auto", // Make it scrollable
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#D2D2D2",
            borderRadius: "3px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "transparent",
          },
        }}
      >
        <InsurancePlanCard
          title="Primary Insurance"
          insuranceType="Primary"
          planName="Ins1234"
          planType="Individual"
          relationship="Self"
          name="Henna West"
          dob="8/30/24"
          gender="Male"
          isPrimary={true}
          onEdit={() =>
            handleEdit("primary", {
              insuranceType: "Primary",
              planName: "Ins1234",
              planType: "Individual",
              relationship: "Self",
              name: "Henna West",
              dob: "8/30/24",
              gender: "Male",
            })
          }
          onArchive={() => handleArchive("primary")}
        />
        <InsurancePlanCard
          title="Secondary Insurance"
          insuranceType="Secondary"
          planName="Ins5678"
          planType="Family"
          relationship="Spouse"
          name="John West"
          dob="7/15/80"
          gender="Female"
          isPrimary={false}
          onEdit={() =>
            handleEdit("secondary", {
              insuranceType: "Secondary",
              planName: "Ins5678",
              planType: "Family",
              relationship: "Spouse",
              name: "John West",
              dob: "7/15/80",
              gender: "Female",
            })
          }
          onArchive={() => handleArchive("secondary")}
          onSetPrimary={() => handleSetPrimary("secondary")}
        />
        <InsurancePlanCard
          title="Tertiary Insurance"
          insuranceType="Tertiary"
          planName="Ins9999"
          planType="Group"
          relationship="Child"
          name="Jane West"
          dob="5/10/85"
          gender="Female"
          isPrimary={false}
          onEdit={() =>
            handleEdit("tertiary", {
              insuranceType: "Tertiary",
              planName: "Ins9999",
              planType: "Group",
              relationship: "Child",
              name: "Jane West",
              dob: "5/10/85",
              gender: "Female",
            })
          }
          onArchive={() => handleArchive("tertiary")}
          onSetPrimary={() => handleSetPrimary("tertiary")}
        />
      </Box>

      {/* Drawer for Add/Edit Insurance */}
      <DrawerBS
        anchor="right"
        open={drawerOpen}
        onClose={handleCloseDrawer}
        title={drawerMode === "edit" ? "Edit Insurance" : "Add Insurance"}
        drawerWidth="40vw"
      >
        <AddInsuranceForm
          defaultValues={selectedInsurance}
          onSubmit={handleFormSubmit}
          isEdit={drawerMode === "edit"}
        />
      </DrawerBS>
    </>
  );
}
