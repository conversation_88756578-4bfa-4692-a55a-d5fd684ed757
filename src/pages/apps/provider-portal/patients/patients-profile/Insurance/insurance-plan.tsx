import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Typography,
  IconButton,
  Stack,
  Divider,
  But<PERSON>,
} from "@mui/material";
import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import AddIcon from "@mui/icons-material/Add";
import CustomLabel from "../../../../../../common-components/customLabel/customLabel";
import DrawerBS from "../../../../../../common-components/drawer-bs/custom-drawer";
import AddInsuranceForm, { AddInsuranceFormValues } from "./add-insurance-form";
import { AppDispatch, RootState } from "../../../../../../redux/store";
import {
  getClientInsurance,
  InsuranceData,
} from "../../../../../../redux/auth/patient/get-client-insurance-reducer";
import { apiStatus } from "../../../../../../models/apiStatus";
import { getPatientByIdState } from "src/redux/auth/patient/get-patient-by-id-reducer";

interface InsurancePlanCardProps {
  title: string;
  insuranceType: string;
  planName: string;
  planType: string;
  relationship: string;
  name: string;
  dob: string;
  gender: string;
  isPrimary?: boolean;
  onEdit?: () => void;
  onArchive?: () => void;
  onSetPrimary?: () => void;
  insuranceCardFront?: string | null;
  insuranceCardBack?: string | null;
}

const InsurancePlanCard = ({
  title,
  insuranceType,
  planName,
  planType,
  relationship,
  name,
  dob,
  gender,
  isPrimary = false,
  onEdit,
  onArchive,
  onSetPrimary,
  insuranceCardFront,
  insuranceCardBack,
}: InsurancePlanCardProps) => (
  <Box
    sx={{
      background: "#EEF7FE",
      borderRadius: 2,
      p: 0,
      mb: 2,
      display: "flex",
      flexDirection: "column",
      width: "100%",
      boxShadow: "none",
      border: "1px solid #E7E7E7",
    }}
  >
    {/* Header */}
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        px: 2,
        py: 1.5,
        borderBottom: "1px solid #E7E7E7",
      }}
    >
      <Typography
        sx={{
          fontFamily: "Figtree",
          fontWeight: 500,
          fontSize: 16,
          color: "#21262B",
        }}
      >
        {title}
      </Typography>
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {/* Set as Primary Button - only show for non-primary insurance */}
        {!isPrimary && onSetPrimary && (
          <Button
            variant="outlined"
            size="small"
            onClick={onSetPrimary}
            sx={{
              fontSize: 12,
              fontWeight: 500,
              textTransform: "none",
              borderColor: "#007FFF",
              color: "#007FFF",
              "&:hover": {
                borderColor: "#0056CC",
                backgroundColor: "#F0F8FF",
              },
            }}
          >
            Set as Primary
          </Button>
        )}
        <IconButton size="small" sx={{ color: "#373D41" }} onClick={onArchive}>
          <ArchiveOutlinedIcon fontSize="small" />
        </IconButton>
        <IconButton size="small" sx={{ color: "#373D41" }} onClick={onEdit}>
          <EditOutlinedIcon fontSize="small" />
        </IconButton>
      </Box>
    </Box>
    {/* Body */}
    <Box sx={{ bgcolor: "white" }}>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60} mb={2}>
          <Box>
            <CustomLabel label="Insurance Type" />
            <Typography sx={valueStyle}>{insuranceType}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Name" />
            <Typography sx={valueStyle}>{planName}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Plan Type" />
            <Typography sx={valueStyle}>{planType}</Typography>
          </Box>
        </Stack>
      </Box>
      <Stack direction="row" spacing={2} mb={2} sx={{ p: 1 }}>
        <Box
          sx={{
            width: 300,
            height: 188,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            overflow: "hidden",
          }}
        >
          {insuranceCardFront ? (
            <img
              src={insuranceCardFront}
              alt="Insurance Card Front"
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          ) : (
            <Typography
              sx={{
                color: "#BDBDBD",
                fontFamily: "Figtree",
                fontWeight: 400,
                fontSize: 14,
              }}
            >
              Insurance Card Front
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            width: 300,
            height: 188,
            background: "#F5F5F5",
            borderRadius: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            overflow: "hidden",
          }}
        >
          {insuranceCardBack ? (
            <img
              src={insuranceCardBack}
              alt="Insurance Card Back"
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          ) : (
            <Typography
              sx={{
                color: "#BDBDBD",
                fontFamily: "Figtree",
                fontWeight: 400,
                fontSize: 14,
              }}
            >
              Insurance Card Back
            </Typography>
          )}
        </Box>
      </Stack>
      {/* Footer */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 2,
          alignItems: "center",
          borderTop: "1px solid #E7E7E7",
          p: 2,
        }}
      >
        <Typography
          sx={{
            fontFamily: "Figtree",
            fontWeight: 500,
            fontSize: 16,
            color: "primary.main",
          }}
        >
          Patient Relationship with Insurance (Self)
        </Typography>
        <Divider orientation="vertical" flexItem />
      </Box>
      <Box sx={{ p: 2 }}>
        <Stack direction="row" spacing={60}>
          <Box>
            <CustomLabel label="Name" />
            <Typography sx={valueStyle}>{name}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Date of Birth" />
            <Typography sx={valueStyle}>{dob}</Typography>
          </Box>
          <Box>
            <CustomLabel label="Gender" />
            <Typography sx={valueStyle}>{gender}</Typography>
          </Box>
        </Stack>
      </Box>
    </Box>
  </Box>
);

const valueStyle = {
  fontFamily: "Figtree",
  fontWeight: 400,
  fontSize: 14,
  color: "#21262B",
  mt: 0.5,
};

export default function InsurancePlan() {
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerMode, setDrawerMode] = useState<"add" | "edit">("add");
  const [selectedInsurance, setSelectedInsurance] = useState<any>(null);

  // Get clientId from location state (passed from patient profile)
  const clientId = location.state;

  // Get insurance data from Redux store
  const {
    data: insuranceData,
    status: insuranceStatus,
    error: insuranceError,
  } = useSelector((state: RootState) => state.GetClientInsuranceReducer);

  const {
    data: getPatientByIdData,
    status: getPatientByIdStatus,
    error: getPatientByIdError,
  }: getPatientByIdState = useSelector(
    (state: RootState) => state.GetPatientByIdReducer
  ) || {};

  console.log("getPatientByIdData", getPatientByIdData);

  // Fetch insurance data when component mounts or clientId changes
  useEffect(() => {
    if (getPatientByIdData?.uuid) {
      dispatch(getClientInsurance(getPatientByIdData?.uuid));
    }
  }, [dispatch, getPatientByIdData]);

  const handleSetPrimary = (insuranceId: string) => {
    console.log(`Setting insurance ${insuranceId} as primary`);
    // Add your logic here to set the insurance as primary
  };

  const handleEdit = (insuranceId: string, insuranceData?: any) => {
    console.log(`Editing insurance ${insuranceId}`);
    setSelectedInsurance(insuranceData);
    setDrawerMode("edit");
    setDrawerOpen(true);
  };

  const handleArchive = (insuranceId: string) => {
    console.log(`Archiving insurance ${insuranceId}`);
    // Add your archive logic here
  };

  const handleAddInsurance = () => {
    setSelectedInsurance(null);
    setDrawerMode("add");
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedInsurance(null);
  };

  const handleFormSubmit = (values: AddInsuranceFormValues) => {
    console.log("Form submitted:", values);
    // Add your form submission logic here
    // After successful submission, refetch the insurance data
    if (clientId) {
      dispatch(getClientInsurance(clientId));
    }
    handleCloseDrawer();
  };

  // Show loading state
  if (insuranceStatus === apiStatus.LOADING) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography>Loading insurance data...</Typography>
      </Box>
    );
  }

  // Show error state
  if (insuranceStatus === apiStatus.FAILED) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography color="error">
          Error loading insurance data: {insuranceError}
        </Typography>
        <Button
          onClick={() => clientId && dispatch(getClientInsurance(clientId))}
          sx={{ mt: 2 }}
        >
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <>
      {/* Insurance Cards Container */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 3,
          width: "100%",
          maxHeight: "calc(100vh - 300px)", // Set a specific max height
          p: 2,
          overflowY: "auto", // Make it scrollable
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#D2D2D2",
            borderRadius: "3px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "transparent",
          },
        }}
      >
        {/* Render insurance cards from API data */}
        {insuranceData && insuranceData.length > 0 ? (
          insuranceData.map((insurance: any, index: number) => {
            const isPrimary = insurance.insuranceType === "PRIMARY";
            const cardTitle = isPrimary
              ? "Primary Insurance"
              : insurance.insuranceType === "SECONDARY"
                ? "Secondary Insurance"
                : insurance.insuranceType === "TERTIARY"
                  ? "Tertiary Insurance"
                  : `${insurance.insuranceType} Insurance`;
            return (
              <InsurancePlanCard
                key={insurance.uuid || index}
                title={cardTitle}
                insuranceType={insurance.insuranceType}
                planName={insurance.insuranceName}
                planType={insurance.groupId || ""}
                relationship={insurance.relationship}
                name={`${insurance.subscriberFirstName || ""} ${insurance.subscriberLastName || ""}`.trim()}
                dob={insurance.subscriberBirthDate}
                gender={"-"}
                isPrimary={isPrimary}
                onEdit={() =>
                  handleEdit(insurance.uuid || `insurance-${index}`, insurance)
                }
                onArchive={() =>
                  handleArchive(insurance.uuid || `insurance-${index}`)
                }
                onSetPrimary={
                  !isPrimary
                    ? () =>
                        handleSetPrimary(insurance.uuid || `insurance-${index}`)
                    : undefined
                }
                insuranceCardFront={insurance.insuranceCardFront}
                insuranceCardBack={insurance.insuranceCardBack}
              />
            );
          })
        ) : (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              No insurance plans found for this patient.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Click "Add Insurance" to add the first insurance plan.
            </Typography>
          </Box>
        )}
      </Box>

      {/* Drawer for Add/Edit Insurance */}
      <DrawerBS
        anchor="right"
        open={drawerOpen}
        onClose={handleCloseDrawer}
        title={drawerMode === "edit" ? "Edit Insurance" : "Add Insurance"}
        drawerWidth="60vw"
      >
        <AddInsuranceForm
          defaultValues={selectedInsurance}
          onSubmit={handleFormSubmit}
          isEdit={drawerMode === "edit"}
        />
      </DrawerBS>
    </>
  );
}
