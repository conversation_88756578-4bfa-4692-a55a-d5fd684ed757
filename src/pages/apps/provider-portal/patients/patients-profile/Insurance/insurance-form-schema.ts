import {
  MinMaxFieldLimits,
  RequiredFieldLabels,
} from "../../../../../../constants/formConst";
import * as yup from "yup";

export const addInsuranceValidationSchema = yup.object().shape({
  insuranceType: yup
    .string()
    .required(RequiredFieldLabels.INSURANCE_TYPE)
    .oneOf(["primary", "secondary"], "Invalid insurance type"),

  insuranceName: yup
    .string()
    .required(RequiredFieldLabels.INSURANCE_NAME)
    .trim()
    .min(2, MinMaxFieldLimits.INSURANCE_NAME_MIN),

  memberId: yup
    .string()
    .required(RequiredFieldLabels.MEMBER_ID)
    .trim()
    .min(1, MinMaxFieldLimits.MEMBER_ID_MIN)
    .max(50, MinMaxFieldLimits.MEMBER_ID_MAX),

  relationship: yup
    .string()
    .required(RequiredFieldLabels.RELATIONSHIP)
    .oneOf(
      ["self", "spouse", "child", "other"],
      "Please select a valid relationship"
    ),

  firstName: yup
    .string()
    .required(RequiredFieldLabels.FIRST_NAME)
    .trim()
    .min(2, MinMaxFieldLimits.FIRST_NAME_MIN)
    .max(50, MinMaxFieldLimits.FIRST_NAME_MAX)
    .matches(
      /^[a-zA-Z\s'-]+$/,
      "First name can only contain letters, spaces, hyphens, and apostrophes"
    ),

  lastName: yup
    .string()
    .required(RequiredFieldLabels.LAST_NAME)
    .trim()
    .min(2, MinMaxFieldLimits.LAST_NAME_MIN)
    .max(50, MinMaxFieldLimits.LAST_NAME_MAX)
    .matches(
      /^[a-zA-Z\s'-]+$/,
      "Last name can only contain letters, spaces, hyphens, and apostrophes"
    ),

  dateOfBirth: yup
    .string()
    .required(RequiredFieldLabels.DATE_OF_BIRTH)
    .test("valid-date", "Please enter a valid date", (value) => {
      if (!value) return false;
      const date = new Date(value);
      return !isNaN(date.getTime());
    })
    .test("not-future", "Date of birth cannot be in the future", (value) => {
      if (!value) return true;
      const date = new Date(value);
      return date <= new Date();
    })
    .test("reasonable-age", "Please enter a reasonable birth date", (value) => {
      if (!value) return true;
      const date = new Date(value);
      const now = new Date();
      const age = now.getFullYear() - date.getFullYear();
      return age >= 0 && age <= 150;
    }),
});
