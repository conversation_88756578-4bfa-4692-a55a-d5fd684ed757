import { Box, Paper } from "@mui/material";
import CustomisedTable from "../../../../../../common-components/table/table";

const rows = [
  {
    billingClinician: "Practice Easily",
    dateOfRequest: "8/16/13, 10:45 AM",
    requestedBy: "<PERSON>",
    appointmentType: "Intake",
    action: "View VOB",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    billingClinician: "Practice Easily",
    dateOfRequest: "8/2/19, 01:22 PM",
    requestedBy: "<PERSON>",
    appointmentType: "Follow Up",
    action: "View VOB",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    billingClinician: "Practice Easily",
    dateOfRequest: "7/18/17, 07:30 AM",
    requestedBy: "<PERSON>",
    appointmentType: "Treatment Plan",
    action: "View VOB",
    actionMenu: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

const headCells = [
  { id: "billingClinician", label: "Billing Clinician" },
  { id: "dateOfRequest", label: "Date of Request" },
  { id: "requestedBy", label: "Requested By" },
  { id: "appointmentType", label: "Appointment Type" },
  { id: "action", label: "Action", type: "link" },
  { id: "actionMenu", label: "", type: "action" },
];

export default function InsuranceVerification() {
  return (
    <Box sx={{ width: "100%", p: 0 }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: "1px solid #E7E7E7",
          overflow: "hidden",
          background: "#fff",
        }}
      >
        <CustomisedTable
          headCells={headCells}
          tableData={rows}
          removeRadius
          setHeight="auto"
        />
      </Paper>
    </Box>
  );
}
