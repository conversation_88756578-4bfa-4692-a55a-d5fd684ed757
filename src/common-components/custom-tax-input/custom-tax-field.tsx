import { Box, InputBase, Typography } from "@mui/material";
import { ChangeEvent, useEffect, useState } from "react";

interface CustomTaxInputProps {
  placeholder?: string;
  name?: string;
  value?: string | null | undefined;
  hasError?: boolean;
  errorMessage?: string | undefined;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  disableField?: boolean;
  bgWhite?: boolean;
}

const formatTaxNumber = (value: string): string => {
  const cleanedValue = value.replace(/\D/g, "");

  const limitedValue = cleanedValue.slice(0, 9);

  if (limitedValue.length >= 6) {
    return `${limitedValue.slice(0, 3)}-${limitedValue.slice(3, 5)}-${limitedValue.slice(5)}`;
  } else if (limitedValue.length >= 4) {
    return `${limitedValue.slice(0, 3)}-${limitedValue.slice(3)}`;
  } else {
    return limitedValue;
  }
};

const customTaxFieldStyles = {
  "& input": {
    color: "black",
    fontSize: "14px",
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: "120%",
    letterSpacing: "0.024px",
    padding: "11.5px 14px",
    borderRadius: "4px",
    border: "1px solid #E0E0E0",
  },
  "& input::placeholder": {
    color: "#BCBCBC",
    fontSize: "14px",
    fontWeight: 400,
  },
  "& .MuiInputBase-root": {
    height: "40px",
    borderRadius: "4px",
    border: "1px solid #E0E0E0",
    "&:hover": {
      borderColor: "#B0B0B0",
    },
    "&.Mui-focused": {
      borderColor: "#1976d2",
      boxShadow: "0 0 0 2px rgba(25, 118, 210, 0.2)",
    },
    "&.Mui-error": {
      borderColor: "#f44336",
    },
  },
};

const errorStyle = {
  color: "#f44336",
  fontSize: "0.75rem",
  marginTop: "4px",
  fontFamily: "Figtree",
};

export default function CustomTaxInput(props: CustomTaxInputProps) {
  const { bgWhite } = props;
  const [inputValue, setInputValue] = useState(
    props.value ? formatTaxNumber(String(props.value)) : ""
  );

  useEffect(() => {
    const formattedValue = props.value
      ? formatTaxNumber(String(props.value))
      : "";
    setInputValue(formattedValue);
  }, [props.value]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const formattedValue = formatTaxNumber(rawValue);

    setInputValue(formattedValue);

    const syntheticEvent = {
      ...e,
      target: {
        ...e.target,
        value: formattedValue,
        dataset: {
          cleanValue: formattedValue.replace(/\D/g, ""),
        },
      },
    };

    props.onChange &&
      props.onChange(syntheticEvent as ChangeEvent<HTMLInputElement>);
  };

  return (
    <Box>
      <InputBase
        fullWidth
        autoComplete="off"
        name={props.name}
        type="text"
        placeholder={props.placeholder || "XXX-XX-XXXX"}
        value={inputValue}
        onChange={handleInputChange}
        error={props.hasError}
        disabled={props.disableField}
        inputMode="numeric"
        sx={{
          ...customTaxFieldStyles,
          background: bgWhite ? "white" : "inherit",
        }}
        inputProps={{
          maxLength: 11, // XXX-XX-XXXX = 11 characters including dashes
        }}
      />
      {props.hasError && (
        <Typography sx={errorStyle} variant="body2">
          {props.errorMessage}
        </Typography>
      )}
    </Box>
  );
}
