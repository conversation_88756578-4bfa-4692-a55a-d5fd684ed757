import MuiPhoneNumber from "material-ui-phone-number";
import { customcontactFieldStyles } from "./widgets/customContactfieldStyles";

interface CustomContactInputProps {
  value?: string | number | undefined | null;
  hasError?: boolean;
  errorMessage?: string | unknown;
  isDisabled?: boolean;
  onChange(selectedValue?: string): void;
}

const CHARS_TO_EXCLUDE = ["(", ")", " ", "-"];

export default function CustomContactInputNew(props: CustomContactInputProps) {
  const { value, hasError, errorMessage, isDisabled, onChange } = props;

  const handleChange = (
    event: string | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const filteredVal = Array.from(event as string)
      .filter((char) => !CHARS_TO_EXCLUDE.includes(char))
      .join("");
    onChange(filteredVal);
  };

  return (
    <>
      <MuiPhoneNumber
        defaultCountry={"us"}
        variant="outlined"
        size="small"
        fullWidth
        value={value}
        disabled={isDisabled}
        disableDropdown={isDisabled}
        onChange={handleChange}
        error={Boolean(hasError)}
        helperText={errorMessage ? String(errorMessage) : ""}
        onlyCountries={["us", "in"]}
        sx={customcontactFieldStyles}
      />
    </>
  );
}
