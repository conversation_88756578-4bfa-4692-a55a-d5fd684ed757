export const loaderContainer = {
  position: "fixed",
  top: "0",
  left: "0",
  width: "100%",
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  zIndex: 99999,
  WebkitUserSelect: "none",
  KhtmlUserSelect: "none",
  MozUserSelect: "none",
  OUserSelect: "none",
  userSelect: "none"
};

export const circularProgress = {
  width: 90,
  height: 90,
  position: "relative",
  color: "#7A7CE9"
};

export const circularProgress1 = {
  width: 90,
  height: 90,
  position: "absolute",
  scale: "-1 1"
};
