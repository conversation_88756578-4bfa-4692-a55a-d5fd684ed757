declare global {
  interface Window {
    MSStream?: object;
  }
}

import MuiPhoneNumber from "material-ui-phone-number";
import { Typography } from "@mui/material";

const customcontactFieldStyles = {
  "& input": {
    color: "black",
    fontSize: "14px",
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: "120%",
    letterSpacing: "0.024px",
    padding: "11.5px 14px",
    fontFamily: "Figtree",
  },
  "& .Mui-error": {
    margin: 0,
    fontWeight: 400,
    fontSize: "0.75rem",
    lineHeight: 1.66,
    color: "red",
    marginRight: "14px",
    marginBottom: "0px",
    marginLeft: "0px",
  },
};

interface CustomContactInputProps {
  value?: string | number;
  hasError?: boolean;
  errorMessage?: string | unknown;
  isDisabled?: boolean;
  onChange(selectedValue?: string): void;
  disableFlag?: boolean;
}

const formatPhoneNumber = (nationalNumber: string): string => {
  const cleanedNational = nationalNumber.replace(/\D/g, "");

  if (cleanedNational.length >= 10) {
    return `(${cleanedNational.slice(0, 3)})-${cleanedNational.slice(3, 6)}-${cleanedNational.slice(6, 10)}`;
  }
  return cleanedNational;
};

export default function CustomContactInput(props: CustomContactInputProps) {
  const { value, hasError, errorMessage, isDisabled, onChange } = props;

  const handleChange = (
    event: string | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const rawValue = typeof event === "string" ? event : event.target.value;

    const cleanedDigits = rawValue.replace(/\D/g, "");

    let nationalNumber = "";
    if (cleanedDigits.startsWith("1") && cleanedDigits.length >= 11) {
      nationalNumber = cleanedDigits.substring(1);
    } else if (cleanedDigits.startsWith("91") && cleanedDigits.length >= 12) {
      nationalNumber = cleanedDigits.substring(2);
    } else {
      nationalNumber = cleanedDigits;
    }

    onChange(formatPhoneNumber(nationalNumber));
  };

  const isIOS =
    (/iPad|iPhone|iPod/.test(navigator.platform) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)) &&
    !window.MSStream;

  return (
    <>
      <MuiPhoneNumber
        defaultCountry={"us"}
        variant="outlined"
        size="small"
        fullWidth
        value={value}
        disabled={isDisabled}
        countryCodeEditable={false}
        disableDropdown={isIOS ? true : isDisabled}
        onChange={handleChange}
        error={Boolean(hasError)}
        onlyCountries={["us", "in"]}
        sx={{
          ...customcontactFieldStyles,
          "& .MuiPhoneNumber-flagButton": {
            pointerEvents: "none",
          },
          "& .Mui-error": {
            color: "red",
          },
          "& .MuiInputBase-root": {
            height: "40px",
          },
        }}
      />
      {hasError && (
        <Typography
          variant="caption"
          sx={{
            color: "red",
            marginLeft: "14px",
            marginTop: "4px",
            display: "block",
          }}
        >
          {String(errorMessage)}
        </Typography>
      )}
    </>
  );
}
