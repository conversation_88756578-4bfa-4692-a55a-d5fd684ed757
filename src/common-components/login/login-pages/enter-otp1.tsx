import { yupResolver } from "@hookform/resolvers/yup";
import { Grid, Typography, useMediaQuery } from "@mui/material";
import { MuiOtpInput } from "mui-one-time-password-input";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { loginConstants } from "../../../constants/common-component";
import { apiStatus } from "../../../models/apiStatus";
import { loaderAction } from "../../../redux/auth/loaderReducer";
import {
  sendVerificationOtp,
  sendVerificationOtpAction,
} from "../../../redux/auth/send-verification-otp-reducer";
import {
  verifyUserOtp,
  verifyUserOtpAction,
} from "../../../redux/auth/verify-user-otp.reducer";
import { AppDispatch, RootState } from "../../../redux/store";
import CustomButton from "../../custom-button/custom-button";
import { EnterOtpSchema } from "./login-pages-schema/login-pages-schema";
import { snackbarAction } from "../../../redux/auth/snackbarReducer";
import { AlertSeverity } from "../../../common-components/snackbar-alert/snackbar-alert";
import { getEmailByUuid } from "../../../redux/auth/get-user-by-uuid";

interface ResendOtpState {
  data: string | null;
  status: string;
  error: string | null;
}

interface EmailByUuidState {
  data: Record<string, string> | null;
  status: string;
  error: string | null;
}

interface VerifyUserOtpState {
  status: string;
  error: string | null;
}

const StyledOtpInput = styled.div`
  & .MuiInputBase-root {
    border-radius: 15px;
  }
  & .MuiInputBase-input {
    border-radius: 15px;
    font-size: 20px;
  }
  & .MuiOutlinedInput-notchedOutline {
    border-radius: 15px;
  }
`;

const StyledTypography = styled(Typography)<{ disabled?: boolean }>`
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};
`;

interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  length: number;
  gap: number;
  maxWidth: string;
}

const OtpInput = ({
  value,
  onChange,
  length,
  gap,
  maxWidth,
}: OtpInputProps) => {
  return (
    <StyledOtpInput>
      <MuiOtpInput
        value={value}
        onChange={onChange}
        length={length}
        gap={gap}
        maxWidth={maxWidth}
      />
    </StyledOtpInput>
  );
};

interface EnterOtpForm {
  otp: string;
}

const EnterOtp = () => {
  const [otp, setOtp] = React.useState("");
  const [countdown, setCountdown] = React.useState(60);
  const [isResendClicked, setIsResendClicked] = React.useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const location = useLocation();
  const { email } = location.state || {};
  const [isNavigated, setIsNavigated] = React.useState(
    location?.state?.isNavigated
  );

  const urlParams = new URLSearchParams(location.search);
  const uuid = urlParams.get("uuid");

  const showResend = location?.state?.isNavigated;

  const {
    data: resendOtpData,
    status: resendOtpStatus,
    error: resendOtpError,
  }: ResendOtpState = useSelector(
    (state: RootState) => state.SendVerifiationOtpReducer
  );

  const { data: emailByUuidData }: EmailByUuidState = useSelector(
    (state: RootState) => state.getEmailByUuidReducer
  );

  const {
    status: VerifyUserOtpStatus,
    error: VerifyUserOtpError,
  }: VerifyUserOtpState = useSelector(
    (state: RootState) => state.VerifyUserOtpReducer
  );

  const handleResendClick = () => {
    dispatch(sendVerificationOtp({ email: email || "" }));
    setCountdown(180);
    setIsResendClicked(true);
  };

  const isMobile = useMediaQuery("(max-width:702px)");

  const { handleSubmit, control } = useForm<EnterOtpForm>({
    resolver: yupResolver(EnterOtpSchema),
  });

  const handleChange = (newValue: string) => {
    setOtp(newValue);
  };
  const navigate = useNavigate();
  const currentOtpId = isNavigated
    ? location?.state?.otpData
    : isResendClicked
      ? resendOtpData
      : uuid;

  const onSubmit = () => {
    dispatch(
      verifyUserOtp({
        otpId: currentOtpId,
        otp: otp,
        fromForgotPassword: isNavigated || isResendClicked ? true : false,
      })
    );
  };

  useEffect(() => {
    dispatch(getEmailByUuid({ uuid: currentOtpId }));
  }, [currentOtpId, dispatch]);

  useEffect(() => {
    if (isResendClicked) {
      setIsNavigated(false);
    }
  }, [isResendClicked]);

  useEffect(() => {
    switch (resendOtpStatus) {
      case apiStatus.LOADING:
        if (isResendClicked) {
          dispatch(loaderAction.showLoader());
        }
        break;
      case apiStatus.SUCCEEDED:
        if (isResendClicked) {
          dispatch(loaderAction.hideLoader());
          dispatch(
            snackbarAction.showSnackbarAction({
              severity: AlertSeverity.SUCCESS,
              message: "OTP Resent Successfully",
            })
          );
          dispatch(sendVerificationOtpAction.resetValues());
        }
        break;
      case apiStatus.FAILED:
        if (isResendClicked) {
          dispatch(loaderAction.hideLoader());
          dispatch(
            snackbarAction.showSnackbarAction({
              severity: AlertSeverity.ERROR,
              message: resendOtpError || "Failed to resend OTP",
            })
          );
          dispatch(sendVerificationOtpAction.resetValues());
        }
        break;
    }
  }, [resendOtpStatus, isResendClicked, dispatch, resendOtpError]);

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prevCountdown) =>
        prevCountdown > 0 ? prevCountdown - 1 : 0
      );
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    switch (VerifyUserOtpStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.SUCCESS,
            message: "OTP Verified Successfully",
          })
        );
        dispatch(verifyUserOtpAction.resetValues());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.ERROR,
            message: VerifyUserOtpError || "Failed to verify OTP",
          })
        );
        dispatch(verifyUserOtpAction.resetValues());
        break;
    }
  }, [VerifyUserOtpStatus, dispatch, VerifyUserOtpError]);

  useEffect(() => {
    switch (VerifyUserOtpStatus) {
      case "succeeded":
        navigate("../set-password", {
          state: {
            currentOtpId: currentOtpId,
            isForgotPassword: true,
            isForgotPasswordEmail: email,
            emailForNewLogin: emailByUuidData || "NewLogin",
          },
        });
        break;
      default:
        break;
    }
  }, [VerifyUserOtpStatus]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        item
        xs={12}
        justifyContent={"center"}
        mb={13}
        width={"28vw"}
        ml={9}
      >
        <Grid display={"flex"} flexDirection={"column"} mb={3}>
          <Grid>
            <Typography variant="titleMediumBold">
              {loginConstants.OTP_VERIFICATION}
            </Typography>
          </Grid>
          <Grid>
            <Typography variant="titleSmallRegular" color={"#74797B"}>
              {loginConstants.CHECK_CODE}
            </Typography>
            <Typography variant="titleSmallRegularBolder" color={"#74797B"}>
              {emailByUuidData as unknown as string}
            </Typography>
          </Grid>
        </Grid>
        <Grid mb={1}>
          <Typography variant="titleSmallMiniBolder">
            {loginConstants.ENTER_CODE}
          </Typography>
        </Grid>
        <Grid width={isMobile ? "65vw" : "25vw"} mb={5}>
          <Controller
            name="otp"
            control={control}
            render={({ field }) => (
              <OtpInput
                value={field.value}
                onChange={(newValue) => {
                  field.onChange(newValue);
                  handleChange(newValue);
                }}
                length={6}
                gap={1}
                maxWidth={isMobile ? "80vw" : "20vw"}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} mt={2}>
          <CustomButton
            label={loginConstants.VERIFY_OTP}
            variant="filled"
            fullWidth
            type="submit"
          />
        </Grid>
        {showResend && (
          <Grid display={"flex"} flexDirection={"column"} mb={4} mt={1}>
            <Grid>
              <Typography variant="titleSmallMedium" color={"#74797B"}>
                {loginConstants.DIDNT_RECIEVER_CODE}
              </Typography>
              <StyledTypography
                variant="titleSmallMedium"
                color={countdown === 0 ? "#0068FF" : "#74797B"}
                onClick={countdown === 0 ? handleResendClick : undefined}
                disabled={countdown > 0}
              >
                {loginConstants.RESEND}
              </StyledTypography>
              <Typography variant="titleSmallRegularBolder" color={"#74797B"}>
                ({countdown}s)
              </Typography>
            </Grid>
          </Grid>
        )}
      </Grid>
    </form>
  );
};

export default EnterOtp;
