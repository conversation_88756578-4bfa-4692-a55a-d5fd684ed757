import { yupResolver } from "@hookform/resolvers/yup";
import { Grid, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import CustomButton from "../../custom-button/custom-button";
import { loginConstants } from "../../../constants/common-component";
import { apiStatus } from "../../../models/apiStatus";
import CustomInput from "../../custom-input/customInput";
import CustomLabel from "../../customLabel/customLabel";
import { ForgotPasswordSchema } from "./login-pages-schema/login-pages-schema";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import { loaderAction } from "../../../redux/auth/loaderReducer";
import { sendVerificationOtp, sendVerificationOtpAction } from "../../../redux/auth/send-verification-otp-reducer";
import { AlertSeverity } from "../../../common-components/alert/alert";
import { snackbarAction } from "../../../redux/auth/snackbarReducer";

interface ForgotPasswordFormData {
  email: string;
}

interface SendVerificationOtpState {
  data: string | null;
  status: string;
  error: string | { message: string } | null;
}

const getErrorMessage = (
  error: string | { message: string } | null
): string => {
  if (!error) return "An error occurred";
  if (typeof error === "string") return error;
  return error.message || "An error occurred";
};

const ForgotPassword = () => {
  const {
    control,
    formState: { errors },
    setValue,
    handleSubmit,
    trigger,
    clearErrors,
  } = useForm({
    resolver: yupResolver(ForgotPasswordSchema),
  });

  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [emailToNavigate, setEmailToNavigate] = useState<string | null>(null);

  const {
    data: sendVerificationOtpData,
    status: sendVerificationOtpStatus,
    error: sendVerificationOtpError,
  }: SendVerificationOtpState = useSelector(
    (state: RootState) => state.SendVerifiationOtpReducer
  );

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    setValue("email", value);

    trigger("email");
    if (value) {
      clearErrors("email");
    }
  };

  const onSubmit = (data: ForgotPasswordFormData) => {
    setEmailToNavigate(data?.email || "");
    dispatch(sendVerificationOtp({ email: data?.email || "" }));
  };

  useEffect(() => {
    switch (sendVerificationOtpStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.SUCCESS,
            message: "Verification Email Sent Successfully",
          })
        );
        navigate("../enter-otp", {
          state: {
            email: emailToNavigate,
            otpData: sendVerificationOtpData,
            isNavigated: true,
          },
        });
        dispatch(sendVerificationOtpAction.resetValues());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.ERROR,
            message: getErrorMessage(sendVerificationOtpError),
          })
        );
        dispatch(sendVerificationOtpAction.resetValues());
        break;
    }
  }, [
    sendVerificationOtpStatus,
    sendVerificationOtpError,
    emailToNavigate,
    dispatch,
    navigate,
    sendVerificationOtpData,
  ]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        item
        xs={12}
        justifyContent={"center"}
        mb={12}
        width={"29vw"}
        ml={9}
      >
        <Grid display={"flex"} flexDirection={"column"} mb={4} width={"29vw"}>
          <Grid mb={1}>
            <Typography variant="titleMediumBold">
              {loginConstants.FORGOT_PASSWORD}
            </Typography>
          </Grid>
          <Grid>
            <Typography variant="titleSmallRegular" color={"#74797B"}>
              {loginConstants.WELCOME_BACK}
            </Typography>
          </Grid>
        </Grid>
        <Grid>
          <CustomLabel
            label={loginConstants.EMAIL_ID_OR_PHONE}
            isRequired={false}
            isAuth={true}
          />
          <Controller
            control={control}
            name="email"
            render={({ field }) => (
              <CustomInput
                isAuth={true}
                placeholder={loginConstants.ENTER_EMAIL_OR_PHONE}
                {...field}
                hasError={!!errors.email}
                errorMessage={errors.email?.message}
                isNumeric={false}
                onChange={handleEmailChange}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} mt={5}>
          <CustomButton
            label={loginConstants.SEND_VERIFICATION}
            variant="filled"
            fullWidth
            type="submit"
          />
        </Grid>
      </Grid>
    </form>
  );
};

export default ForgotPassword;
