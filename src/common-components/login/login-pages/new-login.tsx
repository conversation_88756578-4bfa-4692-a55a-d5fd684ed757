/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-useless-escape */
import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Grid, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import CustomButton from "../../../common-components/custom-button/custom-button";
import { AlertSeverity } from "../../../common-components/snackbar-alert/snackbar-alert";
import { loginConstants } from "../../../constants/common-component";
import { apiStatus } from "../../../models/apiStatus";
import { newLogin, newLoginReducerAction } from "../../../redux/auth/new-login-reducer";
import { loaderAction } from "../../../redux/auth/loaderReducer";
import { snackbarAction } from "../../../redux/auth/snackbarReducer";
import { AppDispatch, RootState } from "../../../redux/store";
import CustomInput from "../../custom-input/customInput";
import CustomLabel from "../../customLabel/customLabel";
import { LoginpageSchema } from "./login-pages-schema/login-pages-schema";

interface LoginForm {
  username: string;
  password: string;
}

function NewLogin() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const isLogin = useSelector((state: any) => state.loginReducer);
  const searchParams = new URLSearchParams(window.location.search);
  const uuid = searchParams.get("uuid");
  const emailFromUrl = searchParams.get("email");
  const decodedEmail = emailFromUrl ? decodeURIComponent(emailFromUrl) : "";

  const [loginData] = useState<LoginForm>({
    username: decodedEmail,
    password: "",
  });

  const { status: loginStatus, error: loginError }: any = useSelector(
    (state: RootState) => state.NewLoginReducer
  );

  const {
    control,
    formState: { errors },
    handleSubmit,
    clearErrors,
  } = useForm<LoginForm>({
    defaultValues: loginData,
    resolver: yupResolver(LoginpageSchema),
  });

  const onSubmit = (values: LoginForm) => {
    dispatch(newLogin({ otpId: uuid || "", otp: values.password }));
  };

  useEffect(() => {
    if (!isLogin) return;

    switch (loginStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        navigate("/auth/set-password", {
          state: {
            email: emailFromUrl,
            isNewLogin: true,
            uuid: uuid,
          },
        });
        if (isLogin?.data?.access_token) {
          dispatch(
            snackbarAction.showSnackbarAction({
              severity: AlertSeverity.SUCCESS,
              message: "Login successful",
            })
          );
          dispatch(newLoginReducerAction.resetValues());
        }
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.ERROR,
            message: loginError?.message || loginError,
          })
        );
        dispatch(newLoginReducerAction.resetValues());
        break;
    }
  }, [
    isLogin,
    loginStatus,
    dispatch,
    navigate,
    emailFromUrl,
    uuid,
    loginError,
  ]);

  return (
    <Box maxWidth="500px" width="100%" margin="0 auto">
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container flexDirection="column" spacing={3} padding={2}>
          <Grid item>
            <Grid container flexDirection="column">
              <Typography variant="titleMediumBold">
                {loginConstants.LOG_IN_TO_ACC}
              </Typography>
              <Grid mt={1}>
                <Typography variant="titleSmallRegular" color={"#74797B"}>
                  {loginConstants.WELCOME_BACK}
                </Typography>
              </Grid>
            </Grid>
          </Grid>

          <Grid item>
            <CustomLabel
              label={loginConstants.EMAIL_ID_OR_PHONE}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="username"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  placeholder={loginConstants.ENTER_EMAIL_OR_PHONE}
                  {...field}
                  hasError={!!errors.username}
                  errorMessage={errors.username?.message}
                  isNumeric={false}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value.trim();
                    field.onChange(value);
                    const emailRegex =
                      /^\w+([\.-]?\w+)*(\+\w+)?@\w+([\.-]?\w+)*(\.\w{2,})$/;
                    if (emailRegex.test(value.toLowerCase())) {
                      clearErrors("username");
                    }
                  }}
                />
              )}
            />
          </Grid>

          <Grid item>
            <CustomLabel
              label={loginConstants.PASSWORD}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="password"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  isPassword={true}
                  placeholder={loginConstants.ENTER_PASSWORD}
                  {...field}
                  hasError={!!errors.password}
                  errorMessage={errors.password?.message}
                  isNumeric={false}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    field.onChange(value);
                    const hasUpperCase = /[A-Z]/.test(value);
                    const hasLowerCase = /[a-z]/.test(value);
                    const hasNumber = /\d/.test(value);
                    const hasSpecialChar = /[@$!%*?&]/.test(value);
                    const isLongEnough = value.length >= 8;

                    if (
                      hasUpperCase &&
                      hasLowerCase &&
                      hasNumber &&
                      hasSpecialChar &&
                      isLongEnough
                    ) {
                      clearErrors("password");
                    }
                  }}
                />
              )}
            />
          </Grid>

          <Grid item marginTop={2}>
            <CustomButton
              label={loginConstants.CONFIRM_LOGIN}
              variant="filled"
              fullWidth
              type="submit"
            />
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}

export default NewLogin;
