import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Collapse, Grid, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "src/redux/store";
import * as yup from "yup";
import check from "../../../assets/images/check.svg";
import cross from "../../../assets/images/cross.svg";
import { AlertSeverity } from "../../../common-components/alert/alert";
import { loginConstants } from "../../../constants/common-component";
import { apiStatus } from "../../../models/apiStatus";
import { loaderAction } from "../../../redux/auth/loaderReducer";
import {
  setNewPassword,
  setPasswordAction,
} from "../../../redux/auth/set-password-reducer";
import { snackbarAction } from "../../../redux/auth/snackbarReducer";
import CustomButton from "../../custom-button/custom-button";
import CustomInput from "../../custom-input/customInput";
import CustomLabel from "../../customLabel/customLabel";
import { validationStyles } from "./login-page-widgets";

interface SetPasswordForm {
  newPassword: string;
  confirmPassword: string;
}

const SetPassword = () => {
  const navigate = useNavigate();

  const { status: newPasswordStatus, error: newPasswordError }: any =
    useSelector((state: RootState) => state.SetPasswordReducer);

  const {
    control,
    setValue,
    formState: { errors },
    handleSubmit,
  } = useForm<SetPasswordForm>({
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
    mode: "onChange",
    resolver: yupResolver(
      yup.object().shape({
        newPassword: yup.string().required("Enter Password"),
        confirmPassword: yup
          .string()
          .required("Enter Confirm Password")
          .test("passwords-match", "Passwords do not match", function (value) {
            return doPasswordsMatch(this.parent.newPassword, value);
          }),
      })
    ),
  });

  const [minValidation, setMinValidation] = useState(false);
  const [hasSymValidation, setHasSymValidation] = useState(false);
  const [password, setPassword] = useState("");
  const [strongValidation, setStrongValidation] = useState(false);

  const dispatch = useDispatch<AppDispatch>();

  const location = useLocation();

  const onSubmit = (data: any) => {
    const isNewUserFlow = location?.state?.emailForNewLogin !== "NewLogin";

    const emailToSend = isNewUserFlow
      ? location?.state?.emailForNewLogin
      : location?.state?.isForgotPasswordEmail;

    dispatch(
      setNewPassword({
        newPassword: data?.newPassword,
        otpId: location?.state?.isForgotPassword
          ? location?.state?.currentOtpId
          : location?.state?.isNewLogin
            ? location?.state?.uuid
            : location?.state?.uuid || "",
        email: emailToSend,
      })
    );
  };

  const doPasswordsMatch = (
    createPassword: string,
    confirmPassword: string
  ) => {
    return createPassword === confirmPassword;
  };

  function hasMinimumLength(password: string) {
    return password.length >= 8;
  }

  function hasSymbolOrNumber(password: string) {
    const symbolOrNumberRegex = /[0-9!@#$%^&*]/;
    return symbolOrNumberRegex.test(password);
  }

  function isStrongPassword(password: string) {
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
    return strongPasswordRegex.test(password);
  }

  useEffect(() => {
    switch (newPasswordStatus) {
      case apiStatus.LOADING:
        dispatch(loaderAction.showLoader());
        break;
      case apiStatus.SUCCEEDED:
        dispatch(loaderAction.hideLoader());
        navigate("../login");
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.SUCCESS,
            message: "Password Set Successfully",
          })
        );
        dispatch(setPasswordAction.resetValues());
        break;
      case apiStatus.FAILED:
        dispatch(loaderAction.hideLoader());
        dispatch(
          snackbarAction.showSnackbarAction({
            severity: AlertSeverity.ERROR,
            message: newPasswordError?.message || newPasswordError,
          })
        );
        dispatch(setPasswordAction.resetValues());
        break;
    }
  }, [newPasswordStatus, dispatch, navigate, newPasswordError]);

  useEffect(() => {
    setMinValidation(hasMinimumLength(password));
    setHasSymValidation(hasSymbolOrNumber(password));
    setStrongValidation(isStrongPassword(password));
  }, [password]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid container spacing={3} width={"29vw"} ml={5} mb={10}>
        <Grid display={"flex"} mt={5} mb={4}>
          <Grid>
            <Typography variant="titleMediumBold">
              {loginConstants.SET_NEW_PASSWORD}
            </Typography>
          </Grid>
        </Grid>
        <Grid display={"flex"} flexDirection={"column"} width={"29vw"}>
          <Grid mb={3}>
            <CustomLabel
              label={loginConstants.NEW_PASSWORD}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="newPassword"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  isPassword={true}
                  placeholder={loginConstants.ENTER_NEW_PASS}
                  {...field}
                  hasError={!!errors.newPassword}
                  errorMessage={errors.newPassword?.message}
                  isNumeric={false}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setPassword(e.target.value);
                    setValue("newPassword", e.target.value);
                  }}
                />
              )}
            />
          </Grid>
          <Grid>
            <CustomLabel
              label={loginConstants.CONFIRM_PASSWORD}
              isRequired={false}
              isAuth={true}
            />
            <Controller
              control={control}
              name="confirmPassword"
              render={({ field }) => (
                <CustomInput
                  isAuth={true}
                  isPassword={true}
                  placeholder={loginConstants.CONFIRM_PASSWORD}
                  {...field}
                  hasError={!!errors.confirmPassword}
                  errorMessage={errors.confirmPassword?.message}
                  isNumeric={false}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} mt={2}>
            <Collapse in={!!password}>
              <Box display={"flex"} gap={1} alignItems={"center"} mb={1}>
                <img src={minValidation ? check : cross} />
                <Typography sx={validationStyles}>
                  {loginConstants.EIGHT_CHAR}
                </Typography>
              </Box>
              <Box display={"flex"} gap={1} alignItems={"center"} mb={1}>
                <img src={hasSymValidation ? check : cross} />
                <Typography sx={validationStyles}>
                  {loginConstants.CONTAINS_NUMBER_CHAR}
                </Typography>
              </Box>
              <Box display={"flex"} gap={1} alignItems={"center"} mb={1}>
                <img src={strongValidation ? check : cross} />
                <Typography sx={validationStyles}>
                  {loginConstants.STRONG_PASSWORD}
                </Typography>
              </Box>
            </Collapse>
          </Grid>
        </Grid>
        <Grid xs={12} mt={4.5}>
          <CustomButton
            label={loginConstants.SET_PASSWORD}
            variant="filled"
            fullWidth
            type="submit"
          />
        </Grid>
      </Grid>
    </form>
  );
};

export default SetPassword;
