import * as yup from "yup";
import { ValidationMessages } from "../../../../constants/formConst";

export const EnterOtpSchema = yup.object().shape({
  otp: yup
    .string()
    .required(ValidationMessages.OtpRequired)
    .length(6, ValidationMessages.OtpLength),
});

export const SetPasswordSchema = yup.object().shape({
  newPassword: yup
    .string()
    .required(ValidationMessages.NewPasswordRequired)
    .min(6, ValidationMessages.PasswordMinLength)
    .matches(/\d/, ValidationMessages.PasswordOneNumeric)
    .matches(
      /[!@#$%^&*(),.?":{}|<>]/,
      ValidationMessages.PasswordOneSpecialChar
    )
    .matches(/[A-Z]/, ValidationMessages.PasswordOneUppercase)
    .matches(/^\S*$/, ValidationMessages.PasswordNoSpaces),
  confirmPassword: yup
    .string()
    .required(ValidationMessages.ConfirmPasswordRequired)
    .oneOf([yup.ref("newPassword")], ValidationMessages.PasswordsMustMatch)
    .matches(/^\S*$/, ValidationMessages.PasswordNoSpaces),
});

export const LoginpageSchema = yup.object().shape({
  username: yup
    .string()
    .required(ValidationMessages.EmailOrPhoneRequired)
    .test(
      "email-or-phone",
      ValidationMessages.ValidEmailOrPhoneRequired,
      (value) => {
        if (!value) return false;
        const emailRegex =
          /^\w+([\.-]?\w+)*(\+\w+)?@\w+([\.-]?\w+)*(\.\w{2,})$/;
        const phoneRegex = /^\d{10}$/;
        const isEmail = emailRegex.test(value.toLowerCase());
        const isPhone = phoneRegex.test(value);
        return isEmail || isPhone;
      }
    ),
  password: yup
    .string()
    .required(ValidationMessages.PasswordRequired)
    .matches(/^\S*$/, ValidationMessages.PasswordNoSpaces),
});

export const ForgotPasswordSchema = yup.object().shape({
  email: yup
    .string()
    .required(ValidationMessages.EmailRequired)
    .transform((value) => value?.toLowerCase())
    .matches(/^\w+([\.-]?\w+)*(\+\w+)?@\w+([\.-]?\w+)*(\.\w{2,})$/, {
      message: ValidationMessages.ValidEmailRequired,
    })
    .required(ValidationMessages.ValidEmailRequired)
    .max(255, ValidationMessages.EmailMaxLength),
});
