import { Avatar, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React, { useRef, useState, useEffect } from "react";

interface ImageUploadProps {
  initialImage?: string;
  onImageChange?: (file: File) => void;
  size?: number;
  disabled?: boolean;
  showRemoveIcon?: boolean;
  onRemoveImage?: () => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  initialImage,
  onImageChange,
  size = 150,
  disabled = false,
  showRemoveIcon = false,
  onRemoveImage,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string>(initialImage || "");
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setPreviewUrl(initialImage || "");
  }, [initialImage]);

  const handleImageClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
      onImageChange?.(file);
    }
  };

  const handleRemoveClick = () => {
    // event.stopPropagation();
    setPreviewUrl(initialImage || "");
    onRemoveImage?.();
  };

  return (
    <div style={{ position: "relative", display: "inline-block" }}>
      <Avatar
        src={previewUrl}
        alt="Profile Picture"
        sx={{
          width: size,
          height: size,
          bgcolor: "#f5f5f5",
          color: "#757575",
          cursor: disabled ? "default" : "pointer",
        }}
        onClick={handleImageClick}
      />

      {showRemoveIcon && previewUrl && (
        <IconButton
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            backgroundColor: "rgba(255, 255, 255, 0.7)",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.9)",
            },
          }}
          size="small"
          onClick={handleRemoveClick}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      )}

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageChange}
        accept="image/*"
        style={{ display: "none" }}
        disabled={disabled}
      />
    </div>
  );
};

export default ImageUpload;
