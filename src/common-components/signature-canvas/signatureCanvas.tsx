import { Box, Button, Grid, Modal, useMediaQuery } from "@mui/material";
import React, { useRef, useState } from "react";
import SignatureCanvas from "react-signature-canvas";
import { theme } from "../../utils/theme";
import "./signatureCanvas.css";

interface SignatureCaptureProps {
  onSave: (dataURL: string) => void;
  onCancel: (event: React.MouseEvent<HTMLButtonElement>) => void;
  open: boolean;
}

function SignatureCapture({ onSave, onCancel, open }: SignatureCaptureProps) {
  const [, setShowSignatureModal] = useState(false);
  const sigCanvas = useRef<any>({});
  const [typeSign] = useState(false);
  const typedSignatureRef = useRef<HTMLInputElement>(null);

  const handleSaveSignature = () => {
    if (!typeSign && sigCanvas.current.isEmpty()) {
      alert("Please provide a signature first.");
    } else if (typeSign && typedSignatureRef.current?.value.trim() === "") {
      alert("Please type your signature first.");
    } else {
      setShowSignatureModal(false);
      onSave(
        typeSign
          ? typedSignatureRef.current?.value || ""
          : sigCanvas.current.toDataURL()
      );
    }
  };

  const isSmallScreen = useMediaQuery(theme.breakpoints.up("sm"));

  return (
    <Modal open={open} onClose={() => setShowSignatureModal(false)}>
      <Box
        sx={{
          bgcolor: "background.paper",
          boxShadow: 24,
          p: isSmallScreen ? 4 : 2,
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: isSmallScreen ? "30vw" : "50vw",
        }}
      >
        <Grid container spacing={2} alignItems="center">
          {!typeSign && (
            <Grid item xs={12} textAlign="center">
              <SignatureCanvas
                penColor="black"
                canvasProps={{
                  className: "signatureCanvas",
                }}
                ref={sigCanvas}
              />
            </Grid>
          )}

          <Grid item xs={12} justifyContent="center" display="flex" gap={2}>
            <Button variant="contained" onClick={handleSaveSignature}>
              Save Signature
            </Button>
            <Button variant="outlined" onClick={() => sigCanvas.current.clear()}>
              Clear Signature
            </Button>
            <Button variant="outlined" onClick={onCancel}>
              Cancel
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Modal>
  );
}

export default SignatureCapture;
