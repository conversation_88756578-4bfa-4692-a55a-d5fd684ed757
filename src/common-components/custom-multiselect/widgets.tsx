export const customSelectStyles = {
  headerLabel: {
    // fontSize: "0.9rem !important",
    // wordWrap: "break-word",
    // fontStyle: "normal !important",
    // fontWeight: "400 !important",
    // lineHeight: "130% !important",
    // letterSpacing: "0.12px !important",
  },
};

export const selectInputStyle = {
  ".MuiOutlinedInput-notchedOutline": {
    border: "1px solid #ccc",
    borderColor: "1px solid #ccc !important",
  },
  height: "40px !important",
  width: "100%",
  borderRadius: "4px",

  "&.Mui-error": {
    padding: "0px!important",
  },
  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #ccc",
    borderColor: "1px solid #ccc !important",
  },
  "&.MuiPaper-root-MuiPopover-paper-MuiMenu-paper.MuiMenu-paper": {
    height: "40px !important",
  },
};
