import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Chip,
  ClickAwayListener,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Paper,
  Popper,
  TextField,
  Typography,
  IconButton,
  Checkbox,
  styled,
} from "@mui/material";
import {
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";

interface Option {
  value: string;
  key: string;
}

interface MultiSelectDropdownProps {
  options: Option[];
  selectedValues: string[];
  setSelectedValues: (values: string[]) => void;
  isRequired?: boolean;
  isError?: boolean;
  error?: any;
  label?: string;
  name: string;
  placeholder?: string;
  handleInputVal?: (inputValue: string) => void;
}

const StyledPopper = styled(Popper)(({ theme }) => ({
  zIndex: 9999,
  width: "100%",
  marginTop: theme.spacing(1),
  "& .MuiPaper-root": {
    maxHeight: 300,
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
  },
}));

const SearchContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  position: "relative",
}));

const OptionsContainer = styled(Box)({
  overflowY: "auto",
  flex: 1,
});

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  options = [],
  selectedValues = [],
  setSelectedValues,
  error,
  isError,
  isRequired,
  label,
  handleInputVal,
  name,
  placeholder = "Select options...",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const anchorRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const filteredOptions = options.filter((option) =>
    option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchTerm(inputValue);
    if (handleInputVal && inputValue !== "") {
      handleInputVal(inputValue);
    }
  };

  const handleSelect = (key: string) => {
    if (selectedValues.includes(key)) {
      setSelectedValues(selectedValues.filter((val) => val !== key));
    } else {
      setSelectedValues([...selectedValues, key]);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const handleClearAll = () => {
    setSelectedValues([]);
  };

  const handleDelete = (key: string) => {
    setSelectedValues(selectedValues.filter((val) => val !== key));
  };

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  return (
    <Box>
      <FormControl fullWidth error={isError}>
        <div ref={anchorRef}>
          <InputLabel id="multi-select-label" required={isRequired}>
            {label}
          </InputLabel>
          <Box
            onClick={() => setIsOpen(!isOpen)}
            sx={{
              minHeight: 42,
              border: (theme) =>
                `1px solid ${isError ? theme.palette.error.main : theme.palette.grey[300]}`,
              borderRadius: 1,
              p: 1,
              cursor: "pointer",
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              gap: 0.5,
            }}
          >
            {selectedValues.length > 0 ? (
              <Box
                sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, flex: 1 }}
              >
                {options
                  .filter((option) => selectedValues.includes(option.key))
                  .map((option) => (
                    <Chip
                      key={option.key}
                      label={option.value}
                      onDelete={() => handleDelete(option.key)}
                      size="small"
                    />
                  ))}
              </Box>
            ) : (
              <Typography color="text.secondary">{placeholder}</Typography>
            )}

            <Box sx={{ display: "flex", alignItems: "center", ml: "auto" }}>
              {selectedValues.length > 0 && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClearAll();
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              <KeyboardArrowDownIcon
                sx={{
                  transform: isOpen ? "rotate(180deg)" : "none",
                  transition: "transform 0.2s",
                }}
              />
            </Box>
          </Box>
        </div>

        {error?.[name] && <FormHelperText>{error[name]}</FormHelperText>}

        <StyledPopper
          open={isOpen}
          anchorEl={anchorRef.current}
          placement="bottom-start"
        >
          <ClickAwayListener onClickAway={() => setIsOpen(false)}>
            <Paper elevation={8}>
              <SearchContainer>
                <TextField
                  inputRef={searchInputRef}
                  fullWidth
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search..."
                  InputProps={{
                    startAdornment: (
                      <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                    ),
                    endAdornment: searchTerm && (
                      <IconButton size="small" onClick={handleClearSearch}>
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    ),
                  }}
                />
              </SearchContainer>

              <OptionsContainer>
                {filteredOptions.length > 0 ? (
                  filteredOptions.map((option) => (
                    <MenuItem
                      key={option.key}
                      onClick={() => handleSelect(option.key)}
                      sx={{ display: "flex", alignItems: "center" }}
                    >
                      <Checkbox
                        checked={selectedValues.includes(option.key)}
                        size="small"
                      />
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: selectedValues.includes(option.key)
                            ? 500
                            : 400,
                        }}
                      >
                        {option.value}
                      </Typography>
                    </MenuItem>
                  ))
                ) : (
                  <Box sx={{ p: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      No options found
                    </Typography>
                  </Box>
                )}
              </OptionsContainer>
            </Paper>
          </ClickAwayListener>
        </StyledPopper>
      </FormControl>
    </Box>
  );
};

export default MultiSelectDropdown;
