export const staffMockData = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    contact: "******-123-4567",
    role: "Administrator",
    status: "Active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    contact: "******-987-6543",
    role: "Nurse",
    status: "Active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    contact: "******-456-7890",
    role: "Doctor",
    status: "Inactive",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

export const clinicianMockData = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    contact: "******-234-5678",
    npinumber: "**********",
    worklocation: "Main Hospital",
    status: "Active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    contact: "******-876-5432",
    npinumber: "**********",
    worklocation: "West Wing Clinic",
    status: "Active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "Amara Patel",
    email: "<EMAIL>",
    contact: "******-345-6789",
    npinumber: "**********",
    worklocation: "East Side Medical Center",
    status: "Inactive",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    name: "Thomas Rodriguez",
    email: "<EMAIL>",
    contact: "******-654-3210",
    npinumber: "**********",
    worklocation: "South Branch Office",
    status: "Active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

export const contactMockData = [
  {
    uuid: "1",
    name: "John Doe",
    email: "<EMAIL>",
    phone: "******-111-2222",
    fax: "******-222-3333",
    contact: "************",
    address: "123 Main St, City, State 12345",
    contacttype: "Referral",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    uuid: "2",
    name: "Oklahoma Diagnostics",
    email: "<EMAIL>",
    phone: "******-444-5555",
    fax: "******-666-7777",
    contact: "************",
    address: "456 Oak Ave, City, State 12345",
    contacttype: "Lab",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

export const rolesMockData = [
  {
    description: "Patients",
  },
  {
    description: "Create Edit View Patient",
    uuid: "create-edit-view-patient",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "View Patients",
    uuid: "view-patients",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Share Patients",
    uuid: "share-patients",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Appointments",
  },
  {
    description: "Create Appointments",
    uuid: "create-appointments",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "View Appointments",
    uuid: "view-appointments",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Appointment Clinician Selection",
    uuid: "appointment-clinician-selection",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Availability",
  },
  {
    description: "Create Availability",
    uuid: "create-availability",
    superAdmin: false,
    clinician: true,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "View Availability",
    uuid: "view-availability",
    superAdmin: false,
    clinician: true,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Have Availability",
    uuid: "have-availability",
    superAdmin: false,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
  {
    description: "Documents - Intake",
  },
  {
    description: "Edit Intake Documents",
    // isCategory: true,
    uuid: "cat-documents-intake",
    superAdmin: true,
    clinician: false,
    staff: false,
    client: false,
    recordCustodian: false,
  },
];

export const locationTableData = [
  {
    locationName: "Main Clinic",
    contactNumber: "************",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "************",
    address: "205 Champion Way Suite 11 Georgetown, KY 40324",
    status: "active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    locationName: "Downtown Branch",
    contactNumber: "502-603-0022",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "502-603-0023",
    address: "123 Main Street Suite 4B Georgetown, KY 40324",
    status: "active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    locationName: "Northside Clinic",
    contactNumber: "502-603-0024",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "502-603-0025",
    address: "4580 North Ave Suite 201 Georgetown, KY 40324",
    status: "active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    locationName: "Southside Medical",
    contactNumber: "************",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "************",
    address: "789 South Blvd Suite 30 Georgetown, KY 40324",
    status: "inactive",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    locationName: "Eastside Health Center",
    contactNumber: "************",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "************",
    address: "623 East Main Road Suite 5 Georgetown, KY 40324",
    status: "active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    locationName: "Westside Family Practice",
    contactNumber: "************",
    email: "<EMAIL>",
    groupNPI: "**********",
    fax: "************",
    address: "1024 West Park Avenue Suite 15 Georgetown, KY 40324",
    status: "active",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

export const practiceData = {
  name: "Practice Easily",
  clinicNPI: "**********",
  emailID: "<EMAIL>",
  taxType: "TIN",
  taxonomyCode: "**********",
  taxNumber: "123456 (TIN)",
  address: "205 Champion Way Suite 11 Georgetown, KY 40324",
  contactNumber: "************",
};

export const feeScheduleMockData = [
  {
    procedureCode: "Reschedule Appointment",
    rate: "$10",
    codeType: "-",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    procedureCode: "No Show",
    rate: "$20",
    codeType: "-",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    procedureCode: "Cancel Appointment",
    rate: "$30",
    codeType: "-",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    procedureCode: "99088",
    rate: "$80",
    codeType: "CPT",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
  {
    procedureCode: "99077",
    rate: "$60",
    codeType: "Self Pay",
    action: [
      { label: "Edit", route: "edit" },
      { label: "Delete", route: "delete" },
    ],
  },
];

export const APPOINTMENT_MOCK_DATA = [
  {
    clientName: "Marvin McKinney",
    apptMode: "Location 1",
    time: "5: 40 am (15 Mins)",
    apptType: "New",
    intakeForm: "PENDING",
    dob: "1/15/12",
    contactNo: "302 555-0107",
    clinician: "Wade Warren",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Floyd Miles",
    apptMode: "Virtual",
    time: "5: 45 am (15 Mins)",
    apptType: "Follow Up",
    intakeForm: "PENDING",
    dob: "4/21/12",
    contactNo: "208 555-0112",
    clinician: "Eleanor Pena",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Jacob Jones",
    apptMode: "Virtual",
    time: "6: 00 am (15 Mins)",
    apptType: "New",
    intakeForm: "PENDING",
    dob: "11/7/16",
    contactNo: "219 555-0114",
    clinician: "Floyd Miles",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Cody Fisher",
    apptMode: "Location 1",
    time: "6: 30 am (15 Mins)",
    apptType: "Follow Up",
    intakeForm: "SCHEDULED",
    dob: "5/19/12",
    contactNo: "684 555-0102",
    clinician: "Albert Flores",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Dianne Russell",
    apptMode: "Location 2",
    time: "6: 45 am (15 Mins)",
    apptType: "New",
    intakeForm: "SCHEDULED",
    dob: "8/30/14",
    contactNo: "303 555-0105",
    clinician: "Annette Black",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Annette Black",
    apptMode: "Virtual",
    time: "7: 00 am (15 Mins)",
    apptType: "Follow Up",
    intakeForm: "SCHEDULED",
    dob: "10/6/13",
    contactNo: "907 555-0101",
    clinician: "Jane Cooper",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Theresa Webb",
    apptMode: "Virtual",
    time: "7: 15 am (15 Mins)",
    apptType: "Follow Up",
    intakeForm: "COMPLETED",
    dob: "12/10/13",
    contactNo: "629 555-0129",
    clinician: "Darrell Steward",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Kathryn Murphy",
    apptMode: "Location 1",
    time: "7: 30 am (15 Mins)",
    apptType: "New",
    intakeForm: "COMPLETED",
    dob: "7/11/19",
    contactNo: "217 555-0113",
    clinician: "Jenny Wilson",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Cameron Williamson",
    apptMode: "Location 2",
    time: "7: 45 am (15 Mins)",
    apptType: "New",
    intakeForm: "COMPLETED",
    dob: "6/19/14",
    contactNo: "505 555-0125",
    clinician: "Kristin Watson",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
  {
    clientName: "Robert Fox",
    apptMode: "Location 1",
    time: "8: 00 am (15 Mins)",
    apptType: "Follow Up",
    intakeForm: "COMPLETED",
    dob: "1/31/14",
    contactNo: "252 555-0126",
    clinician: "Courtney Henry",
    status: "SCHEDULED",
    action: [
      { label: "Start", route: "start" },
      { label: "More", route: "more" },
    ],
  },
];
