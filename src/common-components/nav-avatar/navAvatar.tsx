import { Avatar, Box, Divider } from "@mui/material";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { UserProfile } from "../../models/userModel";
import { logout } from "../../redux/auth/logout-reducer";
import { AppDispatch } from "../../redux/store";
import {
  navAvatarStyles,
  navLogOut,
  navProfile,
} from "./widgets/navAvatarStyles";

interface NavAvatarProps {
  response?: UserProfile | null;
}

export default function NavAvatar(props: NavAvatarProps) {
  const { response } = props;

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const open = Boolean(anchorEl);
  const dispatch = useDispatch<AppDispatch>();

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNavigateUserProfile = () => {
    navigate("/admin/user-profile");
    handleClose();
  };

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <Box display={"flex"} alignItems={"center"}>
      <Box onClick={handleClick} component="div">
        <Avatar
          id="basic-button"
          aria-controls={open ? "basic-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={open ? "true" : undefined}
          alt={`${response?.firstName} ${response?.lastName}`}
          src={response?.avatar ?? ""}
          sx={navAvatarStyles}
        >
          {`${response?.firstName?.[0] ?? ""}${response?.lastName?.[0] ?? ""}`.toUpperCase()}
        </Avatar>
      </Box>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {
          <>
            <MenuItem sx={navProfile} onClick={handleNavigateUserProfile}>
              {"Profile"}
            </MenuItem>
            <Divider />
          </>
        }
        <MenuItem onClick={handleLogout} sx={navLogOut}>
          {"Logout"}
        </MenuItem>
      </Menu>
    </Box>
  );
}
