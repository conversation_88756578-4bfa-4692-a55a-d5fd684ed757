export const outlined = {
  borderRadius: "8px",
  border: "1px solid var(--Primary-Brand, #727272)",
  color: "var(--Primary-Brand, #727272)",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 14,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "111%",
  letterSpacing: 0.056,
  padding: "12.5px 40px",
  textTransform: "inherit",
  height: "40px",
};

export const filled = {
  borderRadius: "8px",
  background: "var(--Primary-Brand, #0068FF)",
  color: "var(--Solid-White, var(--common-white-main, #FFF))",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 14,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "111%",
  padding: "12.5px 40px",
  letterSpacing: 0.056,
  textTransform: "inherit",
  height: "40px",
  "&:hover": {
    backgroundColor: "var(--Primary-Brand, #1B5984)",
  },
};

export const transformTextNone = {
  textTransform: "inherit",
};

export const disableButton = {
  pointerEvent: "none",
  color: "white !important",
  opacity: "50% !important",
  background: "#89aecf !important",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 14,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "111%",
  letterSpacing: 0.056,
  padding: "12.5px 40px",
  textTransform: "inherit",
  height: "40px",
  borderRadius: "8px",
};

export const warning = {
  borderRadius: "8px",
  border: "1px solid var(--Primary-Brand,#DC6803)",
  color: "var(--Primary-Brand, #DC6803)",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 14,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "111%",
  letterSpacing: 0.056,
  padding: "12.5px 40px",
  textTransform: "inherit",
  height: "40px",
};

export const paddingZero = {
  borderRadius: "8px",
  background: "var(--Primary-Brand,  #0068FF)",
  color: "var(--Solid-White, var(--common-white-main, #FFF))",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 14,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "111%",
  padding: "9px 20px",
  letterSpacing: 0.056,
  textTransform: "inherit",
  height: "40px",
  "&:hover": {
    backgroundColor: "var(--Primary-Brand, #1B5984)",
  },
};

export const smallButton = {
  borderRadius: "8px",
  padding: "2px 5px",
  border: "1px solid #0068FF",
  background: "#F2F8FF",

};

export const editProfile = {
  borderRadius: "8px",
  border: "1px solid #0068FF",
  background: "#F2F8FF",
  color: "#0068FF",
  textAlign: "center",
  fontFamily: "Figtree",
  fontSize: 16,
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "normal",
  padding: "9px 24px",
  textTransform: "none",
  display: "flex",
  alignItems: "center",
  gap: "8px",
  "&:hover": {
    background: "#E6F0FF",
    border: "1px solid #0068FF",
  },
};
