import moment from "moment-timezone";
import { DAYS } from "../../pages/apps/provider-portal/availability/model/availabilityModel";

export const getDaysOfWeekInRange = (
  startDate: string,
  endDate: string
): DAYS[] => {
  const start = moment(startDate);
  const end = moment(endDate);
  const days: DAYS[] = [];
  const current = start.clone();

  while (current.isSameOrBefore(end, "day")) {
    const dayOfWeek = current.format("dddd").toUpperCase() as DAYS;
    if (!days.includes(dayOfWeek)) {
      days.push(dayOfWeek);
    }
    current.add(1, "day");
  }

  // Sort the days of the week in the standard order (Monday to Sunday)
  const orderedDays: DAYS[] = [
    DAYS.MONDAY,
    DAYS.TUESDAY,
    DAYS.WEDNESDAY,
    DAYS.THURSDAY,
    DAYS.FRIDAY,
    DAYS.SATURDAY,
    DAYS.SUNDAY,
  ];

  days.sort((a, b) => orderedDays.indexOf(a) - orderedDays.indexOf(b));

  return days;
};
