export const capitalizeFirstLetter = (value: string): string => {
  if (!value) return "";

  return value
    .split(",") // split by comma
    .map((role) =>
      role
        .trim() // remove extra spaces
        .split("_") // handle underscores
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    )
    .join(", "); // join back with comma + space
};

export const getRolesCountAndFormat = (
  roles: string[]
): { formattedRoles: string; count: number } => {
  if (!roles || !Array.isArray(roles) || roles.length === 0) {
    return { formattedRoles: "", count: 0 };
  }

  const formattedAndTrimmedRoles = roles.map((role) =>
    role
      .trim()
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ")
  );

  let displayRoles = "";
  if (formattedAndTrimmedRoles.length === 1) {
    displayRoles = formattedAndTrimmedRoles[0];
  } else if (formattedAndTrimmedRoles.length === 2) {
    displayRoles = `${formattedAndTrimmedRoles[0]}, ${formattedAndTrimmedRoles[1]}`;
  } else {
    displayRoles = `${formattedAndTrimmedRoles[0]}, ${formattedAndTrimmedRoles[1]} (+${formattedAndTrimmedRoles.length - 2})`;
  }

  return { formattedRoles: displayRoles, count: roles.length };
};

export const formatTitle = (fullNameWithTitle: string): string => {
  if (typeof fullNameWithTitle !== "string") {
    return "";
  }

  return fullNameWithTitle.replace(/\(([^)]+)\)/, (_, rawTitle) => {
    const roles = rawTitle.split(",").map((role: string) => {
      const words = role.trim().split(/[_\s]+/);
      const formattedWords = words.map(
        (word: string) =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      );
      return formattedWords.join(" ");
    });

    if (roles.length <= 3) {
      return `(${roles.join(", ")})`;
    } else {
      const visibleRoles = roles.slice(0, 2).join(", ");
      const remainingCount = roles.length - 2;
      return `(${visibleRoles} +${remainingCount})`;
    }
  });
};

export const formatDateToMMDDYYYY = (isoDate: string): string => {
  const [year, month, day] = isoDate.split("-");
  return `${month}-${day}-${year}`;
};

export const formatISOToMMDDYYYYLocal = (isoDateString: string): string => {
  const date = new Date(isoDateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${month}-${day}-${year}`;
};

export const formatPhoneNumber = (phoneNumber: string): string => {
  const hasPlus = phoneNumber?.startsWith("+");

  const cleaned = phoneNumber?.replace(/\D/g, "");

  if (!cleaned || cleaned.length < 10) {
    return phoneNumber;
  }

  const firstDigit = cleaned[0];
  const remaining = cleaned.slice(1);
  return `${hasPlus ? "+" : ""}${firstDigit} (${remaining.slice(0, 3)})-${remaining.slice(3, 6)}-${remaining.slice(6, 10)}`;
};
