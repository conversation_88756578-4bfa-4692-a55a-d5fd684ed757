import {
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { errorStyle } from "../custom-input/widgets/customInputStyles";
import {
  customSelectStyles,
  selectInputStyle,
} from "./widgets/customSelectStyles";

interface CustomSelectProps {
  placeholder: string;
  name?: string;
  value: string;
  items: { value: string; label: string }[];
  onChange?: (e: SelectChangeEvent<string>) => void;
  hasError?: boolean;
  errorMessage?: string;
  isDisabled?: boolean;
  bgWhite?: boolean;
  tablepagination?: boolean;
  backgroundColor?: string;
  crossable?: boolean;
}

function CustomSelect(props: CustomSelectProps) {
  const classes = customSelectStyles();
  const { items, bgWhite, onChange, backgroundColor, crossable } = props;

  const handleValue = (e: SelectChangeEvent<string>) => {
    const selectedLabel = e.target.value;
    const selectedKey =
      props?.items?.find((item) => item.label === selectedLabel)?.value || "";
    e.target.value = selectedKey;

    if (onChange) onChange(e);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onChange) {
      const event = {
        target: { value: "" },
      } as SelectChangeEvent<string>;
      onChange(event);
    }
  };

  const getLabel = (value: string) => {
    return items?.find((item) => item.value === value)?.label || "";
  };

  return (
    <>
      <div
        style={{ position: "relative", display: "inline-block", width: "100%" }}
      >
        <Select
          disabled={props.isDisabled && props.isDisabled}
          sx={{
            ...selectInputStyle,
            backgroundColor:
              backgroundColor || (bgWhite === true ? "inherit" : "inherit"),
            ...(props.tablepagination && { border: "none" }),
            ...(crossable && props.value && { paddingRight: "40px" }),
          }}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
            },
          }}
          displayEmpty
          name={props?.name}
          value={getLabel(props.value)}
          onChange={handleValue}
          error={props.hasError}
          renderValue={(selected) => (
            <Typography
              className={classes.headerLabel}
              sx={{
                color: selected ? "black !important" : "a19a9a !important",
              }}
            >
              {selected || props?.placeholder}
            </Typography>
          )}
        >
          {props?.items && props?.items?.length !== 0 ? (
            props?.items?.map((option) => (
              <MenuItem key={option.value} value={option.label}>
                <Typography className={classes.menuLabel}>
                  {option.label}
                </Typography>
              </MenuItem>
            ))
          ) : (
            <MenuItem value="">
              <Typography className={classes.menuLabel}>
                No options available
              </Typography>
            </MenuItem>
          )}
        </Select>
        {crossable && props.value && !props.isDisabled && (
          <IconButton
            size="small"
            onClick={handleClear}
            sx={{
              position: "absolute",
              right: "28px",
              top: "50%",
              transform: "translateY(-50%)",
              padding: "4px",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.04)",
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )}
      </div>
      {props.hasError && (
        <Typography sx={errorStyle} variant="caption">
          {props.hasError ? props.errorMessage : ""}
        </Typography>
      )}
    </>
  );
}

export default CustomSelect;
