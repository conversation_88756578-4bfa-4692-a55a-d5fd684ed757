/* eslint-disable @typescript-eslint/no-explicit-any */
import { Typography } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from "dayjs";

interface DatePickerProps {
  handleDateChange?: (newValue: string) => void;
  value: string; // Still accept string externally
  hasError?: boolean;
  errorMessage?: string;
  placeholder?: string;
  disableFuture?: boolean;
  disablePast?: boolean;
}

function CustomDatePicker(props: DatePickerProps) {
  const {
    handleDateChange,
    value,
    hasError,
    errorMessage,
    placeholder,
    disableFuture,
    disablePast,
  } = props;

  const parsedValue = value ? dayjs(value) : null;

  const handleChange = (
    newValue: any
    // context: PickerChangeHandlerContext<DateValidationError>
  ) => {
    if (handleDateChange && newValue?.isValid()) {
      handleDateChange(newValue.format("YYYY-MM-DD"));
    } else if (handleDateChange && !newValue) {
      handleDateChange("");
    }
  };

  return (
    <div style={{ width: "100%" }}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          value={parsedValue}
          disableFuture={disableFuture}
          disablePast={disablePast}
          format="MM/DD/YYYY"
          onChange={handleChange}
          slotProps={{
            textField: {
              size: "small",
              error: hasError,
              placeholder: placeholder,
              sx: {
                width: "100%",
                "& .MuiOutlinedInput-root": {
                  height: "40px",
                  backgroundColor: "white",
                },
                "& fieldset": {
                  border: hasError ? "1px solid #d32f2f" : "1px solid #C9CBCC",
                  borderRadius: "5px",
                },
                "& input::placeholder": {
                  color: "black",
                  fontFamily: "Figtree",
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: 400,
                  lineHeight: "120%",
                  letterSpacing: "0.024px",
                },
                "& input": {
                  color: "black",
                  fontFamily: "Figtree",
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: 400,
                  lineHeight: "120%",
                  letterSpacing: "0.024px",
                  padding: "6px",
                },
                "& .MuiIconButton-root": {
                  color: "#BCBCBC",
                  padding: "4px",
                  width: "32px",
                  height: "32px",
                },
              },
            },
          }}
        />
      </LocalizationProvider>
      {hasError && (
        <Typography
          sx={{
            color: "#d32f2f",
            marginTop: "3px",
            fontFamily: "Figtree",
            fontSize: "0.75rem",
            lineHeight: "1.66",
          }}
          variant="caption"
        >
          {errorMessage}
        </Typography>
      )}
    </div>
  );
}

export default CustomDatePicker;
