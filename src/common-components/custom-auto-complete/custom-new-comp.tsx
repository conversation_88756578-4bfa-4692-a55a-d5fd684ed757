import {
  Clear as ClearIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import {
  Box,
  Checkbox,
  Chip,
  FormControl,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  TextField,
  Typography,
  FormHelperText,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";
import CustomLabel from "../customLabel/customLabel";

interface Option {
  value: string;
  key: string;
}

interface MultiSelectDropdownProps {
  options: Option[];
  selectedValues: { key: string; value: string }[];
  setSelectedValues: (values: { key: string; value: string }[]) => void;
  isRequired?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  label?: string;
  name: string;
  placeholder?: string;
  handleInputVal?: (inputValue: string) => void;
  allLoadedOptions?: Option[];
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  options = [],
  selectedValues = [],
  setSelectedValues,
  hasError,
  isRequired,
  label,
  handleInputVal,
  placeholder = "Select Group Members",
  allLoadedOptions = [],
  errorMessage,
  // name,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [dropdownStyles, setDropdownStyles] = useState({
    top: 0,
    left: 0,
    width: 0,
  });

  // Use allLoadedOptions if provided, otherwise fall back to options
  const optionsToUse = allLoadedOptions.length > 0 ? allLoadedOptions : options;

  // Get selected options to display at top - use allLoadedOptions to ensure we can find them
  const selectedOptions = optionsToUse.filter((option) =>
    selectedValues.some((selected) => selected.key === option.key)
  );

  // Filter unselected options based on search - only from current options (search results)
  const unselectedOptions = options.filter(
    (option) =>
      !selectedValues.some((selected) => selected.key === option.key) &&
      option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Combine selected options (at top) with filtered unselected options
  const filteredOptions = [...selectedOptions, ...unselectedOptions];

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchTerm(inputValue);
    handleInputVal && handleInputVal(inputValue);
  };

  const handleSelect = (key: string, value: string) => {
    let newSelectedValues;
    if (selectedValues.some((selected) => selected.key === key)) {
      newSelectedValues = selectedValues.filter(
        (selected) => selected.key !== key
      );
    } else {
      newSelectedValues = [...selectedValues, { key, value }];
    }
    setSelectedValues(newSelectedValues);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node) &&
      dropdownListRef.current &&
      !dropdownListRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const handleClearAll = () => {
    setSelectedValues([]);
  };

  const updateDropdownPosition = () => {
    if (dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setDropdownStyles({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  };

  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();
      window.addEventListener("scroll", updateDropdownPosition, true);
      window.addEventListener("resize", updateDropdownPosition);
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }
    return () => {
      window.removeEventListener("scroll", updateDropdownPosition, true);
      window.removeEventListener("resize", updateDropdownPosition);
    };
  }, [isOpen]);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <Box sx={{ position: "relative" }} ref={dropdownRef}>
      {label && <CustomLabel label={label || ""} isRequired={isRequired} />}

      {/* Dropdown Trigger */}
      <FormControl fullWidth error={hasError}>
        <Box
          onClick={() => setIsOpen(!isOpen)}
          sx={{
            border: hasError ? "1px solid #f87171" : "1px solid #d1d5db",
            borderRadius: 1,
            minHeight: "42px",
            padding: "8px 12px",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            backgroundColor: "white",
            "&:hover": {
              borderColor: hasError ? "#f87171" : "#9ca3af",
            },
            "&:focus-within": {
              borderColor: "#2563eb",
              boxShadow: "0 0 0 1px #2563eb",
            },
          }}
        >
          <Box sx={{ flex: 1, overflow: "hidden" }}>
            {selectedValues.length > 0 ? (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {optionsToUse
                  .filter((option) =>
                    selectedValues.some(
                      (selected) => selected.key === option.key
                    )
                  )
                  .map((option) => (
                    <Chip
                      key={option.key}
                      label={option.value}
                      size="small"
                      onDelete={(e) => {
                        e.stopPropagation();
                        handleSelect(option.key, option.value);
                      }}
                      deleteIcon={<CloseIcon />}
                      sx={{
                        backgroundColor: "#f3f4f6",
                        "& .MuiChip-deleteIcon": {
                          fontSize: "14px",
                        },
                      }}
                    />
                  ))}
              </Box>
            ) : (
              <Typography variant="body2" sx={{ color: "#9ca3af" }}>
                {placeholder}
              </Typography>
            )}
          </Box>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            {selectedValues.length > 0 && (
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearAll();
                }}
                sx={{
                  mr: 0.5,
                  color: "#9ca3af",
                  "&:hover": { color: "#6b7280" },
                }}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            )}
            <ExpandMoreIcon
              sx={{
                color: "#334155",
                transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
                transition: "transform 0.2s ease-in-out",
              }}
            />
          </Box>
        </Box>

        {/* Error Message Display */}
        {hasError && errorMessage && (
          <FormHelperText error sx={{ mt: 0.5, mx: 1.75 }}>
            {errorMessage}
          </FormHelperText>
        )}
      </FormControl>

      {/* Dropdown Menu */}
      {isOpen &&
        ReactDOM.createPortal(
          <Paper
            ref={dropdownListRef}
            elevation={3}
            sx={{
              position: "fixed",
              top: dropdownStyles.top,
              left: dropdownStyles.left,
              width: dropdownStyles.width,
              zIndex: 9999,
              mt: 0.5,
              maxHeight: "240px",
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
            }}
          >
            {/* Search input */}
            <Box sx={{ p: 2, borderBottom: "1px solid #e5e7eb" }}>
              <TextField
                inputRef={searchInputRef}
                size="small"
                fullWidth
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ fontSize: "16px", color: "#9ca3af" }} />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={handleClearSearch}
                        sx={{
                          color: "#9ca3af",
                          "&:hover": { color: "#6b7280" },
                        }}
                      >
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      border: "none",
                    },
                  },
                }}
              />
            </Box>

            {/* Options list */}
            <Box sx={{ overflow: "auto", flex: 1 }}>
              {filteredOptions.length > 0 ? (
                <List dense disablePadding>
                  {selectedOptions.length > 0 && (
                    <>
                      {/* Selected options section */}
                      <Box
                        sx={{
                          px: 2,
                          py: 1,
                          backgroundColor: "#f8fafc",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        <Typography
                          variant="caption"
                          sx={{
                            color: "#64748b",
                            fontWeight: 600,
                            textTransform: "uppercase",
                            letterSpacing: "0.05em",
                          }}
                        >
                          Selected ({selectedOptions.length})
                        </Typography>
                      </Box>
                      {selectedOptions.map((option) => (
                        <ListItem key={`selected-${option.key}`} disablePadding>
                          <ListItemButton
                            onClick={() =>
                              handleSelect(option.key, option.value)
                            }
                            sx={{
                              py: 1,
                              backgroundColor: "#f0f9ff",
                              borderLeft: "3px solid #0ea5e9",
                              "&:hover": { backgroundColor: "#e0f2fe" },
                            }}
                          >
                            <Checkbox
                              edge="start"
                              checked={true}
                              tabIndex={-1}
                              disableRipple
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <ListItemText
                              primary={option.value}
                              primaryTypographyProps={{
                                variant: "body2",
                                fontWeight: 500,
                                color: "#0369a1",
                              }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </>
                  )}

                  {/* Unselected options section */}
                  {unselectedOptions.length > 0 && (
                    <>
                      {selectedOptions.length > 0 && (
                        <Box
                          sx={{
                            px: 2,
                            py: 1,
                            backgroundColor: "#f8fafc",
                            borderBottom: "1px solid #e2e8f0",
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{
                              color: "#64748b",
                              fontWeight: 600,
                              textTransform: "uppercase",
                              letterSpacing: "0.05em",
                            }}
                          >
                            Available Options
                          </Typography>
                        </Box>
                      )}
                      {unselectedOptions.map((option) => (
                        <ListItem
                          key={`unselected-${option.key}`}
                          disablePadding
                        >
                          <ListItemButton
                            onClick={() =>
                              handleSelect(option.key, option.value)
                            }
                            sx={{
                              py: 1,
                              "&:hover": { backgroundColor: "#f9fafb" },
                            }}
                          >
                            <Checkbox
                              edge="start"
                              checked={false}
                              tabIndex={-1}
                              disableRipple
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <ListItemText
                              primary={option.value}
                              primaryTypographyProps={{
                                variant: "body2",
                                fontWeight: 400,
                              }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </>
                  )}
                </List>
              ) : (
                <Box sx={{ px: 2, py: 1 }}>
                  <Typography variant="body2" sx={{ color: "#6b7280" }}>
                    {searchTerm
                      ? "No matching options found"
                      : "No options available"}
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>,
          document.body
        )}
    </Box>
  );
};

export default MultiSelectDropdown;
