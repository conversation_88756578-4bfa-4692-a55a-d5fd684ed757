/* eslint-disable @typescript-eslint/no-explicit-any */
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import authService from "../../services/auth/authService";
import { apiStatus } from "../../models/apiStatus";
import { ErrorResponseEntity } from "../../models/error-response";

export interface MyApiState {
  data: any;
  status: string;
  error: string | null;
}

const initialState: MyApiState = {
  data: null,
  status: "idle",
  error: null,
};

export const sendVerificationOtp = createAsyncThunk(
  "sendVerificationOtp",

  async (payload: { email: string }) => {
    try {
      const response: any = await authService.sendVerificationOtp(payload);

      if (response?.status >= 400) {
        throw new Error(response?.data?.message);
      }

      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error(
          (error as any)?.data?.message ||
            (error as ErrorResponseEntity)?.body?.message
        );
      }
      throw new Error((error as any)?.data?.message || "Failed");
    }
  }
);

const sendVerificationOtpSlice = createSlice({
  name: "sendVerificationOtp",
  initialState,
  reducers: {
    resetValues: (state) => {
      state.data = null;
      state.status = "idle";
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendVerificationOtp.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(sendVerificationOtp.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(sendVerificationOtp.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "Failed to login";
      });
  },
});

const SendVerifiationOtpReducer = sendVerificationOtpSlice.reducer;
export default SendVerifiationOtpReducer;
export const sendVerificationOtpAction = sendVerificationOtpSlice.actions;
