import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../models/apiStatus";
import authService from "../../services/auth/authService";

export interface getEmailByUuidState {
  data: Record<string, string> | null;
  status: string;
  error: string | null;
}

const initialState: getEmailByUuidState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

interface EmailByUuidResponse {
  data: Record<string, string>;
  message: string | null;
  code: string;
  path: string;
  requestId: string;
  version: string;
}

export const getEmailByUuid = createAsyncThunk(
  "getEmailByUuid",
  async (payload: { uuid: string }) => {
    try {
      const response = await authService.getEmailByUuid(payload);
      const responseData = response as unknown as EmailByUuidResponse;
      return responseData.data;
    } catch (error: any) {
      throw new Error(
        error?.data?.message || error?.message || "Failed to fetch email"
      );
    }
  }
);

const getEmailByUuidReducerSlice = createSlice({
  name: "getEmailByUuid",
  initialState,
  reducers: {
    resetEmailByUuidAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getEmailByUuid.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getEmailByUuid.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getEmailByUuid.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const getEmailByUuidReducer = getEmailByUuidReducerSlice.reducer;
export default getEmailByUuidReducer;
export const getEmailByUuidAction = getEmailByUuidReducerSlice.actions;
