import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import groupService from "../../../services/auth/practice-profile-service/group-servixe";
import { ProviderGroupResponse } from "src/models/providerGroup";

export interface deleteGroupSettingsState {
  data: ContentObject<ProviderGroupResponse> | null;
  status: string;
  error: string | null;
}

const initialState: deleteGroupSettingsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const deleteGroupSettings = createAsyncThunk(
  "DeleteGroupSettings",
  async (payload: { groupId: string }) => {
    try {
      const response: ResponseArrayContentEntity<ProviderGroupResponse> =
        await groupService.deleteGroupSettings(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const deleteGroupSettingsReducerSlice = createSlice({
  name: "DeleteGroupSettings",
  initialState,
  reducers: {
    resetGroupSettingsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(deleteGroupSettings.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(deleteGroupSettings.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(deleteGroupSettings.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const deleteGroupSettingsReducer = deleteGroupSettingsReducerSlice.reducer;
export default deleteGroupSettingsReducer;
export const deleteGroupSettingsAction =
  deleteGroupSettingsReducerSlice.actions;
