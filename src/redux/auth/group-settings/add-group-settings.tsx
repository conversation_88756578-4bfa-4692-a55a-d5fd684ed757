import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import groupService from "../../../services/auth/practice-profile-service/group-servixe";
import { ProviderGroupResponse } from "src/models/providerGroup";

export interface addGroupSettingsState {
  data: ContentObject<ProviderGroupResponse> | null;
  status: string;
  error: string | null;
}

const initialState: addGroupSettingsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const addGroupSettings = createAsyncThunk(
  "AddGroupSettings",
  async (payload: {
    groupName: string;
    groupInitials: string;
    cptCode: string;
    clientUuids: string[];
    clinicianUUID: string;
    familyGroup: boolean;
    billTo: string;
  }) => {
    try {
      const response: ResponseArrayContentEntity<ProviderGroupResponse> =
        await groupService.addGroupSettings(payload);
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const addGroupSettingsReducerSlice = createSlice({
  name: "AddGroupSettings",
  initialState,
  reducers: {
    resetGroupSettingsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addGroupSettings.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addGroupSettings.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addGroupSettings.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const addGroupSettingsReducer = addGroupSettingsReducerSlice.reducer;
export default addGroupSettingsReducer;
export const addGroupSettingsAction = addGroupSettingsReducerSlice.actions;
