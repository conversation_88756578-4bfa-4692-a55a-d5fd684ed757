import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import groupService from "../../../services/auth/practice-profile-service/group-servixe";
import { ProviderGroupResponse } from "src/models/providerGroup";

export interface editGroupSettingsState {
  data: ContentObject<ProviderGroupResponse> | null;
  status: string;
  error: string | null;
}

const initialState: editGroupSettingsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const EditAllGroupSettings = createAsyncThunk(
  "EditAllGroupSettings",
  async (payload: {
    groupName: string;
    groupInitials: string;
    cptCode: string;
    clientUuids: string[];
    uuid?: string;
    billTo?: string;
    clinicianUUID: string;
    familyGroup?: boolean;
  }) => {
    try {
      const response: ResponseArrayContentEntity<ProviderGroupResponse> =
        await groupService.editGroupSettings(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const editGroupSettingsReducerSlice = createSlice({
  name: "EditGroupSettings",
  initialState,
  reducers: {
    resetGroupSettingsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(EditAllGroupSettings.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(EditAllGroupSettings.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(EditAllGroupSettings.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const editGroupSettingsReducer = editGroupSettingsReducerSlice.reducer;
export default editGroupSettingsReducer;
export const editGroupSettingsAction = editGroupSettingsReducerSlice.actions;
