import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ProviderGroupResponse } from "src/models/providerGroup";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import groupService from "../../../services/auth/practice-profile-service/group-servixe";

export interface getAllGroupSettingsState {
  data: ContentObject<ProviderGroupResponse> | null;
  status: string;
  error: string | null;
}

const initialState: getAllGroupSettingsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllGroupSettings = createAsyncThunk(
  "GetAllGroupSettings",
  async (payload: { size: number; page: number; searchString: string }) => {
    try {
      const response: ResponseArrayContentEntity<ProviderGroupResponse> =
        await groupService.getAllGroupSettings(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get group settings");
    }
  }
);

const getAllGroupSettingsReducerSlice = createSlice({
  name: "GetAllGroupSettings",
  initialState,
  reducers: {
    resetGroupSettingsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllGroupSettings.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllGroupSettings.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllGroupSettings.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllGroupSettingsReducer = getAllGroupSettingsReducerSlice.reducer;
export default GetAllGroupSettingsReducer;
export const getAllGroupSettingsAction =
  getAllGroupSettingsReducerSlice.actions;
