import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import groupService from "../../../services/auth/practice-profile-service/group-servixe";
import { ProviderGroupResponse } from "src/models/providerGroup";

export interface getGroupSettingsByIdState {
  data: ContentObject<ProviderGroupResponse> | null;
  status: string;
  error: string | null;
}

const initialState: getGroupSettingsByIdState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getGroupSettingsById = createAsyncThunk(
  "GetGroupSettingsById",
  async (payload: { groupId: string }) => {
    try {
      const response: ResponseArrayContentEntity<ProviderGroupResponse> =
        await groupService.getGroupSettingsById(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const getGroupSettingsByIdReducerSlice = createSlice({
  name: "GetGroupSettingsById",
  initialState,
  reducers: {
    resetGroupSettingsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getGroupSettingsById.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getGroupSettingsById.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getGroupSettingsById.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const getGroupSettingsByIdReducer = getGroupSettingsByIdReducerSlice.reducer;
export default getGroupSettingsByIdReducer;
export const getGroupSettingsByIdAction =
  getGroupSettingsByIdReducerSlice.actions;
