import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface ResendInviteClientClinicianState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ResendInviteClientClinicianState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const resendInviteClientClinician = createAsyncThunk(
  "ResendInviteClientClinician",
  async (payload: { uuid: string, isClient: boolean }) => {
    try {
      const response: ResponseContentEntity<string> =
        await practiceProfileService.resendInviteClient(payload.uuid, payload.isClient);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to resend invite");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);    }
  }
);

const resendInviteClientClinicianReducerSlice = createSlice({
  name: "ResendInviteClientClinician",
  initialState,
  reducers: {
    resetResendInviteClientClinicianReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(resendInviteClientClinician.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(resendInviteClientClinician.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(resendInviteClientClinician.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ResendInviteClientClinicianReducer = resendInviteClientClinicianReducerSlice.reducer;
export default ResendInviteClientClinicianReducer;
export const resendInviteClientClinicianReducerAction =
  resendInviteClientClinicianReducerSlice.actions;
