import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface ChangeUserPasswordState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ChangeUserPasswordState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const changeUserPassword = createAsyncThunk(
  "ChangeUserPassword",
  async (payload: { newPassword: string }) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.changeUserPassword(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to update Staff ");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);    }
  }
);

const changeUserPasswordReducerSlice = createSlice({
  name: "ChangeUserPassword",
  initialState,
  reducers: {
    resetChangeUserPasswordReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changeUserPassword.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(changeUserPassword.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(changeUserPassword.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ChangeUserPasswordReducer = changeUserPasswordReducerSlice.reducer;
export default ChangeUserPasswordReducer;
export const changeUserPasswordReducerAction =
  changeUserPasswordReducerSlice.actions;
