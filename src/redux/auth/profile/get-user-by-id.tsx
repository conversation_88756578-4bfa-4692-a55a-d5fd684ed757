import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface getUserById {
  data: any | null;
  status: string;
  error: string | null;
}

const initialState: getUserById = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getUserById = createAsyncThunk("GetUserProfileById", async () => {
  try {
    const response: ResponseContentEntity<any> =
      await practiceProfileService.getUserById();
    return response;
  } catch (error: unknown) {
    if ((error as ErrorResponseEntity)?.body?.message) {
      throw new Error((error as ErrorResponseEntity).body.message);
    }
    throw new Error("Failed to get User ");
  }
});

const getUserProfileByIdReducerSlice = createSlice({
  name: "GetUserProfileById",
  initialState,
  reducers: {
    resetUserByIdAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getUserById.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getUserById.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload.data;
      })
      .addCase(getUserById.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetUserProfileByIdReducer = getUserProfileByIdReducerSlice.reducer;
export default GetUserProfileByIdReducer;
export const getUserProfileByIdAction = getUserProfileByIdReducerSlice.actions;
