import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface ArchiveContactState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ArchiveContactState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const archiveContact = createAsyncThunk(
  "ArchiveContactReducer",
  async (payload: { uuid: string; archiveStatus: boolean }) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.archiveContact(
          payload.uuid,
          payload.archiveStatus
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to archive contact");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const archiveContactReducerSlice = createSlice({
  name: "ArchiveContactReducer",
  initialState,
  reducers: {
    resetArchiveContactReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(archiveContact.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(archiveContact.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(archiveContact.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ArchiveContactReducer = archiveContactReducerSlice.reducer;
export default ArchiveContactReducer;
export const archiveContactReducerAction = archiveContactReducerSlice.actions;
