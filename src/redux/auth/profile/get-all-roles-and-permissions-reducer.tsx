import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";
import { RolesAndPermissionsData } from "src/models/all-const";
export interface getAllRolesAndPermissionsState {
  data: ContentObject<RolesAndPermissionsData> | null;
  status: string;
  error: string | null;
}

const initialState: getAllRolesAndPermissionsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllRolesAndPermissions = createAsyncThunk(
  "GetAllRolesAndPermissions",
  async () => {
    try {
      const response: ResponseArrayContentEntity<RolesAndPermissionsData> =
        await practiceProfileService.getAllRolesAndPermissions();
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get all roles and permissions");
    }
  }
);

const getAllRolesAndPermissionsReducerSlice = createSlice({
  name: "GetAllRolesAndPermissions",
  initialState,
  reducers: {
    resetRolesAndPermissionsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllRolesAndPermissions.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllRolesAndPermissions.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllRolesAndPermissions.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllRolesAndPermissionsReducer =
  getAllRolesAndPermissionsReducerSlice.reducer;
export default GetAllRolesAndPermissionsReducer;
export const getAllRolesAndPermissionsAction =
  getAllRolesAndPermissionsReducerSlice.actions;
