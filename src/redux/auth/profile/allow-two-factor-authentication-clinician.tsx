import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService, {
  ChangeTwoFactorAuthenticationPayload,
} from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface ChangeTwoFactorAuthenticationClinicianState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ChangeTwoFactorAuthenticationClinicianState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const changeTwoFactorAuthenticationClinician = createAsyncThunk(
  "ChangeTwoFactorAuthenticationClinicianReducer",
  async (payload: ChangeTwoFactorAuthenticationPayload) => {
    try {
      const response: ResponseContentEntity<ChangeTwoFactorAuthenticationPayload> =
        await practiceProfileService.changeTwoFactorAuthentication(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update clinician");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const changeTwoFactorAuthenticationClinicianReducerSlice = createSlice({
  name: "ChangeTwoFactorAuthenticationClinicianReducer",
  initialState,
  reducers: {
    resetChangeTwoFactorAuthenticationReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changeTwoFactorAuthenticationClinician.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(
        changeTwoFactorAuthenticationClinician.fulfilled,
        (state, action) => {
          state.status = apiStatus.SUCCEEDED;
          state.data = action.payload;
        }
      )
      .addCase(
        changeTwoFactorAuthenticationClinician.rejected,
        (state, action) => {
          state.status = apiStatus.FAILED;
          state.error = action.error.message ?? "An error occurred";
        }
      );
  },
});

const ChangeTwoFactorAuthenticationClinicianReducer =
  changeTwoFactorAuthenticationClinicianReducerSlice.reducer;
export default ChangeTwoFactorAuthenticationClinicianReducer;
export const changeTwoFactorAuthenticationClinicianReducerAction =
  changeTwoFactorAuthenticationClinicianReducerSlice.actions;
