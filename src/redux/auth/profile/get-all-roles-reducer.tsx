import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";
import { PermissionGroup } from "src/models/all-const";
export interface getAllRolesState {
  data: ContentObject<PermissionGroup> | null;
  status: string;
  error: string | null;
}

const initialState: getAllRolesState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllRoles = createAsyncThunk("GetAllRoles", async () => {
  try {
    const response: ResponseArrayContentEntity<PermissionGroup> =
      await practiceProfileService.getAllPermissions();
    return response.data;
  } catch (error: unknown) {
    if ((error as ErrorResponseEntity)?.body?.message) {
      throw new Error((error as ErrorResponseEntity).body.message);
    }
    throw new Error("Failed to get all roles");
  }
});

const getAllRolesReducerSlice = createSlice({
  name: "GetAllRoles",
  initialState,
  reducers: {
    resetRolesAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllRoles.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllRoles.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllRoles.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllRolesReducer = getAllRolesReducerSlice.reducer;
export default GetAllRolesReducer;
export const getAllRolesAction = getAllRolesReducerSlice.actions;
