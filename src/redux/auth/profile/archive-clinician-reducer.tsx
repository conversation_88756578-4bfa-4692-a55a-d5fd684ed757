import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface ArchiveClinicianState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ArchiveClinicianState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const archiveClinician = createAsyncThunk(
  "ArchiveClinicianReducer",
  async (payload: { uuid: string; archiveStatus: boolean }) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.archiveClinician(
          payload.uuid,
          payload.archiveStatus
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to archive clinician");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const archiveClinicianReducerSlice = createSlice({
  name: "ArchiveClinicianReducer",
  initialState,
  reducers: {
    resetArchiveClinicianReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(archiveClinician.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(archiveClinician.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(archiveClinician.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ArchiveClinicianReducer = archiveClinicianReducerSlice.reducer;
export default ArchiveClinicianReducer;
export const archiveClinicianReducerAction =
  archiveClinicianReducerSlice.actions;
