import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface EditRolesAndPermissionsState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: EditRolesAndPermissionsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const editRolesAndPermissions = createAsyncThunk(
  "EditRolesAndPermissionsReducer",
  async (payload: { clinicianId: string; roleName: string; flag: boolean }) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.editRolesAndPermissions(
          payload.clinicianId,
          payload.roleName,
          payload.flag
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update practice");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);    }
  }
);

const editRolesAndPermissionsReducerSlice = createSlice({
  name: "EditRolesAndPermissionsReducer",
  initialState,
  reducers: {
    resetEditRolesAndPermissionsReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(editRolesAndPermissions.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(editRolesAndPermissions.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(editRolesAndPermissions.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const EditRolesAndPermissionsReducer =
  editRolesAndPermissionsReducerSlice.reducer;
export default EditRolesAndPermissionsReducer;
export const editRolesAndPermissionsReducerAction =
  editRolesAndPermissionsReducerSlice.actions;
