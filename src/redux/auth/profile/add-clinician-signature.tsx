import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";

export interface AddClinicianSignature {
  data: string | null;
  status: string;
  error: string | null;
}

export interface AddClinicianSignaturePayload {
  clinicianId: string;
  base64: string;
}

const initialState: AddClinicianSignature = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const addClinicianSignature = createAsyncThunk(
  "AddClinicianSignatureReducer",

  async (payload: AddClinicianSignaturePayload) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.addUserSignature(
          payload.clinicianId,
          payload.base64
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to update Signature");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const addClinicianSignatureReducerSlice = createSlice({
  name: "AddClinicianSignatureReducer",
  initialState,
  reducers: {
    resetAddClinicianSignatureReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addClinicianSignature.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addClinicianSignature.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addClinicianSignature.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const AddClinicianSignatureReducer = addClinicianSignatureReducerSlice.reducer;
export default AddClinicianSignatureReducer;
export const addClinicianSignatureReducerAction =
  addClinicianSignatureReducerSlice.actions;
