import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import availabilityService from "../../../services/auth/practice-profile-service/availability-service";

interface ApiError {
  data: {
    message: string;
    code: string;
  };
}

export interface AddAvailabilityState {
  data: any | null;
  status: string;
  error: string | null;
}

const initialState: AddAvailabilityState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

// Async thunk for adding availability
export const addAvailability = createAsyncThunk(
  "AddAvailabilityReducer",
  async (payload: any) => {
    // Assuming payload is 'any'
    try {
      const response: ResponseContentEntity<any> =
        await availabilityService.addAvailabiityService(payload); // Corrected service call

      if (response?.code === "BAD_REQUEST" || response?.code === "ERROR") {
        throw new Error(response.message || "Failed to add availability");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ApiError)?.data?.message) {
        throw new Error((error as ApiError).data.message);
      } else if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      } else if ((error as Error)?.message) {
        throw new Error((error as Error).message);
      }
      throw new Error("Failed to add availability");
    }
  }
);

const addAvailabilityReducerSlice = createSlice({
  name: "AddAvailabilityReducer",
  initialState,
  reducers: {
    // Reset action for the reducer state
    resetAddAvailabilityReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addAvailability.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addAvailability.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addAvailability.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const AddAvailabilityReducer = addAvailabilityReducerSlice.reducer;
export default AddAvailabilityReducer;
export const addAvailabilityReducerAction = addAvailabilityReducerSlice.actions;
