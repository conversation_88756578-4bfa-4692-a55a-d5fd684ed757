import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import availabilityService from "../../../services/auth/practice-profile-service/availability-service";


export interface getAvailabilityByClinicianIdState {
  data: any | null; // Assuming a similar data structure for availability
  status: string;
  error: string | null;
}

const initialState: getAvailabilityByClinicianIdState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAvailabilityByClinicianId = createAsyncThunk(
  "GetAvailabilityByClinicianId",
  async (payload: any | string) => {
    // Adjust payload type if necessary
    try {
      let uuid: string;
      if (typeof payload === "string") {
        uuid = payload;
      } else {
        uuid = payload.uuid; // Adjust property name if necessary
      }

      const response: ResponseContentEntity<any> = // Adjust response type if necessary
        await availabilityService.getAvailabilityById(uuid); // Adjust service method name
      return response;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get availability by clinician id"); // Adjust error message
    }
  }
);

const getAvailabilityByClinicianIdReducerSlice = createSlice({
  name: "GetAvailabilityByClinicianId",
  initialState,
  reducers: {
    resetAvailabilityAction: (state) => {
      // Adjust reset action name
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAvailabilityByClinicianId.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAvailabilityByClinicianId.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload.data;
      })
      .addCase(getAvailabilityByClinicianId.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAvailabilityByClinicianIdReducer =
  getAvailabilityByClinicianIdReducerSlice.reducer;
export default GetAvailabilityByClinicianIdReducer;
export const getAvailabilityByClinicianIdAction =
  getAvailabilityByClinicianIdReducerSlice.actions;
