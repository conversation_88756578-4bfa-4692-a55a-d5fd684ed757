/* eslint-disable @typescript-eslint/no-explicit-any */
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../models/apiStatus";
import { ErrorResponseEntity } from "../../models/error-response";
import authService from "../../services/auth/authService";

export interface MyApiState {
  data: any;
  status: string;
  error: string | null;
}

const initialState: MyApiState = {
  data: null,
  status: "idle",
  error: null,
};

export const logout = createAsyncThunk("Logout", async () => {
  try {
    const response: any = await authService.logout();

    if (response?.status >= 400) {
      throw new Error(response?.data?.message);
    }

    return response.data;
  } catch (error: unknown) {
    if ((error as ErrorResponseEntity)?.body?.message) {
      throw new Error(
        (error as any)?.data?.message ||
          (error as ErrorResponseEntity).body.message
      );
    }
    throw new Error((error as any)?.data?.message || "Failed to logout");
  }
});

const logoutSlice = createSlice({
  name: "Logout",
  initialState,
  reducers: {
    resetValues: (state) => {
      state.data = null;
      state.status = "idle";
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(logout.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(logout.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(logout.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "Failed to logout";
      });
  },
});

const logoutReducer = logoutSlice.reducer;
export default logoutReducer;
export const logoutAction = logoutSlice.actions;
