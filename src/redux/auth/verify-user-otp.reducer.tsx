/* eslint-disable @typescript-eslint/no-explicit-any */
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import authService from "../../services/auth/authService";
import { apiStatus } from "../../models/apiStatus";
import { ErrorResponseEntity } from "../../models/error-response";

export interface MyApiState {
  data: any;
  status: string;
  error: string | null;
}

const initialState: MyApiState = {
  data: null,
  status: "idle",
  error: null,
};

export const verifyUserOtp = createAsyncThunk(
  "verifyUserOtp",

  async (payload: { otpId: string; otp: string; fromForgotPassword?: boolean }) => {
    try {
      const response: any = await authService.verifyUserOtp(payload);

      if (response?.status >= 400) {
        throw new Error(response?.data?.message);
      }

      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error(
          (error as any)?.data?.message ||
            (error as ErrorResponseEntity)?.body?.message
        );
      }
      throw new Error((error as any)?.data?.message || "Failed to login");
    }
  }
);

const verifyUserOtpSlice = createSlice({
  name: "verifyUserOtp",
  initialState,
  reducers: {
    resetValues: (state) => {
      state.data = null;
      state.status = "idle";
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(verifyUserOtp.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(verifyUserOtp.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(verifyUserOtp.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "Failed to login";
      });
  },
});

const VerifyUserOtpReducer = verifyUserOtpSlice.reducer;
export default VerifyUserOtpReducer;
export const verifyUserOtpAction = verifyUserOtpSlice.actions;
