import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

export interface ChangeTwoFactorAuthenticationPayload {
  clientUuid: string;
  flag: boolean;
}

export interface ChangeTwoFactorAuthenticationState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ChangeTwoFactorAuthenticationState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const changeTwoFactorAuthentication = createAsyncThunk(
  "ChangeTwoFactorAuthenticationReducer",
  async (payload: ChangeTwoFactorAuthenticationPayload) => {
    try {
      const response: ResponseContentEntity<ChangeTwoFactorAuthenticationPayload> =
        await patientService.changeTwoFactorAuthentication(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update clinician");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const changeTwoFactorAuthenticationReducerSlice = createSlice({
  name: "ChangeTwoFactorAuthenticationReducer",
  initialState,
  reducers: {
    resetChangeTwoFactorAuthenticationReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changeTwoFactorAuthentication.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(changeTwoFactorAuthentication.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(changeTwoFactorAuthentication.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ChangeTwoFactorAuthenticationReducer =
  changeTwoFactorAuthenticationReducerSlice.reducer;
export default ChangeTwoFactorAuthenticationReducer;
export const changeTwoFactorAuthenticationReducerAction =
  changeTwoFactorAuthenticationReducerSlice.actions;
