import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { PatientData } from "../../../models/all-const";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";
import { apiStatus } from "../../../models/apiStatus";

export interface getPatientByIdState {
  data: PatientData | null;
  status: string;
  error: string | null;
}

const initialState: getPatientByIdState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getPatientById = createAsyncThunk(
  "GetPatientById",
  async (payload: string) => {
    try {
      const response: ResponseContentEntity<PatientData> =
        await patientService.getPatientById(payload);
      return response;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get patient by id");
    }
  }
);

const getPatientByIdReducerSlice = createSlice({
  name: "GetPatientById",
  initialState,
  reducers: {
    resetCliniciansAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPatientById.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getPatientById.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload.data;
      })
      .addCase(getPatientById.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetPatientByIdReducer = getPatientByIdReducerSlice.reducer;
export default GetPatientByIdReducer;
export const getPatientByIdAction = getPatientByIdReducerSlice.actions;
