import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

export interface ChangePrimaryClinicianPayload {
  clientUuid: string;
  existingClinicianId: string;
  newClinicianId: string;
}

export interface ChangePrimaryClinicianState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ChangePrimaryClinicianState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const changePrimaryClinician = createAsyncThunk(
  "ChangePrimaryClinicianReducer",
  async (payload: ChangePrimaryClinicianPayload) => {
    try {
      const response: ResponseContentEntity<null> =
        await patientService.changePrimaryClinician(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update clinician");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const changePrimaryClinicianReducerSlice = createSlice({
  name: "ChangePrimaryClinicianReducer",
  initialState,
  reducers: {
    resetChangePrimaryClinicianReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changePrimaryClinician.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(changePrimaryClinician.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(changePrimaryClinician.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ChangePrimaryClinicianReducer =
  changePrimaryClinicianReducerSlice.reducer;
export default ChangePrimaryClinicianReducer;
export const changePrimaryClinicianReducerAction =
  changePrimaryClinicianReducerSlice.actions;
