import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseArrayContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

// Insurance data interface
export interface InsuranceData {
  id?: string;
  insuranceType: string;
  planName: string;
  planType: string;
  relationship: string;
  name: string;
  dob: string;
  gender: string;
  memberId: string;
  groupNumber?: string;
  payerId?: string;
  insurancePhone?: string;
  address?: string;
  isPrimary?: boolean;
  clientId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface GetClientInsuranceState {
  data: InsuranceData[] | null;
  status: string;
  error: string | null;
}

const initialState: GetClientInsuranceState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getClientInsurance = createAsyncThunk(
  "GetClientInsurance",
  async (clientId: string) => {
    try {
      const response: ResponseArrayContentEntity<InsuranceData[]> =
        await patientService.getClientInsurance(clientId);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.status === 404) {
        return [] as InsuranceData[];
      }
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message || "Failed to get client insurance");
    }
  }
);

const getClientInsuranceReducerSlice = createSlice({
  name: "GetClientInsurance",
  initialState,
  reducers: {
    resetClientInsurance: () => initialState,
    clearInsuranceError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getClientInsurance.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.error = null;
      })
      .addCase(getClientInsurance.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload as InsuranceData[];
      })
      .addCase(getClientInsurance.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetClientInsuranceReducer = getClientInsuranceReducerSlice.reducer;
export default GetClientInsuranceReducer;
export const getClientInsuranceAction = getClientInsuranceReducerSlice.actions;
