import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import patientService from "../../../services/auth/practice-profile-service/patient-service";
export interface getAllPatientsState {
  data: ContentObject<any> | null;
  status: string;
  error: string | null;
}

const initialState: getAllPatientsState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllPatients = createAsyncThunk(
  "GetAllPatients",
  async (payload: any) => {
    try {
      const response: ResponseArrayContentEntity<any> =
        await patientService.getAllPatients(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const getAllPatientsReducerSlice = createSlice({
  name: "GetAllPatients",
  initialState,
  reducers: {
    resetPatientsAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllPatients.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllPatients.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllPatients.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllPatientsReducer = getAllPatientsReducerSlice.reducer;
export default GetAllPatientsReducer;
export const getAllPatientsAction = getAllPatientsReducerSlice.actions;
