import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import practiceProfileService from "../../../services/auth/practice-profile-service/practice-profile-service";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import { PatientTypes } from "src/models/providerGroup";
export interface EditPatientState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: EditPatientState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const editPatient = createAsyncThunk(
  "EditPatientReducer",
  async (payload: PatientTypes) => {
    try {
      const response: ResponseContentEntity<null> =
        await practiceProfileService.editPatientProfile(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(
          response?.message || "Failed to update patient profile"
        );
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const editPatientReducerSlice = createSlice({
  name: "EditPatientReducer",
  initialState,
  reducers: {
    resetEditPatientReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(editPatient.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(editPatient.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(editPatient.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const EditPatientReducer = editPatientReducerSlice.reducer;
export default EditPatientReducer;
export const editPatientReducerAction = editPatientReducerSlice.actions;
