import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { PatientData } from "../../../models/all-const";
import { apiStatus } from "../../../models/apiStatus";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

export interface getPatientByIdState {
  data: PatientData | null;
  status: string;
  error: string | null;
}

const initialState: getPatientByIdState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getPatientById = createAsyncThunk(
  "GetPatientById",
  async (clientId: string) => {
    try {
      const response: ResponseContentEntity<PatientData> =
        await patientService.getPatientByUuid(clientId);
      return response;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.status === 404) {
        return {
          data: null,
        } as unknown as ResponseContentEntity<PatientData>;
      }
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get patient by id");
    }
  }
);

const getPatientByIdReducerSlice = createSlice({
  name: "GetPatientById",
  initialState,
  reducers: {
    resetGetPatientById: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPatientById.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.data = null;
        state.error = null;
      })
      .addCase(getPatientById.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload.data as PatientData;
      })
      .addCase(getPatientById.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetPatientByIdReducer = getPatientByIdReducerSlice.reducer;
export default GetPatientByIdReducer;
export const getPatientByIdAction = getPatientByIdReducerSlice.actions;
