import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

// Update insurance data interface
export interface UpdateInsuranceData {
  uuid: string;
  insuranceName: string;
  memberId: string;
  groupId: string;
  startDate: string;
  endDate: string;
  relationship: string;
  subscriberFirstName: string;
  subscriberLastName: string;
  subscriberBirthDate: string;
  insuranceType: "PRIMARY" | "SECONDARY" | "OTHER";
  insuranceCardFront?: string;
  insuranceCardBack?: string;
}

export interface UpdateClientInsuranceState {
  data: UpdateInsuranceData | null;
  status: string;
  error: string | null;
}

const initialState: UpdateClientInsuranceState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const updateClientInsurance = createAsyncThunk(
  "UpdateClientInsurance",
  async ({ clientId, insuranceData }: { clientId: string; insuranceData: UpdateInsuranceData }) => {
    try {
      const response: ResponseContentEntity<UpdateInsuranceData> =
        await patientService.updateClientInsurance(clientId, insuranceData);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message ?? "Failed to update client insurance");
    }
  }
);

const updateClientInsuranceReducerSlice = createSlice({
  name: "UpdateClientInsurance",
  initialState,
  reducers: {
    resetUpdateClientInsurance: () => initialState,
    clearUpdateInsuranceError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateClientInsurance.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.error = null;
      })
      .addCase(updateClientInsurance.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload as UpdateInsuranceData;
      })
      .addCase(updateClientInsurance.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const UpdateClientInsuranceReducer = updateClientInsuranceReducerSlice.reducer;
export default UpdateClientInsuranceReducer;
export const updateClientInsuranceAction = updateClientInsuranceReducerSlice.actions;
