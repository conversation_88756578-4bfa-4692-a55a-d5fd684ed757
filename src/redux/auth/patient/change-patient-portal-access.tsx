import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

export interface ChangePortalAccessPayload {
  clientUuid: string;
  portalAccess: boolean;
  flag?: string;
}

export interface ChangePortalAccessState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ChangePortalAccessState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const changePortalAccess = createAsyncThunk(
  "ChangePortalAccessReducer",
  async (payload: ChangePortalAccessPayload) => {
    try {
      const response: ResponseContentEntity<ChangePortalAccessPayload> =
        await patientService.changePortalAccess(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update clinician");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const changePortalAccessReducerSlice = createSlice({
  name: "ChangePortalAccessReducer",
  initialState,
  reducers: {
    resetChangePortalAccessReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changePortalAccess.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(changePortalAccess.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(changePortalAccess.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ChangePortalAccessReducer = changePortalAccessReducerSlice.reducer;
export default ChangePortalAccessReducer;
export const changePortalAccessReducerAction =
  changePortalAccessReducerSlice.actions;
