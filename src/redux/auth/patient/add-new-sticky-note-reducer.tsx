import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";

export interface AddStickyNotesState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: AddStickyNotesState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export interface StickyNotePayload {
  uuid: string | null;
  note?: string;
  alertNote?: string;
  clientId?: string;
}

export const addStickyNotes = createAsyncThunk(
  "AddStickyNotesReducer",

  async (payload: StickyNotePayload) => {
    try {
      const response: ResponseContentEntity<StickyNotePayload> =
        await patientService.addStickyNotes(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to update Patient");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }

      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const addStickyNotesReducerSlice = createSlice({
  name: "AddStickyNotesReducer",
  initialState,
  reducers: {
    resetAddStickyNotesReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addStickyNotes.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addStickyNotes.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addStickyNotes.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const AddStickyNotesReducer = addStickyNotesReducerSlice.reducer;
export default AddStickyNotesReducer;
export const addStickyNotesReducerAction = addStickyNotesReducerSlice.actions;
