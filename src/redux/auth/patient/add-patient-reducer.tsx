import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import patientService from "../../../services/auth/practice-profile-service/patient-service";
import { AllTypes } from "src/models/all-const";

export interface AddPatientState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: AddPatientState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const addPatient = createAsyncThunk(
  "AddPatientReducer",

  async (payload: AllTypes) => {
    try {
      const response: ResponseContentEntity<null> =
        await patientService.addPatient(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error("Failed to update Patient");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }

      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const addPatientReducerSlice = createSlice({
  name: "AddPatientReducer",
  initialState,
  reducers: {
    resetAddPatientReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addPatient.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addPatient.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addPatient.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const AddPatientReducer = addPatientReducerSlice.reducer;
export default AddPatientReducer;
export const addPatientReducerAction = addPatientReducerSlice.actions;
