import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import patientService from "../../../services/auth/practice-profile-service/patient-service";
import { PatientData } from "../../../models/all-const";
import { StickyNotePayload } from "./add-new-sticky-note-reducer";

export interface getStickyNotesState {
  data: PatientData | null;
  status: string;
  error: string | null;
}

const initialState: getStickyNotesState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getStickyNotes = createAsyncThunk(
  "GetStickyNotes",
  async (clientId: string) => {
    try {
      const response: ResponseContentEntity<StickyNotePayload> =
        await patientService.getStickyNotes(clientId);
      return response;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.status === 404) {
        return {
          data: null,
        } as unknown as ResponseContentEntity<StickyNotePayload>;
      }
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get note ");
    }
  }
);

const getStickyNotesReducerSlice = createSlice({
  name: "GetStickyNotes",
  initialState,
  reducers: {
    resetGetStickyNotes: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getStickyNotes.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.data = null;
        state.error = null;
      })
      .addCase(getStickyNotes.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload.data as PatientData;
      })
      .addCase(getStickyNotes.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetStickyNotesReducer = getStickyNotesReducerSlice.reducer;
export default GetStickyNotesReducer;
export const getStickyNotesAction = getStickyNotesReducerSlice.actions;
