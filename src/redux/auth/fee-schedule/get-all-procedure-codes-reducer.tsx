import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
import { ProcedureCode } from "src/models/all-const";
export interface getAllProcedureCodesState {
  data: ContentObject<ProcedureCode> | null;
  status: string;
  error: string | null;
}

const initialState: getAllProcedureCodesState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllProcedureCodes = createAsyncThunk(
  "GetAllProcedureCodes",
  async () => {
    try {
      const response: ResponseArrayContentEntity<ProcedureCode> =
        await feeScheduleService.getAllProcedureCodes();
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {  
        throw new Error((error as ErrorResponseEntity).body.message); 
      }
      throw new Error("Failed to get all procedure codes");
    }
  }
);

const getAllProcedureCodesReducerSlice = createSlice({
  name: "GetAllProcedureCodes",
  initialState,
  reducers: {
    resetProcedureCodesAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllProcedureCodes.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllProcedureCodes.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllProcedureCodes.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllProcedureCodesReducer = getAllProcedureCodesReducerSlice.reducer;
export default GetAllProcedureCodesReducer;
export const getAllProcedureCodesAction =
  getAllProcedureCodesReducerSlice.actions;
