import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";

export interface EditFeeScheduleStatusState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: EditFeeScheduleStatusState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const editFeeScheduleStatus = createAsyncThunk(
  "EditFeeScheduleStatusReducer",
  async (payload: { uuid: string; flag: boolean }) => {
    try {
      const response: ResponseContentEntity<null> =
        await feeScheduleService.editFeeScheduleStatus(
          payload.uuid,
          payload.flag
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(
          response?.message || "Failed to update clinician status"
        );
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message);
    }
  }
);

const editFeeScheduleStatusReducerSlice = createSlice({
  name: "EditFeeScheduleStatusReducer",
  initialState,
  reducers: {
    resetEditFeeScheduleStatusReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(editFeeScheduleStatus.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(editFeeScheduleStatus.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(editFeeScheduleStatus.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const EditFeeScheduleStatusReducer = editFeeScheduleStatusReducerSlice.reducer;
export default EditFeeScheduleStatusReducer;
export const editFeeScheduleStatusReducerAction =
  editFeeScheduleStatusReducerSlice.actions;
