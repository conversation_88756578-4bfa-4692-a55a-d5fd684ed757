import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
import { ProcedureCode } from "../../../models/all-const";
// Define a specific type for FeeSchedule
export interface FeeSchedulePayload {
  procedureCode: string;
  rate: string;
  codeType: string;
}

interface ApiError {
  data: {
    message: string;
    code: string;
  };
}

export interface AddFeeScheduleState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: AddFeeScheduleState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const addFeeSchedule = createAsyncThunk(
  "AddFeeScheduleReducer",
  async (payload: ProcedureCode) => {
    try {
      const response: ResponseContentEntity<ProcedureCode> =
        await feeScheduleService.addFeeSchedule(payload);

      if (response?.code === "BAD_REQUEST" || response?.code === "ERROR") {
        throw new Error(response.message || "Failed to update fee schedule");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ApiError)?.data?.message) {
        throw new Error((error as ApiError).data.message);
      } else if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      } else if ((error as Error)?.message) {
        throw new Error((error as Error).message);
      }
      throw new Error("Failed to update fee schedule");
    }
  }
);

const addFeeScheduleReducerSlice = createSlice({
  name: "AddFeeScheduleReducer",
  initialState,
  reducers: {
    resetAddFeeScheduleReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addFeeSchedule.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(addFeeSchedule.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(addFeeSchedule.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const AddFeeScheduleReducer = addFeeScheduleReducerSlice.reducer;
export default AddFeeScheduleReducer;
export const addFeeScheduleReducerAction = addFeeScheduleReducerSlice.actions;
