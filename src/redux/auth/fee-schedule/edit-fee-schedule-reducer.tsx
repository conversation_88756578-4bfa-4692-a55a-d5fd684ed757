import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import { ProcedureCode } from "src/models/all-const";
export interface EditFeeScheduleState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: EditFeeScheduleState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const editFeeSchedule = createAsyncThunk(
  "EditFeeScheduleReducer",
  async (payload: ProcedureCode) => {
    try {
      const response: ResponseContentEntity<ProcedureCode> =
        await feeScheduleService.updateFeeSchedule(payload);
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update fee schedule");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to update fee schedule");
    }
  }
);

const editFeeScheduleReducerSlice = createSlice({
  name: "EditFeeScheduleReducer",
  initialState,
  reducers: {
    resetEditFeeScheduleReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(editFeeSchedule.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(editFeeSchedule.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(editFeeSchedule.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const EditFeeScheduleReducer = editFeeScheduleReducerSlice.reducer;
export default EditFeeScheduleReducer;
export const editFeeScheduleReducerAction = editFeeScheduleReducerSlice.actions;
