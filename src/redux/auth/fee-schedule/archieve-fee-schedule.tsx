import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { apiStatus } from "../../../models/apiStatus";

import { ErrorResponseEntity } from "../../../models/error-response";
import { ResponseContentEntity } from "../../../models/response-content-entity";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
export interface ArchiveFeeScheduleState {
  data: string | null;
  status: string;
  error: string | null;
}

const initialState: ArchiveFeeScheduleState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const archiveFeeSchedule = createAsyncThunk(
  "ArchiveFeeScheduleReducer",
  async (payload: { uuid: string; flag: boolean }) => {
    try {
      const response: ResponseContentEntity<null> =
        await feeScheduleService.archiveFeeSchedule(
          payload.uuid,
          payload.flag
        );
      const statusCode = parseInt(response?.code || "0", 10);
      if (statusCode >= 400) {
        throw new Error(response?.message || "Failed to update fee schedule");
      }
      return response?.message;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to update fee schedule");
    }
  }
);

const archiveFeeScheduleReducerSlice = createSlice({
  name: "ArchiveFeeScheduleReducer",
  initialState,
  reducers: {
    resetArchiveFeeScheduleReducer: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(archiveFeeSchedule.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(archiveFeeSchedule.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(archiveFeeSchedule.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const ArchiveFeeScheduleReducer = archiveFeeScheduleReducerSlice.reducer;
export default ArchiveFeeScheduleReducer;
export const archiveFeeScheduleReducerAction =
  archiveFeeScheduleReducerSlice.actions;
