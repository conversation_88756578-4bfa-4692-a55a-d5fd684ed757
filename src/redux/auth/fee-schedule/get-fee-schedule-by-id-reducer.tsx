import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
import { ProcedureCode } from "src/models/all-const";

export interface getFeeScheduleByIdState {
  data: ProcedureCode | null;
  status: string;
  error: string | null;
}

const initialState: getFeeScheduleByIdState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getFeeScheduleById = createAsyncThunk(
  "GetFeeScheduleById",
  async (id: string) => {
    try {
      const response: ResponseContentEntity<ProcedureCode> =
        await feeScheduleService.getFeeScheduleById(id);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get fee schedule");
    }
  }
);

const getFeeScheduleByIdReducerSlice = createSlice({
  name: "GetFeeScheduleById",
  initialState,
  reducers: {
    resetFeeScheduleByIdAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getFeeScheduleById.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getFeeScheduleById.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getFeeScheduleById.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetFeeScheduleByIdReducer = getFeeScheduleByIdReducerSlice.reducer;
export default GetFeeScheduleByIdReducer;
export const getFeeScheduleByIdAction = getFeeScheduleByIdReducerSlice.actions;
