import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import {
  ContentObject,
  ResponseArrayContentEntity,
} from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import feeScheduleService from "../../../services/auth/practice-profile-service/fee-schedule-service";
import { ProcedureCode } from "src/models/all-const";
export interface getAllFeeScheduleState {
  data: ContentObject<ProcedureCode> | null;
  status: string;
  error: string | null;
}

const initialState: getAllFeeScheduleState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllFeeSchedule = createAsyncThunk(
  "GetAllFeeSchedule",
  async ({
    codeType = "",
    page = 0,
    pageSize = 10,
    searchString = "",
    filter,
  }: {
    codeType?: string;
    page?: number;
    pageSize?: number;
    searchString?: string;
    filter?: boolean;
  } = {}) => {
    try {
      const response: ResponseArrayContentEntity<ProcedureCode> =
        await feeScheduleService.getAllFeeSchedule(
          codeType,
          page,
          pageSize,
          searchString,
          filter
        );
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      throw new Error("Failed to get all fee schedules");
    }
  }
);

const getAllFeeScheduleReducerSlice = createSlice({
  name: "GetAllFeeSchedule",
  initialState,
  reducers: {
    resetFeeScheduleAction: (state) => {
      state.data = null;
      state.status = apiStatus.IDLE;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllFeeSchedule.pending, (state) => {
        state.status = apiStatus.LOADING;
      })
      .addCase(getAllFeeSchedule.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllFeeSchedule.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllFeeScheduleReducer = getAllFeeScheduleReducerSlice.reducer;
export default GetAllFeeScheduleReducer;
export const getAllFeeScheduleReducerAction =
  getAllFeeScheduleReducerSlice.actions;
