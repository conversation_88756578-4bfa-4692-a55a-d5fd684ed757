import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseContentEntity } from "src/models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import preAuthorizationService from "../../../services/auth/pre-authorization-service/pre-authorization-service";

// Pre-authorization data interface
export interface PreAuthorizationData {
  uuid?: string;
  authNumber: string;
  preAuthorizationDate: string;
  clientInsuranceId: string;
  clinicianIds: string[];
  feeScheduleIds: string[];
  visitsUsed: number;
  visitsAllowed: number;
  startDate: string;
  endDate: string;
  notes: string;
  dashboardWarnings: boolean;
  spokeTo: string;
  allClinician: boolean;
  allCptCodes: boolean;
  clientId: string;
}

export interface CreatePreAuthorizationState {
  data: PreAuthorizationData | null;
  status: string;
  error: string | null;
}

const initialState: CreatePreAuthorizationState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const createPreAuthorization = createAsyncThunk(
  "CreatePreAuthorization",
  async (preAuthData: PreAuthorizationData) => {
    try {
      const response: ResponseContentEntity<PreAuthorizationData> =
        await preAuthorizationService.createPreAuthorization(preAuthData);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(err.data?.message ?? "Failed to create pre-authorization");
    }
  }
);

const createPreAuthorizationReducerSlice = createSlice({
  name: "CreatePreAuthorization",
  initialState,
  reducers: {
    resetCreatePreAuthorization: () => initialState,
    clearCreatePreAuthError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createPreAuthorization.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.error = null;
      })
      .addCase(createPreAuthorization.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload as PreAuthorizationData;
      })
      .addCase(createPreAuthorization.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const CreatePreAuthorizationReducer = createPreAuthorizationReducerSlice.reducer;
export default CreatePreAuthorizationReducer;
export const createPreAuthorizationAction = createPreAuthorizationReducerSlice.actions;
