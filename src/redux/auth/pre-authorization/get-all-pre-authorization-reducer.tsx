import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ErrorResponseEntity } from "src/models/error-response";
import { ResponseArrayContentEntity } from "../../../models/response-content-entity";
import { apiStatus } from "../../../models/apiStatus";
import preAuthorizationService from "../../../services/auth/pre-authorization-service/pre-authorization-service";

// Pre-authorization item interface
export interface PreAuthorizationItem {
  uuid: string;
  authNumber: string;
  preAuthorizationDate: string;
  clientInsuranceId: string;
  clinicianIds: string[];
  feeScheduleIds: string[];
  visitsUsed: number;
  visitsAllowed: number;
  startDate: string;
  endDate: string;
  notes: string;
  dashboardWarnings: boolean;
  spokeTo: string;
  allClinician: boolean;
  allCptCodes: boolean;
  clientId: string;
  // Add any additional fields that come from the API
  insuranceName?: string;
  clinicianNames?: string[];
  procedureCodes?: string[];
  status?: string;
  createdDate?: string;
}

// Pagination payload interface
export interface GetAllPreAuthPayload {
  clientId: string;
  page: number;
  pageSize: number;
}

export interface GetAllPreAuthorizationState {
  data: {
    content: PreAuthorizationItem[];
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    first: boolean;
    last: boolean;
  } | null;
  status: string;
  error: string | null;
}

const initialState: GetAllPreAuthorizationState = {
  data: null,
  status: apiStatus.IDLE,
  error: null,
};

export const getAllPreAuthorizations = createAsyncThunk(
  "GetAllPreAuthorizations",
  async (payload: GetAllPreAuthPayload) => {
    try {
      const response: ResponseArrayContentEntity<any> =
        await preAuthorizationService.getAllPreAuthorizations(payload);
      return response.data;
    } catch (error: unknown) {
      if ((error as ErrorResponseEntity)?.body?.message) {
        throw new Error((error as ErrorResponseEntity).body.message);
      }
      const err = error as ErrorResponseEntity & {
        data?: { message?: string };
      };
      throw new Error(
        err.data?.message ?? "Failed to fetch pre-authorizations"
      );
    }
  }
);

const getAllPreAuthorizationReducerSlice = createSlice({
  name: "GetAllPreAuthorizations",
  initialState,
  reducers: {
    resetGetAllPreAuthorizations: () => initialState,
    clearGetAllPreAuthError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllPreAuthorizations.pending, (state) => {
        state.status = apiStatus.LOADING;
        state.error = null;
      })
      .addCase(getAllPreAuthorizations.fulfilled, (state, action) => {
        state.status = apiStatus.SUCCEEDED;
        state.data = action.payload;
      })
      .addCase(getAllPreAuthorizations.rejected, (state, action) => {
        state.status = apiStatus.FAILED;
        state.data = null;
        state.error = action.error.message ?? "An error occurred";
      });
  },
});

const GetAllPreAuthorizationReducer =
  getAllPreAuthorizationReducerSlice.reducer;
export default GetAllPreAuthorizationReducer;
export const getAllPreAuthorizationAction =
  getAllPreAuthorizationReducerSlice.actions;
