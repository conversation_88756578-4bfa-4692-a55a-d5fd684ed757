import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import AddAvailabilityReducer from "./auth/availability/add-availability-reducer";
import GetAvailabilityByClinicianIdReducer from "./auth/availability/get-availability-by-clinician-id";
import AddFeeScheduleReducer from "./auth/fee-schedule/add-fee-schedule-reducer";
import ArchiveFeeScheduleReducer from "./auth/fee-schedule/archieve-fee-schedule";
import EditFeeScheduleReducer from "./auth/fee-schedule/edit-fee-schedule-reducer";
import EditFeeScheduleStatusReducer from "./auth/fee-schedule/edit-fee-schedule-status";
import GetAllFeeScheduleReducer from "./auth/fee-schedule/get-all-fee-schedule-reducer";
import GetAllProcedureCodesReducer from "./auth/fee-schedule/get-all-procedure-codes-reducer";
import GetFeeScheduleByIdReducer from "./auth/fee-schedule/get-fee-schedule-by-id-reducer";
import getEmailByUuidReducer from "./auth/get-user-by-uuid";
import loaderReducer from "./auth/loaderReducer";
import loginReducer from "./auth/loginReducer";
import NewLoginReducer from "./auth/new-login-reducer";
import addStickyNotesReducer from "./auth/patient/add-new-sticky-note-reducer";
import AddPatientReducer from "./auth/patient/add-patient-reducer";
import ChangePrimaryClinicianReducer from "./auth/patient/change-primary-clinician";
import GetAllPatientsReducer from "./auth/patient/get-all-patients-reducer";
import GetPatientByIdReducer from "./auth/patient/get-patient-by-id";
import getStickyNotesReducer from "./auth/patient/get-sticky-notes-reducer";
import AddClinicianReducer from "./auth/profile/add-clinician-reducer";
import AddClinicianSignatureReducer from "./auth/profile/add-clinician-signature";
import AddContactsReducer from "./auth/profile/add-contacts-reducer";
import AddPracticeLocationReducer from "./auth/profile/add-practice-location-reducer";
import AddStaffReducer from "./auth/profile/add-staff-reducer";
import ArchiveClinicianReducer from "./auth/profile/archive-clinician-reducer";
import ArchiveContactReducer from "./auth/profile/archive-contact-reducer";
import ArchiveLocationReducer from "./auth/profile/archive-location-reducer";
import ChangeUserPasswordReducer from "./auth/profile/change-user-password";
import EditClinicianReducer from "./auth/profile/edit-clinician-reducer";
import EditContactsReducer from "./auth/profile/edit-contacts-reducer";
import EditLocationReducer from "./auth/profile/edit-location-reducer";
import EditLocationStatusReducer from "./auth/profile/edit-location-status-reduxer";
import EditPracticeReducer from "./auth/profile/edit-practice-reducer";
import EditRolesAndPermissionsReducer from "./auth/profile/edit-roles-and-permissions-reducer";
import EditStaffReducer from "./auth/profile/edit-staff-reducer";
import EditStaffStatusReducer from "./auth/profile/edit-staff-status-reducer";
import GetAllCliniciansReducer from "./auth/profile/get-all-clinicians";
import GetAllContactsReducer from "./auth/profile/get-all-contacts-reducer";
import GetAllRolesAndPermissionsReducer from "./auth/profile/get-all-roles-and-permissions-reducer";
import GetAllRolesReducer from "./auth/profile/get-all-roles-reducer";
import GetAllStaffReducer from "./auth/profile/get-all-staff-reducer";
import GetAllAmericanStatesReducer from "./auth/profile/get-all-states-reducer";
import GetAllSupervisingCliniciansReducer from "./auth/profile/get-all-supervising-clinicians";
import GetAllWorkLocationClinicianReducer from "./auth/profile/get-all-worklocation-clinicians";
import GetClinicianByIdReducer from "./auth/profile/get-clinician-by-id-reducer";
import GetLocationByIdReducer from "./auth/profile/get-location-by-id-reducer";
import GetAllLocationDetailsReducer from "./auth/profile/get-location-details";
import GetAllPracticeDetailsReducer from "./auth/profile/get-profile-reducer";
import GetUserProfileByIdReducer from "./auth/profile/get-user-by-id";
import SendVerifiationOtpReducer from "./auth/send-verification-otp-reducer";
import SetPasswordReducer from "./auth/set-password-reducer";
import snackbarReducer from "./auth/snackbarReducer";
import VerifyUserOtpReducer from "./auth/verify-user-otp.reducer";
import verifyLinkReducer from "./auth/verifyLinkReducer";
import intervalsReducer from "./intervals/intervalsReducer";
import ChangePortalAccessReducer from "./auth/patient/change-patient-portal-access";
import ChangeTwoFactorAuthenticationReducer from "./auth/patient/allow-two-factor-authentication-patient-reducer";
import ChangeTwoFactorAuthenticationClinicianReducer from "./auth/profile/allow-two-factor-authentication-clinician";
import ResendInviteClientClinicianReducer from "./auth/profile/resend-invitation-client-clinician-reducer";
import EditPatientReducer from "./auth/patient/edit-patient-reducer";
import GetAllGroupSettingsReducer from "./auth/group-settings/get-all-group-settings";
import AddGroupSettingsReducer from "./auth/group-settings/add-group-settings";
import EditGroupSettingsReducer from "./auth/group-settings/edit-group-settings";
import GetGroupSettingsByIdReducer from "./auth/group-settings/get-group-setting-by-id";
import DeleteGroupSettingsReducer from "./auth/group-settings/delete-group-setting";
const store = configureStore({
  reducer: {
    loginReducer: loginReducer,
    snackbarReducer: snackbarReducer,
    loaderReducer: loaderReducer,
    verifyLinkReducer: verifyLinkReducer,
    intervalsReducer: intervalsReducer,
    GetAllPracticeDetailsReducer: GetAllPracticeDetailsReducer,
    EditPracticeReducer: EditPracticeReducer,
    GetAllLocationDetailsReducer: GetAllLocationDetailsReducer,
    GetAllAmericanStatesReducer: GetAllAmericanStatesReducer,
    AddPracticeLocationReducer: AddPracticeLocationReducer,
    EditLocationReducer: EditLocationReducer,
    EditLocationStatusReducer: EditLocationStatusReducer,
    GetAllStaffReducer: GetAllStaffReducer,
    EditStaffReducer: EditStaffReducer,
    AddStaffReducer: AddStaffReducer,
    EditStaffStatusReducer: EditStaffStatusReducer,
    GetAllContactsReducer: GetAllContactsReducer,
    AddContactsReducer: AddContactsReducer,
    EditContactsReducer: EditContactsReducer,
    GetAllCliniciansReducer: GetAllCliniciansReducer,
    GetAllWorkLocationClinicianReducer: GetAllWorkLocationClinicianReducer,
    GetAllSupervisingCliniciansReducer: GetAllSupervisingCliniciansReducer,
    AddClinicianReducer: AddClinicianReducer,
    EditClinicianReducer: EditClinicianReducer,
    GetClinicianByIdReducer: GetClinicianByIdReducer,
    GetLocationByIdReducer: GetLocationByIdReducer,
    GetAllRolesReducer: GetAllRolesReducer,
    GetAllRolesAndPermissionsReducer: GetAllRolesAndPermissionsReducer,
    EditRolesAndPermissionsReducer: EditRolesAndPermissionsReducer,
    GetAllFeeScheduleReducer: GetAllFeeScheduleReducer,
    AddFeeScheduleReducer: AddFeeScheduleReducer,
    EditFeeScheduleReducer: EditFeeScheduleReducer,
    GetFeeScheduleByIdReducer: GetFeeScheduleByIdReducer,
    GetAllProcedureCodesReducer: GetAllProcedureCodesReducer,
    AddPatientReducer: AddPatientReducer,
    GetAllPatientsReducer: GetAllPatientsReducer,
    EditFeeScheduleStatusReducer: EditFeeScheduleStatusReducer,
    AddAvailabilityReducer: AddAvailabilityReducer,
    GetAvailabilityByClinicianIdReducer: GetAvailabilityByClinicianIdReducer,
    GetPatientByIdReducer: GetPatientByIdReducer,
    GetUserProfileByIdReducer: GetUserProfileByIdReducer,
    NewLoginReducer: NewLoginReducer,
    SetPasswordReducer: SetPasswordReducer,
    SendVerifiationOtpReducer: SendVerifiationOtpReducer,
    VerifyUserOtpReducer: VerifyUserOtpReducer,
    AddClinicianSignatureReducer: AddClinicianSignatureReducer,
    ArchiveLocationReducer: ArchiveLocationReducer,
    ArchiveClinicianReducer: ArchiveClinicianReducer,
    ArchiveContactReducer: ArchiveContactReducer,
    ChangeUserPasswordReducer: ChangeUserPasswordReducer,
    ArchiveFeeScheduleReducer: ArchiveFeeScheduleReducer,
    ChangePrimaryClinicianReducer: ChangePrimaryClinicianReducer,
    getEmailByUuidReducer: getEmailByUuidReducer,
    getStickyNotesReducer: getStickyNotesReducer,
    addStickyNotesReducer: addStickyNotesReducer,
    ChangePortalAccessReducer: ChangePortalAccessReducer,
    ChangeTwoFactorAuthenticationReducer: ChangeTwoFactorAuthenticationReducer,
    ChangeTwoFactorAuthenticationClinicianReducer:
      ChangeTwoFactorAuthenticationClinicianReducer,
    ResendInviteClientClinicianReducer: ResendInviteClientClinicianReducer,
    EditPatientReducer: EditPatientReducer,
    GetAllGroupSettingsReducer: GetAllGroupSettingsReducer,
    AddGroupSettingsReducer: AddGroupSettingsReducer,
    EditGroupSettingsReducer: EditGroupSettingsReducer,
    GetGroupSettingsByIdReducer: GetGroupSettingsByIdReducer,
    DeleteGroupSettingsReducer: DeleteGroupSettingsReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useReduxDispatch = useDispatch<typeof store.dispatch>;
export const useReduxSelector = useSelector<RootState>;

export default store;
