import { ResponseContentEntity } from "src/models/response-content-entity";
import { ResponseArrayContentEntity } from "../../../models/response-content-entity";
import axiosInstance from "../../../interceptor/interceptor";
import { apiPath } from "../../../constants/apiPath";
import { PreAuthorizationData } from "../../../redux/auth/pre-authorization/create-pre-authorization-reducer";
import { GetAllPreAuthPayload } from "../../../redux/auth/pre-authorization/get-all-pre-authorization-reducer";

class PreAuthorizationService {
  createPreAuthorization = (
    preAuthData: PreAuthorizationData
  ): Promise<ResponseContentEntity<PreAuthorizationData>> => {
    console.log("API URL:", `${apiPath.PRE_AUTHORIZATION}`);
    console.log("Payload being sent:", preAuthData);
    return axiosInstance.post(`${apiPath.PRE_AUTHORIZATION}`, preAuthData);
  };

  updatePreAuthorization = (
    preAuthData: PreAuthorizationData
  ): Promise<ResponseContentEntity<PreAuthorizationData>> => {
    console.log("API URL:", `${apiPath.PRE_AUTHORIZATION}`);
    console.log("Update payload being sent:", preAuthData);
    return axiosInstance.put(`${apiPath.PRE_AUTHORIZATION}`, preAuthData);
  };

  getAllPreAuthorizations = (
    payload: GetAllPreAuthPayload
  ): Promise<ResponseArrayContentEntity<any>> => {
    const url = `${apiPath.PRE_AUTHORIZATION}/all/${payload.clientId}?page=${payload.page}&pageSize=${payload.pageSize}`;
    console.log("Get All Pre-Auth API URL:", url);
    return axiosInstance.get(url);
  };
}

const preAuthorizationService = new PreAuthorizationService();
Object.freeze(preAuthorizationService);

export default preAuthorizationService;
