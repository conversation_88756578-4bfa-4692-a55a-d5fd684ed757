/* eslint-disable @typescript-eslint/no-explicit-any */
import { AxiosResponse } from "axios";
import { apiPath } from "../../constants/apiPath";
import axiosInstance from "../../interceptor/interceptor";
import { LoginResponse } from "../../models/loginModel";
import { ResponseContentEntity } from "../../models/response-content-entity";
import { ResetLinkType } from "../../models/reset-linktype";

class AuthService {
  login = (payload: {
    email: string;
    password: string;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.post(apiPath.LOGIN, payload);
  };

  logout = (): Promise<AxiosResponse<null>> => {
    return axiosInstance.post(apiPath.LOGOUT_USER);
  };

  verifyPasswordLink = (payload: {
    linkId: string;
    linkType: string;
  }): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.get(
      `${payload.linkType === ResetLinkType.RESET ? apiPath.VERIFY_LINK_RESET : apiPath.VERIFY_LINK_SET}/${payload.linkId}`
    );
  };

  newPasswordLink = (payload: {
    otpId: string;
    otp: string;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.post(apiPath.ADD_NEW_LOGIN, payload);
  };

  setPasswordLink = (payload: {
    newPassword: string;
    otpId: string;
    email: string;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.post(apiPath.SET_PASSWORD, payload);
  };

  sendVerificationOtp = (payload: {
    email: string;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.post(apiPath.SEND_VERIFICATION_OTP, payload);
  };

  verifyUserOtp = (payload: {
    otpId: string;
    otp: string;
    fromForgotPassword?: boolean;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.post(
      `${apiPath.VERIFY_USER_OTP}?fromForgotPassword=${payload.fromForgotPassword}`,
      payload
    );
  };

  getEmailByUuid = (payload: {
    uuid: string;
  }): Promise<AxiosResponse<LoginResponse>> => {
    return axiosInstance.get(apiPath.GET_EMAIL_BY_UUID, {
      params: { uuid: payload.uuid },
    });
  };
}

const authService = new AuthService();

Object.freeze(authService);

export default authService;
