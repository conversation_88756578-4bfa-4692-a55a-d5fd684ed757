import { ProcedureCode } from "src/models/all-const";
import { apiPath } from "../../../constants/apiPath";
import axiosInstance from "../../../interceptor/interceptor";
import {
  ResponseArrayContentEntity,
  ResponseContentEntity,
} from "../../../models/response-content-entity";
class FeeScheduleService {
  getAllFeeSchedule = (
    codeType: string,
    page: number = 0,
    pageSize: number = 10,
    searchString: string = "",
    filter?: boolean
  ): Promise<ResponseArrayContentEntity<ProcedureCode>> => {
    let queryParams = `page=${page}&pageSize=${pageSize}`;

    if (searchString) {
      queryParams += `&searchString=${searchString}`;
    }

    if (filter !== undefined) {
      queryParams += `&active=${filter}`;
    }

    if (codeType === "ARCHIVE") {
      queryParams += `&archive=true`;
    } else if (codeType && codeType !== "") {
      queryParams += `&codeType=${codeType}`;
    }

    return axiosInstance.get(
      `${apiPath.GET_ALL_FEE_SCHEDULE}/get-all?${queryParams}`
    );
  };
  addFeeSchedule = (
    payload: ProcedureCode
  ): Promise<ResponseContentEntity<ProcedureCode>> => {
    return axiosInstance.post(`${apiPath.ADD_FEE_SCHEDULE}`, payload);
  };
  updateFeeSchedule = (
    payload: ProcedureCode
  ): Promise<ResponseContentEntity<ProcedureCode>> => {
    return axiosInstance.put(`${apiPath.UPDATE_FEE_SCHEDULE}`, payload);
  };
  getFeeScheduleById = (
    id: string
  ): Promise<ResponseContentEntity<ProcedureCode>> => {
    return axiosInstance.get(`${apiPath.GET_FEE_SCHEDULE_BY_ID}/${id}`);
  };

  getAllProcedureCodes = (): Promise<
    ResponseArrayContentEntity<ProcedureCode>
  > => {
    return axiosInstance.get(`${apiPath.GET_ALL_PROCEDURE_CODES}`);
  };

  editFeeScheduleStatus = (
    feeScheduleId: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.UPDATE_FEE_SCHEDULE_STATUS}/${feeScheduleId}?flag=${flag}`
    );
  };
  archiveFeeSchedule = (
    feeScheduleId: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.ARCHIVE_FEE_SCHEDULE}/${feeScheduleId}?flag=${flag}`
    );
  };
}
const feeScheduleService = new FeeScheduleService();
Object.freeze(feeScheduleService);

export default feeScheduleService;
