import { apiPath } from "../../../constants/apiPath";
import axiosInstance from "../../../interceptor/interceptor";
import {
  AllTypes,
  ClinicianPayload,
  ContactPayload,
  LocationPayload,
  PermissionGroup,
  ProfilePayload,
  RolesAndPermissionsData,
  StaffPayload,
  USState,
} from "../../../models/all-const";
import {
  ClinicInfo,
  LocationInfo,
  PatientTypes,
} from "../../../models/providerGroup";
import {
  ResponseArrayContentEntity,
  ResponseContentEntity,
} from "../../../models/response-content-entity";

export interface ChangeTwoFactorAuthenticationPayload {
  clinicianUuid: string;
  flag: boolean;
}

class PracticeProfileService {
  getPracticeDetails = (): Promise<
    ResponseArrayContentEntity<ProfilePayload>
  > => {
    return axiosInstance.get(`${apiPath.GET_PRACTICE_PROFILE}`);
  };

  getLocationDetails = (payload: {
    size: number;
    page: number;
    filter?: boolean;
    searchString: string;
    archive?: boolean;
  }): Promise<ResponseArrayContentEntity<LocationPayload>> => {
    let queryParams = `?pageSize=${payload.size}&page=${payload.page}&searchString=${payload?.searchString}`;

    if (payload.hasOwnProperty("filter")) {
      queryParams += `&active=${payload.filter}`;
    }

    if (payload.hasOwnProperty("archive")) {
      queryParams += `&archive=${payload.archive}`;
    }

    return axiosInstance.get(`${apiPath.GET_LOCATION_DETAILS}${queryParams}`);
  };
  editPracticeDetails = (
    payload: ClinicInfo
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.UPDATE_PRACTICE_PROFILE}`, payload);
  };
  getAllStates = (): Promise<ResponseArrayContentEntity<USState>> => {
    return axiosInstance.get(`${apiPath.GET_ALL_STATES}`);
  };
  addLocation = (
    payload: LocationInfo
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.post(`${apiPath.ADD_LOCATION}`, payload);
  };
  editLocation = (
    payload: LocationInfo
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.EDIT_LOCATION}`, payload);
  };
  editLocationStatus = (
    locationId: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.EDIT_LOCATION_STATUS}/${locationId}?flag=${flag}`
    );
  };
  getAllStaff = (payload: {
    size: number;
    page: number;
    searchString: string;
  }): Promise<ResponseArrayContentEntity<StaffPayload>> => {
    return axiosInstance.get(
      `${apiPath.GET_ALL_STAFF}?pageSize=${payload.size}&page=${payload.page}`
    );
  };
  addStaff = (payload: AllTypes): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.post(`${apiPath.ADD_STAFF}`, payload);
  };
  editStaff = (payload: PatientTypes): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.EDIT_STAFF}`, payload);
  };
  editStaffStatus = (
    staffId: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.EDIT_STAFF_STATUS}/${staffId}?flag=${flag}`
    );
  };
  getAllContacts = (payload: {
    size: number;
    page: number;
    searchString: string;
    filter?: boolean;
    archive?: boolean;
  }): Promise<ResponseArrayContentEntity<ContactPayload>> => {
    let queryParams = `?pageSize=${payload.size}&page=${payload.page}&searchString=${payload?.searchString}`;

    if (payload.hasOwnProperty("filter")) {
      queryParams += `&active=${payload.filter}`;
    }

    if (payload.hasOwnProperty("archive")) {
      queryParams += `&archive=${payload.archive}`;
    }

    return axiosInstance.get(`${apiPath.GET_ALL_CONTACTS}${queryParams}`);
  };
  addContact = (payload: AllTypes): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.post(`${apiPath.ADD_CONTACT}`, payload);
  };
  editContact = (
    payload: PatientTypes
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.EDIT_CONTACT}`, payload);
  };
  getAllClinicians = (payload: {
    size: number;
    page: number;
    filter?: boolean;
    searchString: string;
    archive?: boolean;
  }): Promise<ResponseArrayContentEntity<ClinicianPayload>> => {
    let queryParams = `?pageSize=${payload.size}&page=${payload.page}&searchString=${payload?.searchString}`;

    if (payload.hasOwnProperty("filter")) {
      queryParams += `&active=${payload.filter}`;
    }

    if (payload.hasOwnProperty("archive")) {
      queryParams += `&archive=${payload.archive}`;
    }

    return axiosInstance.get(
      `${apiPath.GET_ALL_CLINICIAN_DETAILS}${queryParams}`
    );
  };
  getAllWorkLocationClinician = (): Promise<{
    data: Record<string, string>;
    message: string | null;
    code: string;
    path: string;
    requestId: string;
    version: string;
  }> => {
    return axiosInstance.get(`${apiPath.GET_ALL_WORK_LOCATION_CLINICIAN}`);
  };
  addClinician = (
    payload: ClinicianPayload
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.post(`${apiPath.ADD_CLINICIAN}`, payload);
  };
  editClinician = (
    payload: PatientTypes
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.EDIT_CLINICIAN}`, payload);
  };
  editClinicianStatus = (
    clinicianId: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.EDIT_CLINICIAN_STATUS}/${clinicianId}?flag=${flag}`
    );
  };
  getAllSupervisorClinician = (): Promise<{
    data: Record<string, string>;
    message: string | null;
    code: string;
    path: string;
    requestId: string;
    version: string;
  }> => {
    return axiosInstance.get(`${apiPath.GET_ALL_SUPERVISOR_CLINICIAN}`);
  };
  getClinicianById = (
    clinicianId: string
  ): Promise<ResponseContentEntity<ClinicianPayload>> => {
    return axiosInstance.get(`${apiPath.GET_CLINICIAN_BY_ID}/${clinicianId}`);
  };
  getLocationById = (
    locationId: string
  ): Promise<ResponseContentEntity<LocationPayload>> => {
    return axiosInstance.get(
      `${apiPath.GET_LOCATION_DETAILS_BY_ID}/${locationId}`
    );
  };
  getAllPermissions = (): Promise<
    ResponseArrayContentEntity<PermissionGroup>
  > => {
    return axiosInstance.get(`${apiPath.GET_ALL_PERMISSIONS}`);
  };
  getAllRolesAndPermissions = (): Promise<
    ResponseArrayContentEntity<RolesAndPermissionsData>
  > => {
    return axiosInstance.get(`${apiPath.GET_ALL_ROLES_AND_PERMISSIONS}`);
  };
  editRolesAndPermissions = (
    clinicianId: string,
    roleName: string,
    flag: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.EDIT_ROLES_AND_PERMISSIONS}/${clinicianId}?roleName=${roleName}&flag=${flag}`
    );
  };
  getUserById = (): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.get(`${apiPath.GET_USER_PROFILE}`);
  };

  addUserSignature = (
    clinicianId: string,
    base64: string
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.ADD_CLINICIAN_SIGNATURE}/${clinicianId}/signature`,
      { base64 }
    );
  };
  archiveLocation = (
    locationId: string,
    archiveStatus: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.ARCHIVE_LOCATION}/${locationId}?flag=${archiveStatus}`
    );
  };
  archiveClinician = (
    clinicianId: string,
    archiveStatus: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.ARCHIVE_CLINICIAN}/${clinicianId}?flag=${archiveStatus}`
    );
  };
  archiveContact = (
    contactId: string,
    archiveStatus: boolean
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(
      `${apiPath.ARCHIVE_CONTACT}/${contactId}?flag=${archiveStatus}`
    );
  };
  changeUserPassword = (payload: {
    newPassword: string;
  }): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.post(`${apiPath.CHANGE_USER_PASSWORD}`, payload);
  };
  changeTwoFactorAuthentication = (
    payload: ChangeTwoFactorAuthenticationPayload
  ): Promise<ResponseContentEntity<ChangeTwoFactorAuthenticationPayload>> => {
    return axiosInstance.put(
      `${apiPath.CHANGE_TWO_FACTOR_AUTHENTICATION_CLINICIAN_PROFILE}/${payload.clinicianUuid}?flag=${payload.flag}`
    );
  };
  resendInviteClient = (
    uuid: string,
    isClient: boolean
  ): Promise<ResponseContentEntity<string>> => {
    return axiosInstance.post(
      `${apiPath.RESEND_INVITE_CLIENT}/${uuid}?isClient=${isClient}`
    );
  };
  editPatientProfile = (
    payload: PatientTypes
  ): Promise<ResponseContentEntity<null>> => {
    return axiosInstance.put(`${apiPath.EDIT_PATIENT_PROFILE}`, payload);
  };
}
const practiceProfileService = new PracticeProfileService();
Object.freeze(practiceProfileService);

export default practiceProfileService;
