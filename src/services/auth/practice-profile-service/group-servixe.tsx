import { ResponseArrayContentEntity } from "../../../models/response-content-entity";
import { apiPath } from "../../../constants/apiPath";
// import { GroupSetting } from "../../../models/all-const";
import axiosInstance from "../../../interceptor/interceptor";
import { ProviderGroupResponse } from "../../../models/providerGroup";
class GroupService {
  getAllGroupSettings = (payload: {
    size: number;
    page: number;
    searchString: string;
  }): Promise<ResponseArrayContentEntity<ProviderGroupResponse>> => {
    return axiosInstance.get(
      `${apiPath.GET_ALL_GROUP_SETTINGS}?pageSize=${payload.size}&page=${payload.page}&searchString=${payload.searchString}`
    );
  };
  addGroupSettings = (payload: {
    groupName: string;
    groupInitials: string;
    cptCode: string;
    clientUuids: string[];
    clinicianUUID: string;
    familyGroup: boolean;
    billTo: string;
  }): Promise<ResponseArrayContentEntity<ProviderGroupResponse>> => {
    return axiosInstance.post(apiPath.GET_ALL_GROUP_SETTINGS, payload);
  };
  editGroupSettings = (payload: {
    uuid?: string;
    groupName: string;
    groupInitials: string;
    cptCode: string;
    clientUuids: string[];
    clinicianUUID: string;
    familyGroup?: boolean;
    billTo?: string;
    isFamilyGroup?: boolean;
    isGroup?: boolean;
  }): Promise<ResponseArrayContentEntity<ProviderGroupResponse>> => {
    return axiosInstance.put(apiPath.GET_ALL_GROUP_SETTINGS, payload);
  };
  getGroupSettingsById = (payload: {
    groupId: string;
  }): Promise<ResponseArrayContentEntity<ProviderGroupResponse>> => {
    return axiosInstance.get(
      `${apiPath.GET_ALL_GROUP_SETTINGS}/${payload.groupId}`
    );
  };
  deleteGroupSettings = (payload: {
    groupId: string;
  }): Promise<ResponseArrayContentEntity<ProviderGroupResponse>> => {
    return axiosInstance.delete(
      `${apiPath.GET_ALL_GROUP_SETTINGS}/${payload.groupId}`
    );
  };
}
const groupService = new GroupService();
Object.freeze(groupService);

export default groupService;
