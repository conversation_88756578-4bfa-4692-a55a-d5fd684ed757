import { StickyNotePayload } from "../../../redux/auth/patient/add-new-sticky-note-reducer";
import { apiPath } from "../../../constants/apiPath";
import axiosInstance from "../../../interceptor/interceptor";
import {
  ResponseArrayContentEntity,
  ResponseContentEntity,
} from "../../../models/response-content-entity";
import { ChangePortalAccessPayload } from "../../../redux/auth/patient/change-patient-portal-access";
import { ChangeTwoFactorAuthenticationPayload } from "../../../redux/auth/patient/allow-two-factor-authentication-patient-reducer";

class PatientService {
  addPatient = (payload: any): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.post(apiPath.ADD_PATIENT, payload);
  };
  getAllPatients = (payload: any): Promise<ResponseArrayContentEntity<any>> => {
    return axiosInstance.get(
      `${apiPath.GET_ALL_PATIENTS}/all?` +
        `page=${payload.page}&` +
        `pageSize=${payload.size}&` +
        `searchString=${payload.searchString}&` +
        `clientStatus=${payload.status}&` +
        `paymentMethod=${payload.insurance}&` +
        `clinicianId=${payload.clinicianId}` +
        (payload.filter !== null || payload.filter == "remove"
          ? `&searchType=${payload.filter}`
          : "")
    );
  };

  getPatientById = (payload: any): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.get(`${apiPath.GET_PATIENT_BY_ID}/${payload}`);
  };
  changePrimaryClinician = (
    payload: any
  ): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.put(
      `${apiPath.CHANGE_PRIMARY_CLINICIAN}/${payload.clientUuid}?existingClinicianId=${payload.existingClinicianId}&newClinicianId=${payload.newClinicianId}`
    );
  };
  addStickyNotes = (
    payload: StickyNotePayload
  ): Promise<ResponseContentEntity<StickyNotePayload>> => {
    return axiosInstance.post(`${apiPath.ADD_STICKY_NOTES}`, payload);
  };
  getStickyNotes = (
    clientId: string
  ): Promise<ResponseContentEntity<StickyNotePayload>> => {
    return axiosInstance.get(`${apiPath.ADD_STICKY_NOTES}/${clientId}`);
  };
  changePortalAccess = (
    payload: ChangePortalAccessPayload
  ): Promise<ResponseContentEntity<ChangePortalAccessPayload>> => {
    return axiosInstance.put(
      `${apiPath.CHANGE_PORTAL_ACCESS}/${payload.clientUuid}?flag=${payload.portalAccess}`
    );
  };
  changeTwoFactorAuthentication = (
    payload: ChangeTwoFactorAuthenticationPayload
  ): Promise<ResponseContentEntity<ChangeTwoFactorAuthenticationPayload>> => {
    return axiosInstance.put(
      `${apiPath.CHANGE_TWO_FACTOR_AUTHENTICATION_PATIENT_PROFILE}/${payload.clientUuid}?flag=${payload.flag}`
    );
  };
  getPatientByUuid = (payload: any): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.get(`${apiPath.GET_PATIENT_BY_UUID}/${payload}`);
  };
  getClientInsurance = (
    clientId: string
  ): Promise<ResponseArrayContentEntity<any>> => {
    return axiosInstance.get(`${apiPath.GET_CLIENT_INSURANCE}/${clientId}`);
  };
  updateClientInsurance = (
    _clientId: string,
    insuranceData: any
  ): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.put(`${apiPath.GET_CLIENT_INSURANCE}`, insuranceData);
  };
}
const patientService = new PatientService();
Object.freeze(patientService);

export default patientService;
