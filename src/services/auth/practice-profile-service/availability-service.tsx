import { apiPath } from "../../../constants/apiPath";

import { ResponseContentEntity } from "../../../models/response-content-entity";

import axiosInstance from "../../../interceptor/interceptor";

class AvailabilityService {
  addAvailabiityService = (
    payload: any
  ): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.post(`${apiPath.ADD_AVAILABILITY}`, payload);
  };

  getAvailabilityById = (id: string): Promise<ResponseContentEntity<any>> => {
    return axiosInstance.get(
      `${apiPath.GET_AVAILABILITY}/${id}`
    );
  };
}
const availabilityService = new AvailabilityService();
Object.freeze(availabilityService);

export default availabilityService;
