# Name of workflow as seen in Github actions tab
name: Practice Easily EHR CI Deployment
# Run workflow only on push to dev branch
on:
  push:
    branches:
      - dev
jobs:
  # To build the project
  build-practice-easily-fe:
    runs-on: ubuntu-latest
    steps:
      - name: Chat Setup
        uses: Co-qn/google-chat-notification@v1
        with:
          name: Practice Easily EMR Frontend Dev Deployment Started
          url: ${{ secrets.GOOGLE_CHAT_WEBHOOK }}
          status: ${{ job.status }}

      - name: Checking out code
        uses: actions/checkout@v3

      - name: Installing Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Installing dependencies
        run: npm install --legacy-peer-deps

      - name: Building Provider, Patient portals
        run: npm run build

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION_DEV }}

      - name: Deploy to S3 bucket
        run: aws s3 sync ./dist/ s3://${{ secrets.AWS_S3_BUCKET_DEV }} --delete

      - name: Invalidate CloudFront
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.AWS_CF_DISTRIBUTION_DEV }}
          PATHS: "/*"
          AWS_REGION: ${{ secrets.AWS_REGION_DEV }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Practice Easily EMR Frontend Dev Deployment Status
        uses: Co-qn/google-chat-notification@releases/v1
        with:
          name: Build
          url: ${{ secrets.GOOGLE_CHAT_WEBHOOK }}
          status: ${{ job.status }}
        if: always()
