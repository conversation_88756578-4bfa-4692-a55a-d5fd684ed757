{
	"compilerOptions": {
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"skipLibCheck": true,

		/* Bundler mode */
		"moduleResolution": "node",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,

		/* Additional options */
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"baseUrl": ".",
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"incremental": true,
		"types": ["vite/client"]
	},
	"include": ["src"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
